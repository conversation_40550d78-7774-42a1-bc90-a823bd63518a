<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.center</groupId>
  <artifactId>knowledge</artifactId>
  <version>${revision}</version>
  <packaging>pom</packaging>
  <modules>
    <module>center-dependencies</module>
    <module>center-framework</module>
    <module>center-server</module>
    <module>center-infrastructure</module>
    <module>center-knowledge</module>
  </modules>

  <properties>
    <revision>1.0.1</revision>
    <!-- Maven 相关 -->
    <java.version>1.8</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
    <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
    <spring.boot.version>2.7.18</spring.boot.version>
    <lombok.version>1.18.30</lombok.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.center</groupId>
        <artifactId>center-dependencies</artifactId>
        <version>${revision}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>


  <!-- 使用 huawei / aliyun 的 Maven 源，提升下载速度 -->
  <repositories>
    <repository>
      <id>huaweicloud</id>
      <name>huawei</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </repository>
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
  </repositories>

</project>