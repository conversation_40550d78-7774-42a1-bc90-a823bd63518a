package com.center.infrastructure.system.biz.tenant.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/10/23 16:56
 */
@Data
public class TenantListResp {
    @Schema(description = "租户ID")
    private Long id;

    @Schema(description = "企业名称")
    private String name;

    @Schema(description = "企业管理员姓名")
    private String username;

    @Schema(description = "企业管理员手机号")
    private String phoneNumber;

    @Schema(description = "状态")
    private CommonStatusEnum status;
}
