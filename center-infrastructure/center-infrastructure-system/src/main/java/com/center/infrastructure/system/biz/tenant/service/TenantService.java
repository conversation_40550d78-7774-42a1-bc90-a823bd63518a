package com.center.infrastructure.system.biz.tenant.service;


import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.tenant.pojo.*;

public interface TenantService {

  void save(TenantCreateReq tenantCreateReq);

  void update(TenantUpdateReq tenantUpdateReq);

  TenantResp get(Long id);

  TenantUpdateResp getTenantUpdateResp(Long id);

  void delete(Long id);

    void activeTenant(Long id);

  void inactiveTenant(Long id);

  PageResult<TenantListResp> getTenantList(TenantPageReq tenantPageReq);
}
