package com.center.infrastructure.system.biz.tenant;

import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.tenant.pojo.*;
import com.center.infrastructure.system.biz.tenant.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "系统基础功能-租户管理")
@RestController
@RequestMapping("/system/tenant")
@Validated
public class TenantController {

  @Resource
  private TenantService tenantService;

  @GetMapping("/get")
  @Operation(summary = "根据租户ID查询租户信息",description = "")
  @Parameter(name = "id",description = "租户ID",example = "1")
  @EnumConvertPoint
  public CommonResult<TenantUpdateResp> get(@RequestParam(name = "id") Long id){
    return CommonResult.success(tenantService.getTenantUpdateResp(id));
  }

  @GetMapping("/getList")
  @Operation(summary = "查询租户列表",description = "")
  public CommonResult<PageResult<TenantListResp>> getTenantList(TenantPageReq tenantPageReq){
    return CommonResult.success(tenantService.getTenantList(tenantPageReq));
  }

  @PostMapping("/create")
  @Operation(summary = "创建租户",description = "创建新的租户（名称不能重复）")
  @Parameter(description = "新的租户基本信息")
  public CommonResult<String> save(@RequestBody @Valid TenantCreateReq tenantCreateReq){
    tenantService.save(tenantCreateReq);
    return CommonResult.successWithMessageOnly("创建租户成功!");
  }

  @PostMapping("/update")
  @Operation(summary = "修改租户基本信息",description = "新的租户名称不能重复")
  @Parameter(description = "租户新的基本信息")
  public CommonResult<String> update(@RequestBody @Valid TenantUpdateReq tenantUpdateReq){
    tenantService.update(tenantUpdateReq);
    return CommonResult.successWithMessageOnly("修改租户成功!");
  }

  @PostMapping("/delete/{id}")
  @Operation(summary = "根据租户ID删除租户",description = "")
  @Parameter(name = "id",description = "租户ID",example = "1")
  public CommonResult<String> delete(@PathVariable Long id){
    tenantService.delete(id);
    return CommonResult.successWithMessageOnly("删除租户成功!");
  }

  @PostMapping("/active/{id}")
  @Operation(summary = "启用租户",description = "")
  @Parameter(name = "id",description = "租户ID",example = "1")
  public CommonResult<String> activeTenant(@PathVariable Long id){
    tenantService.activeTenant(id);
    return CommonResult.successWithMessageOnly("启用租户成功!");
  }

  @PostMapping("/inactive/{id}")
  @Operation(summary = "停用租户",description = "")
  @Parameter(name = "id",description = "租户ID",example = "1")
  public CommonResult<String> inactiveTenant(@PathVariable Long id){
    tenantService.inactiveTenant(id);
    return CommonResult.successWithMessageOnly("停用租户成功!");
  }


}
