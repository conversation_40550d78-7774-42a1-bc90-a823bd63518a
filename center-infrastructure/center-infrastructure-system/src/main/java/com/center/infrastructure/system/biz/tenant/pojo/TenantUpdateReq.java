package com.center.infrastructure.system.biz.tenant.pojo;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import org.hibernate.validator.constraints.Length;

import lombok.Data;

@Data
public class TenantUpdateReq  {

  @Schema(description = "租户ID",requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
  @NotNull(message = "租户ID不能为空")
  private Long id;

  @Schema(description = "企业名称")
  @NotNull(message = "企业名称不能为空")
  private String name;

  @Schema(description = "企业管理员姓名")
  @NotNull(message = "企业管理员姓名不能为空")
  @Length(min = 2, max = 20, message = "企业管理员姓名长度为 2-20 位")
  private String managerName;

  @Schema(description = "显示名",example = "管理员")
  @Length(max = 20,message = "显示名称长度为20个字符以内")
  private String displayName;

  @Schema(description = "企业管理员手机号")
  @Pattern(regexp = "^\\d{11}$",message = "请输入11为数字手机号")
  @NotNull(message = "企业管理员手机号不能为空")
  private String phoneNumber;

  @Schema(description = "联系邮箱")
  @Email(message = "邮箱格式不正确")
  private String email;
}
