package com.center.infrastructure.system.biz.depart;

import cn.hutool.core.lang.tree.Tree;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.depart.pojo.*;
import com.center.infrastructure.system.biz.depart.service.DepartService;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.web.bind.annotation.*;

import java.util.List;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;


import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 部门管理控制器
 */
@Tag(name = "部门管理模块")
@RestController
@RequestMapping("/depart")
@Validated
public class DepartController {

    @Resource
    private DepartService departService;



    @Operation(summary = "获取当前用户租户下部门树")
    @GetMapping("/get_depart_tree")
    public CommonResult<List<Tree<String>>> getDepartTree(){
        return CommonResult.success(departService.getTenantDepartTree());
    }

    @Operation(summary = "获取当前用户所在部门树")
    @GetMapping("/get_user_depart_tree")
    public CommonResult<List<Tree<String>>> getUserDepartTree(){
        return CommonResult.success(departService.getDepartTree());
    }
    /**
     * 创建部门
     *
     * @param req 部门创建请求
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "新增下级部门")
    public CommonResult<String> createDepart(@Valid @RequestBody DepartCreateReq req) {
        departService.createDepart(req);
        return CommonResult.successWithMessageOnly("部门创建成功");
    }

    /**
     * 修改部门名称
     *
     * @param req 部门更新请求
     * @return 更新结果
     */
    @PostMapping("/update")
    @Operation(summary = "修改部门名称")
    public CommonResult<String> updateDepart(@Valid @RequestBody DepartUpdateReq req) {
        departService.updateDepart(req);
        return CommonResult.successWithMessageOnly("部门更新成功");
    }

    /**
     * 删除部门
     *
     * @param id 部门ID
     * @return 删除结果
     */
    @PostMapping("/delete/{id}")
    @Operation(summary = "删除部门")
    public CommonResult<String> deleteDepart(@PathVariable Long id) {
        departService.deleteDepart(id);
        return CommonResult.successWithMessageOnly("部门删除成功");
    }

    /**
     * 获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    @GetMapping("/get/{id}")
    @Operation(summary = "获取部门详情")
    public CommonResult<DepartResp> getDepartById(@PathVariable Long id) {
        return CommonResult.success(departService.getDepartById(id));
    }

    /**
     * 根据上级部门ID获取部门列表
     *
     * @param parentId 上级部门ID
     * @return 部门列表
     */
    @GetMapping("/listByParent/{parentId}")
    @Operation(summary = "获取部门列表根据上级部门ID")
    public CommonResult<List<DepartResp>> getDepartsByParentId(@PathVariable Long parentId) {
        return CommonResult.success(departService.getDepartsByParentId(parentId));
    }

    /**
     * 根据租户ID获取部门列表
     *
     * @param tenantId 租户ID
     * @return 部门列表
     */
    @GetMapping("/listByTenant/{tenantId}")
    @Operation(summary = "获取部门列表根据租户ID")
    public CommonResult<List<DepartResp>> getDepartsByTenantId(@PathVariable Long tenantId) {
        return CommonResult.success(departService.getDepartsByTenantId(tenantId));
    }

    /**
     * 获取部门列表
     *
     * @param departId 部门ID，用于识别特定的部门
     * @param path 路径，用于指定部门的层级或位置
     * @return 返回一个包含部门列表的CommonResult对象，列表中的每个元素都是一个DepartListResp对象
     */
    @Operation(summary = "获取部门列表（最左侧列表）")
    @GetMapping("/list_depart")
    public CommonResult<List<DepartAndKbListResp>> listDepart(Long departId, String path) {
        return CommonResult.success(departService.getDepart(departId, path));
    }


    /**
     * 获取部门列表,省略中间目录
     *
     * @param departId 部门ID，用于识别特定的部门
     * @param path 路径，用于指定部门的层级或位置
     * @return 返回一个包含部门列表的CommonResult对象，列表中的每个元素都是一个DepartListResp对象
     */
    @Operation(summary = "获取部门列表（新增知识库时使用）")
    @GetMapping("/list_depart_kb")
    public CommonResult<List<DepartAndKbListResp>> listDepartKb(Long departId, String path) {
        return CommonResult.success(departService.getDepartKb(departId, path));
    }

}