package com.center.infrastructure.system.biz.depart.service;


import cn.hutool.core.lang.tree.Tree;
import com.center.infrastructure.system.biz.depart.pojo.DepartAndKbListResp;
import com.center.infrastructure.system.biz.depart.pojo.DepartCreateReq;
import com.center.infrastructure.system.biz.depart.pojo.DepartResp;
import com.center.infrastructure.system.biz.depart.pojo.DepartUpdateReq;

import java.util.List;
import java.util.Set;

/**
 * 部门管理服务接口
 */
public interface DepartService {

    /**
     * 获取部门列表
     *
     * @param departId 部门ID
     * @param path     部门路径
     * @return 部门列表
     */
    List<DepartAndKbListResp> getDepart(Long departId, String path);

    /**
     * 获取部门列表，省略中间目录
     *
     * @param departId 部门ID
     * @param path     部门路径
     * @return 部门列表
     */
    List<DepartAndKbListResp> getDepartKb(Long departId, String path);

    /**
     * 创建部门
     *
     * @param req 部门创建请求
     */
    void createDepart(DepartCreateReq req);

    /**
     * 更新部门
     *
     * @param req 部门更新请求
     */
    void updateDepart(DepartUpdateReq req);

    /**
     * 删除部门
     *
     * @param id 部门ID
     */
    void deleteDepart(Long id);

    /**
     * 获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    DepartResp getDepartById(Long id);

    /**
     * 根据部门ID获取部门列表
     *
     * @param parentId 上级部门ID
     * @return 部门列表
     */
    List<DepartResp> getDepartsByParentId(Long parentId);

    /**
     * 根据租户ID获取部门列表
     *
     * @param tenantId 租户ID
     * @return 部门列表
     */
    List<DepartResp> getDepartsByTenantId(Long tenantId);

    /**
     * 判断目标部门是否在用户的权限范围内（自己部门或下级部门）
     *
     * @param userDeptId   当前用户所在的部门ID
     * @param targetDeptId 目标部门ID
     * @return 是否在权限范围内
     */
    boolean checkDepartmentPermission(Long userDeptId, Long targetDeptId);

    /**
     * 获取用户所在部门及其下级部门的ID列表,请注意，只是1级
     *
     * @param userDeptId 当前用户部门ID
     * @return 部门ID列表
     */
    List<Long> getAccessibleDepartmentIds(Long userDeptId);


    List<DepartResp> getDepartmentsByTenantAndParentId(Long tenantId, Long deptId);

    Set<Long> getAccessibleDepartmentIds();

    void loadAllTenantDepartTrees();

    void loadAllDepartTrees();

    List<Tree<String>> getTenantDepartTree();

    List<Tree<String>> getDepartTree();

    List<String> getParentsId(Tree<String> node, boolean includeCurrentNode);

    String getParentsIdKey(Long deptId);
}