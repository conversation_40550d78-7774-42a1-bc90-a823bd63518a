package com.center.infrastructure.system.biz.tenant.service;


import com.center.cache.factory.CacheFactory;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.db.config.SnowFlakeConfig;
import com.center.framework.web.pojo.PageResult;
import com.center.framework.web.pojo.SortProperties;
import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.center.infrastructure.system.biz.menu.persistence.MenuRepository;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import com.center.infrastructure.system.biz.role.persistence.RoleMenuModel;
import com.center.infrastructure.system.biz.role.persistence.RoleMenuRepository;
import com.center.infrastructure.system.biz.role.persistence.RoleModel;
import com.center.infrastructure.system.biz.role.persistence.RoleRepository;
import com.center.infrastructure.system.biz.tenant.persitence.QTenantModel;
import com.center.infrastructure.system.biz.tenant.persitence.TenantModel;
import com.center.infrastructure.system.biz.tenant.persitence.TenantRepository;
import com.center.infrastructure.system.biz.tenant.pojo.*;
import com.center.infrastructure.system.biz.user.persistence.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TenantServiceImpl implements TenantService {

    @Resource
    private TenantRepository tenantRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private DepartRepository departRepository;

    @Resource
    private CacheFactory cacheFactory;

    @Value("${admin.password}")
    private String password;
    @Autowired
    private SnowFlakeConfig snowFlakeConfig;
    @Autowired
    private RoleMenuRepository roleMenuRepository;
    @Autowired
    private JPAQueryFactory jpaQueryFactory;
    @Autowired
    private MenuRepository menuRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 2)
    public void save(TenantCreateReq tenantCreateReq) {
        //企业名称重复校验
        checkName(tenantCreateReq.getName());
        TenantModel tenantModel = OrikaUtils.convert(tenantCreateReq, TenantModel.class);
        long tenantId = snowFlakeConfig.snowFlakeCore().nextId();
        tenantModel.setId(tenantId);
        //初始化角色
        Map<RoleEnum, Long> roleIdMap = initRole(tenantId);
        //创建根部门
        Long departId = initDepart(tenantId, tenantCreateReq.getName());
        //设置管理员
        Long managerId = checkManager(tenantCreateReq.getManagerName(),tenantCreateReq.getDisplayName(), tenantCreateReq.getPhoneNumber(), tenantId, roleIdMap, departId, tenantCreateReq.getEmail());
        tenantModel.setManagerId(managerId);

        tenantModel.setDescription("");
        tenantModel.setExpireTime(LocalDateTime.now().plusYears(100));
        tenantModel.setStatus(CommonStatusEnum.ACTIVE);
        tenantModel.setAccountCount(100);
        tenantRepository.save(tenantModel);
    }

    /**
     * 初始化部门信息
     * 此方法用于创建公司时创建一个与公司同名的顶级部门(parentId为0)，并返回此部门的ID
     *
     * @param tenantId 租户ID，用于区分不同租户的数据
     * @param name     部门名称，表示新创建部门的名称
     * @return 返回新创建部门的ID，用于后续可能的操作或记录
     */
    private Long initDepart(Long tenantId, String name) {
        DepartModel departModel = new DepartModel();
        departModel.setDepartName(name);
        departModel.setParentId(0L);
        departModel.setTenantId(tenantId);
        departModel.setSort(1);
        departRepository.save(departModel);
        return departModel.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 2)
    public void update(TenantUpdateReq tenantUpdateReq) {
        TenantModel tenantModel = tenantRepository.findById(tenantUpdateReq.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在"));
        tenantModel.setName(tenantUpdateReq.getName());
        Long originManagerId = tenantModel.getManagerId();
        UserModel userModel = userRepository.findById(originManagerId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在"));
        //判断管理员手机号是否修改
        if (!userModel.getPhoneNumber().equals(tenantUpdateReq.getPhoneNumber())) {
            //管理员手机号已修改，修改管理员
            Map<RoleEnum, Long> roleIdMap = new HashMap<>();
            roleRepository.findByTenantIdAndStatus(tenantUpdateReq.getId(), CommonStatusEnum.ACTIVE).forEach(roleModel -> {
                roleIdMap.put(roleModel.getCode(), roleModel.getId());
            });
            Long managerId = checkManager(tenantUpdateReq.getManagerName(),tenantUpdateReq.getDisplayName(), tenantUpdateReq.getPhoneNumber(), tenantUpdateReq.getId(),
                    roleIdMap, tenantUpdateReq.getEmail());
            tenantModel.setManagerId(managerId);
            tenantRepository.save(tenantModel);
            //将原管理员设置为普通用户
            UserRoleModel userRoleModel = userRoleRepository.findByUserId(userModel.getId());
            userRoleModel.setRoleId(roleIdMap.get(RoleEnum.EMPLOYEE));
            userRoleRepository.save(userRoleModel);
        } else {
            //管理员手机号未修改，但是管理员用户名与手机号不匹配
            if (!userModel.getUsername().equals(tenantUpdateReq.getManagerName())) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "该手机号已使用,请检查手机号与用户名是否匹配,或者换一个手机号尝试!");
            }
            //管理员手机号未修改，且管理员用户名与手机号匹配，则直接更新用户信息
            userModel.setEmail(tenantUpdateReq.getEmail());
            userRepository.save(userModel);
        }

        tenantRepository.save(tenantModel);
    }

    @Override
    public TenantResp get(Long id) {
        return OrikaUtils.convert(getTenantModel(id), TenantResp.class);
    }

    @Override
    public TenantUpdateResp getTenantUpdateResp(Long id) {
        TenantModel tenantModel = tenantRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在"));
        UserModel userModel = userRepository.findById(tenantModel.getManagerId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "用户不存在"));
        TenantUpdateResp tenantResp = new TenantUpdateResp();
        tenantResp.setId(tenantModel.getId());
        tenantResp.setName(tenantModel.getName());
        tenantResp.setDisplayName(userModel.getDisplayName());
        tenantResp.setManagerName(userModel.getUsername());
        tenantResp.setPhoneNumber(userModel.getPhoneNumber());
        tenantResp.setEmail(userModel.getEmail());
        return tenantResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 2)
    public void delete(Long id) {
        try {
            if (userRepository.countByTenantId(id) > 0) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, "请先删除此租户下的所有用户");
            }
            List<Long> departsId = departRepository.findAllByTenantId(id).stream().map(DepartModel::getId).collect(Collectors.toList());
            tenantRepository.deleteById(id);
            departRepository.deleteAllByTenantId(id);
            roleRepository.deleteAllByTenantId(id);
            userRepository.deleteAllByTenantId(id);
            cacheFactory.getHashCache().remove(id.toString());
            for (Long departId : departsId) {
                cacheFactory.getHashCache().remove(departId.toString());
            }
        } catch (EmptyResultDataAccessException e) {
            log.error("删除节点出错！", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, e, "租户不存在");
        }
    }

    @Override
    public void inactiveTenant(Long id) {
        updateTenantStatus(id, CommonStatusEnum.INACTIVE);
    }

    @Override
    public PageResult<TenantListResp> getTenantList(TenantPageReq tenantPageReq) {
        Pageable pageable = PageRequest.of(tenantPageReq.getPageNo() - 1
                , tenantPageReq.getPageSize()
                , Sort.by(Sort.Direction.DESC,
                        SortProperties.UPDATE_TIME));
        QTenantModel qTenantModel = QTenantModel.tenantModel;
        QUserModel qUserModel = QUserModel.userModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qTenantModel.managerId.eq(qUserModel.id));
        if (StringUtils.isNotEmpty(tenantPageReq.getTenantName())) {
            builder.and(qTenantModel.name.like("%"+tenantPageReq.getTenantName()+"%"));
        }

        JPQLQuery jpqlQuery = jpaQueryFactory.select((Projections.bean(
                        TenantListResp.class,
                        qTenantModel.id,
                        qTenantModel.name,
                        qUserModel.username,
                        qUserModel.phoneNumber,
                        qTenantModel.status
                )))
                .from(qTenantModel, qUserModel)
                .where(builder)
                .orderBy(qTenantModel.updateTime.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());
        Long total = jpqlQuery.fetchCount();
        List<TenantListResp> list = jpqlQuery.fetch();
        return PageResult.of(list, total);
    }

    @Override
    public void activeTenant(Long id) {
        updateTenantStatus(id, CommonStatusEnum.ACTIVE);
    }

    public void updateTenantStatus(Long id, CommonStatusEnum status) {
        TenantModel tenantModel = tenantRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在"));
        tenantModel.setStatus(status);
        tenantRepository.save(tenantModel);
    }

    private TenantModel getTenantModel(Long id) {
        Optional<TenantModel> optional = tenantRepository.findById(id);
        if (optional.isPresent()) {
            return optional.get();
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在");
        }
    }

    private void checkName(Long id, String name) {
        TenantModel tenantModel = getTenantModel(id);
        if (!tenantModel.getName().equals(name)) {
            tenantModel = tenantRepository.findByName(name);
            if (null != tenantModel) {
                throw ServiceExceptionUtil
                        .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的租户名称");
            }
        }
    }

    private void checkName(String name) {
        if (tenantRepository.existsByName(name)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的企业名称");
        }
    }

    private Long checkManager(String managerName,String displayName, String phoneNumber, Long tenantId, Map<RoleEnum, Long> roleIdMap, String email) {
        //首先查询该手机号有无用户注册
        UserModel userModel = userRepository.findByPhoneNumber(phoneNumber);
        if (null != userModel) {
            //若手机号已被使用，判断是否为该企业的用户
            if (userModel.getTenantId().equals(tenantId)) {
                //若为该企业的用户，则将其设置为管理员
                UserRoleModel userRoleModel = userRoleRepository.findByUserId(userModel.getId());
                userRoleModel.setRoleId(roleIdMap.get(RoleEnum.ADMIN));
                userRoleRepository.save(userRoleModel);
                userModel.setEmail(email);
                userModel.setDisplayName(displayName);
                userModel.setUsername(managerName);
                userRepository.save(userModel);
                return userModel.getId();
            } else {
                //若不是该企业的用户，则抛出异常
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "该手机号已在其他企业使用");
            }
        } else {
            //若手机号未被使用，创建企业管理员用户
            Long departId = departRepository.findByParentIdAndTenantId(0L, tenantId).get(0).getId();
            return initManager(managerName,displayName, phoneNumber, tenantId, departId, roleIdMap, email);
        }
    }

    /**
     * 检查并创建企业管理员用户
     * <p>
     * 此方法首先检查给定手机号是否已被注册如果已被注册，则抛出异常
     * 如果未被注册，则创建一个新的企业管理员用户，并分配相应的角色和部门
     *
     * @param managerName 管理员姓名
     * @param phoneNumber 手机号，用于检查是否已被注册
     * @param tenantId    租户ID，用于关联用户到特定租户
     * @param roleIdMap   角色ID映射，用于获取管理员角色ID
     * @param departId    部门ID，用于关联用户到特定部门
     * @return 返回新创建的用户ID
     */
    private Long checkManager(String managerName,String displayName, String phoneNumber, Long tenantId, Map<RoleEnum, Long> roleIdMap, Long departId, String email) {
        UserModel userModel = userRepository.findByPhoneNumber(phoneNumber);
        //首先查询该手机号有无用户注册
        if (null != userModel) {
            //若手机号已被使用,抛出异常
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "该手机号已被使用");
        } else {
            //若手机号未被使用，创建企业管理员用户
            return initManager(managerName,displayName, phoneNumber, tenantId, departId, roleIdMap, email);
        }
    }

    /**
     * 初始化管理员信息
     *
     * @param managerName 管理员姓名
     * @param phoneNumber 管理员联系电话
     * @param tenantId    租户ID
     * @param departId    部门ID
     * @param roleIdMap   角色枚举与角色ID的映射关系
     * @return 新增管理员的用户ID
     */
    public Long initManager(String managerName,String displayName, String phoneNumber, Long tenantId, Long departId, Map<RoleEnum, Long> roleIdMap, String email) {
        UserModel user = new UserModel();
        checkUsername(managerName);
        user.setUsername(managerName);
        user.setPhoneNumber(phoneNumber);
        user.setPassword(password);
        user.setStatus(CommonStatusEnum.ACTIVE);
        user.setDepartId(departId);
        user.setTenantId(tenantId);
        long userId = snowFlakeConfig.snowFlakeCore().nextId();
        user.setDeleted(false);
        user.setId(userId);
        user.setDisplayName(displayName);
        user.setEmail(email);
        userRepository.save(user);
        UserRoleModel userRoleModel = new UserRoleModel();
        userRoleModel.setUserId(userId);
        userRoleModel.setRoleId(roleIdMap.get(RoleEnum.ADMIN));
        userRoleRepository.save(userRoleModel);
        return userId;
    }

    private void checkUsername(String managerName) {
        if (userRepository.existsByUsername(managerName)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT,"该用户名已被使用");
        }
    }

    /**
     * 初始化角色
     * 该方法用于在系统中创建并保存两个预定义角色：管理员和员工
     * 这些角色是每个租户在系统中注册时自动创建的，以确保基本的权限管理结构
     *
     * @param tenantId 租户ID，用于关联角色与特定的租户
     */
    private Map<RoleEnum, Long> initRole(Long tenantId) {
        Map<RoleEnum, Long> roleIdMap = new HashMap<>();
        RoleModel roleModelAdmin = new RoleModel();
        roleModelAdmin.setRoleName("管理员");
        roleModelAdmin.setCode(RoleEnum.ADMIN);
        roleModelAdmin.setTenantId(tenantId);
        roleModelAdmin.setStatus(CommonStatusEnum.ACTIVE);
        roleModelAdmin.setSort(2);
        roleRepository.save(roleModelAdmin);
        roleIdMap.put(RoleEnum.ADMIN, roleModelAdmin.getId());
        RoleModel roleModelEmployee = new RoleModel();
        roleModelEmployee.setRoleName("员工");
        roleModelEmployee.setCode(RoleEnum.EMPLOYEE);
        roleModelEmployee.setTenantId(tenantId);
        roleModelEmployee.setStatus(CommonStatusEnum.ACTIVE);
        roleModelEmployee.setSort(0);
        roleRepository.save(roleModelEmployee);
        roleIdMap.put(RoleEnum.EMPLOYEE, roleModelEmployee.getId());
        //设置企业管理员企业管理权限
        setManagerMenu(roleIdMap.get(RoleEnum.ADMIN));
        return roleIdMap;
    }
    /**
     * 为管理员设置权限
     *
     * 除去新增企业外的所有权限
     *
     * @param roleId 角色ID，用于关联角色和菜单权限
     */
    public void setManagerMenu(Long roleId){
        menuRepository.findAllByIdNotAndParentId(1848552499414859776L,1848547917292929024L).forEach(menuModel -> {
            RoleMenuModel roleMenuModel = new RoleMenuModel();
            roleMenuModel.setRoleId(roleId);
            roleMenuModel.setMenuId(menuModel.getId());
            roleMenuRepository.save(roleMenuModel);
        });

    }

}
