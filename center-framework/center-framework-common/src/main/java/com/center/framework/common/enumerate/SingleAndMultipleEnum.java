package com.center.framework.common.enumerate;

import lombok.AllArgsConstructor;

@AllArgsConstructor

public enum SingleAndMultipleEnum implements IEnumerate<String>{

    SINGLE("SINGLE","单个"),
    MULTIPLE("MULTIPLE","多个");

    private String value;

    private String description;

    @Override
    public String getValue() {
        return null;
    }

    @Override
    public String getDescription() {
        return null;
    }
}
