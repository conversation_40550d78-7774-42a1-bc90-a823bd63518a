package com.center.framework.common.enumerate;

import lombok.AllArgsConstructor;

/**
 * 通用的状态枚举类，如果业务上有其他的状态，可以在单独的业务Module中重新定义新的枚举类
 */

@AllArgsConstructor
public enum CommonStatusEnum implements IEnumerate<String> {

  ACTIVE("ACTIVE","启用"),
  INACTIVE("INACTIVE","停用");

  private String value;

  private String description;

  @Override
  public String getValue() {
    return value;
  }

  @Override
  public String getDescription() {
    return description;
  }}
