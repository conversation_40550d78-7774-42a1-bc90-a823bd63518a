package com.center.framework.common.exception.util;

import cn.hutool.core.util.StrUtil;
import com.center.framework.common.exception.ErrorCode;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import lombok.extern.slf4j.Slf4j;

/**
 * 目的在于，格式化异常信息提示。
 * 考虑到 String.format 在参数不正确时会报错，因此使用 {} 作为占位符，并使用 {@link #doFormat(int, String, Object...)} 方法来格式化
 * <p>
 * 因为 {@link #MESSAGES} 里面默认是没有异常信息提示的模板的，所以需要使用方自己初始化进去。目前想到的有几种方式：
 * <p>
 * 1. 异常提示信息，写在枚举类中，
 */
@Slf4j
public class ServiceExceptionUtil {

    /**
     * 错误码提示模板
     */
    private static final ConcurrentMap<Integer, String> MESSAGES = new ConcurrentHashMap<>();

    public static void putAll(Map<Integer, String> messages) {
        ServiceExceptionUtil.MESSAGES.putAll(messages);
    }

    public static void put(Integer code, String message) {
        ServiceExceptionUtil.MESSAGES.put(code, message);
    }

    public static void delete(Integer code, String message) {
        ServiceExceptionUtil.MESSAGES.remove(code, message);
    }

    // ========== 和 ServiceException 的集成 ==========

    public static ServiceException exception(ErrorCode errorCode) {
        String messagePattern = MESSAGES.getOrDefault(errorCode.getCode(), errorCode.getMessage());
        return exception0(errorCode.getCode(), null, messagePattern);
    }

    public static ServiceException exception(ErrorCode errorCode, Object... params) {
        String messagePattern = MESSAGES.getOrDefault(errorCode.getCode(), errorCode.getMessage());

        // ✅ 防止嵌套前缀：如果 param 本身就包含模板前缀，直接返回 param
        if (params != null && params.length == 1 && params[0] instanceof String) {
            String paramStr = (String) params[0];
            String patternPrefix = messagePattern.replace("{}", "");
            if (paramStr.startsWith(patternPrefix)) {
                return new ServiceException(errorCode.getCode(), paramStr);
            }
        }

        return exception0(errorCode.getCode(), null, messagePattern, params);
    }


    public static ServiceException exception(ErrorCode errorCode, Exception exception) {
        String messagePattern = MESSAGES.getOrDefault(errorCode.getCode(), errorCode.getMessage());
        return exception0(errorCode.getCode(), exception, messagePattern);
    }

    public static ServiceException exception(ErrorCode errorCode, Exception exception, Object... params) {
        String messagePattern = MESSAGES.getOrDefault(errorCode.getCode(), errorCode.getMessage());
        return exception0(errorCode.getCode(), exception, messagePattern, params);
    }

    /**
     * 创建指定编号的 ServiceException 的异常
     *
     * @param code 编号
     * @return 异常
     */
    public static ServiceException exception(Integer code) {
        return exception0(code, null, MESSAGES.get(code));
    }

    /**
     * 创建指定编号的 ServiceException 的异常
     *
     * @param code   编号
     * @param params 消息提示的占位符对应的参数
     * @return 异常
     */
    public static ServiceException exception(Integer code, Exception exception, Object... params) {
        return exception0(code, exception, MESSAGES.get(code), params);
    }

    public static ServiceException exception0(Integer code, Exception exception, String messagePattern, Object... params) {
        String message = doFormat(code, messagePattern, params);
        return new ServiceException(code, message, exception);
    }

    public static ServiceException invalidParamException(Exception exception, String messagePattern, Object... params) {
        return exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), exception, messagePattern, params);
    }

    // ========== 格式化方法 ==========

    /**
     * 将错误编号对应的消息使用 params 进行格式化。
     *
     * @param code           错误编号
     * @param messagePattern 消息模版
     * @param params         参数
     * @return 格式化后的提示
     */
    public static String doFormat(int code, String messagePattern, Object... params) {
        StringBuilder sbuf = new StringBuilder(messagePattern.length() + 50);
        int i = 0;
        int j;
        int l;
        for (l = 0; l < params.length; l++) {
            j = messagePattern.indexOf("{}", i);
            if (j == -1) {
                log.error("[doFormat][参数过多：错误码({})|错误内容({})|参数({})", code, messagePattern, params);
                if (i == 0) {
                    return messagePattern;
                } else {
                    sbuf.append(messagePattern.substring(i));
                    return sbuf.toString();
                }
            } else {
                sbuf.append(messagePattern, i, j);
                sbuf.append(params[l]);
                i = j + 2;
            }
        }
        if (messagePattern.indexOf("{}", i) != -1) {
            log.error("[doFormat][参数过少：错误码({})|错误内容({})|参数({})", code, messagePattern, params);
        }
        sbuf.append(messagePattern.substring(i));
        return sbuf.toString();
    }

    public static String getStackTrace(Exception exception) {
        String result = StrUtil.EMPTY;
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        if (null != exception) {
            try {
                exception.printStackTrace(printWriter);
                result = stringWriter.toString();
            } catch (Exception e) {
                log.error("解析异常堆栈信息出错", e);
            } finally {
                printWriter.close();
                try {
                    stringWriter.close();
                } catch (IOException e) {
                    log.error("解析异常堆栈信息出错", e);
                }
            }
        }
        return result;
    }

}
