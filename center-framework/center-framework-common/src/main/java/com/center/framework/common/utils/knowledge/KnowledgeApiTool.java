package com.center.framework.common.utils.knowledge;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 通用的工具类，用于调用知识库相关的API
 */
@Slf4j
public class KnowledgeApiTool {
    // 连接超时时间（秒）
    private static final int CONNECT_TIMEOUT = 60;
    private static String pythonApiKey ="app-i8p2B40OaL67Qy1WDvc9KvbB";
    // 读取超时时间（秒）
    private static final int READ_TIMEOUT = 60;
    private static final OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .build();

    /**
     * 发送POST请求，并返回JSON响应结果
     * @param url API的URL
     * @param requestBody 请求体数据（JSON格式）
     * @return JSONObject 解析后的JSON对象
     * @throws Exception 可能出现的异常
     */
    public static JSONObject post(String url, Map<String, Object> requestBody) throws Exception {
        // 调用新的方法，传入空的字段列表
        return post(url, requestBody, LogMaskFields.DEFAULT_FIELDS_TO_MASK.toArray(new String[0]));
    }

    public static JSONObject post(String url, Map<String, Object> requestBody, String... fieldsToMask) throws Exception {
        // 将可变参数转换为集合
        Set<String> fieldsToMaskSet = new HashSet<>(Arrays.asList(fieldsToMask));

        // 复制请求体，用于日志记录
        Map<String, Object> logRequestBody = new LinkedHashMap<>(requestBody);

        // 遍历需要隐藏的字段，替换值
        for (String field : fieldsToMaskSet) {
            if (logRequestBody.containsKey(field)) {
                logRequestBody.put(field, "[因为内容过多，已隐藏]");
            }
        }

        // 将请求体转换为JSON格式
        String jsonBody = JSON.toJSONString(requestBody);
        log.info("发送POST请求: {}, 请求体: {}", url, logRequestBody);

        // 创建请求体
        RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json; charset=utf-8"));

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), responseBody);
                throw new IOException("API请求失败，状态码: " + response.code());
            }

            // 获取响应体字符串
            String responseStr = response.body() != null ? response.body().string() : "";
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseObject(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw new RuntimeException("调用API失败", e);
        }
    }

    public static JSONObject postJob(String url, Map<String, Object> requestBody) throws Exception {
        // 调用新的方法，传入空的字段列表
        return postJob(url, requestBody, LogMaskFields.DEFAULT_FIELDS_TO_MASK.toArray(new String[0]));
    }

    public static JSONObject postJob(String url, Map<String, Object> requestBody, String... fieldsToMask) throws Exception {
        // 将请求体转换为JSON格式
        String jsonBody = JSON.toJSONString(requestBody);
        // 遍历需要隐藏的字段，替换值
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonBody);
        // 递归遍历并替换符合条件的字段
        traverseAndReplace(rootNode);
        log.info("发送POST请求: {}, 请求体: {}", url, rootNode);

        // 创建请求体
        RequestBody body = RequestBody.create(jsonBody, MediaType.parse("application/json; charset=utf-8"));

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization","Bearer "+ pythonApiKey)
                .addHeader("Content-Type","application/json")
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), responseBody);
                throw new IOException("API请求失败，状态码: " + response.code());
            }

            // 获取响应体字符串
            String responseStr = response.body() != null ? response.body().string() : "";
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseObject(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw new RuntimeException("调用API失败", e);
        }
    }

    public static JSONObject post(String url,File file) throws Exception {
        // 调用新的方法，传入空的字段列表
        return post(url,file, LogMaskFields.DEFAULT_FIELDS_TO_MASK.toArray(new String[0]));
    }
    public static JSONObject post(String url, File file, String... fieldsToMask) throws Exception {
        // 将可变参数转换为集合
        Set<String> fieldsToMaskSet = new HashSet<>(Arrays.asList(fieldsToMask));

        // 构建 multipart/form-data 请求体
        MultipartBody.Builder multipartBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);

        // 添加文件到multipart
        if (file != null) {
            multipartBuilder.addFormDataPart("file", file.getName(),
                    RequestBody.create(file, MediaType.parse("application/octet-stream")));
        }

        // 创建multipart请求体
        RequestBody body = multipartBuilder.build();

        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), responseBody);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,"API请求失败，状态码: " + response.code());
            }

            // 获取响应体字符串
            String responseStr = response.body() != null ? response.body().string() : "";
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseObject(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,"调用API失败", e);
        }
    }


    /**
     * 发送GET请求，并返回JSON响应结果
     * @param url API的URL
     * @param params 请求参数
     * @return JSONObject 解析后的JSON对象
     * @throws Exception 可能出现的异常
     */
    public static JSONObject get(String url, Map<String, String> params) throws Exception {
        return get(url, params, LogMaskFields.DEFAULT_FIELDS_TO_MASK.toArray(new String[0]));
    }

    public static JSONObject get(String url, Map<String, String> params, String... fieldsToMask) throws Exception {
        // 构建URL参数
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        Map<String, String> logParams = new LinkedHashMap<>(params);

        // 遍历需要隐藏的字段，替换值
        Set<String> fieldsToMaskSet = new HashSet<>(Arrays.asList(fieldsToMask));
        for (String field : fieldsToMaskSet) {
            if (logParams.containsKey(field)) {
                logParams.put(field, "[内容已隐藏]");
            }
        }

        for (Map.Entry<String, String> entry : params.entrySet()) {
            urlBuilder.addQueryParameter(entry.getKey(), entry.getValue());
        }

        String finalUrl = urlBuilder.build().toString();

        // 日志记录使用被隐藏字段处理后的参数
        log.info("发送GET请求: {}, 请求参数: {}", finalUrl, logParams);

        // 构建请求
        Request request = new Request.Builder()
                .url(finalUrl)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.error("API请求失败, 状态码: {}, 响应: {}", response.code(), responseBody);
                throw new IOException("API请求失败，状态码: " + response.code());
            }

            // 获取响应体字符串
            String responseStr = response.body() != null ? response.body().string() : "";
            log.info("API响应成功, 响应内容: {}", responseStr);

            // 将响应字符串解析为JSON对象
            return JSON.parseObject(responseStr);
        } catch (IOException e) {
            log.error("API请求出现异常: {}", e.getMessage());
            throw new RuntimeException("调用API失败", e);
        }
    }


    /**
     * 从JSON响应中提取关键字段
     * @param jsonObject JSON响应
     * @param key 字段名
     * @return 字段值
     */
    public static String extractField(JSONObject jsonObject, String key) {
        if (jsonObject == null) {
            log.error("JSON对象为空，无法提取字段: {}", key);
            return null;
        }
        String value = jsonObject.getString(key);
        log.info("提取字段: {}, 值: {}", key, value);
        return value;
    }

    private static void traverseAndReplace(JsonNode node) {
        if (node.isObject()) {
            // 遍历所有字段
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                JsonNode fieldValue = field.getValue();
                // 递归处理字段值
                traverseAndReplace(fieldValue);
                // 如果满足条件，替换值
                if (fieldValue.isTextual() &&  field.getKey().equals("PDF_JS_BASE64")) {
                    ((ObjectNode) node).put(field.getKey(),"[因为内容过多，已隐藏]");
                }
            }
        } else if (node.isArray()) {
            // 如果是数组，遍历数组元素
            for (JsonNode arrayItem : node) {
                traverseAndReplace(arrayItem);
            }
        }
    }

}