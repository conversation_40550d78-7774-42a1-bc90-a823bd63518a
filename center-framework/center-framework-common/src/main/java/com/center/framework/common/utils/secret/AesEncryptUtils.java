package com.center.framework.common.utils.secret;


import com.center.framework.common.utils.secret.SecretTool;
import cn.hutool.core.util.StrUtil;

public class AesEncryptUtils {

    private static final SecretTool secretTool = new SecretTool(); // 使用默认密钥

    public static String encrypt(String raw) {
        return secretTool.encryptHex(raw);
    }

    public static String decrypt(String encryptedHex) {
        return secretTool.decryptStrHex(encryptedHex);
    }

    public static String maskKey(String decryptedKey) {
        if (StrUtil.isBlank(decryptedKey) || decryptedKey.length() < 8) {
            return "****";
        }
        return decryptedKey.substring(0, 4) + "****" + decryptedKey.substring(decryptedKey.length() - 4);
    }
}
