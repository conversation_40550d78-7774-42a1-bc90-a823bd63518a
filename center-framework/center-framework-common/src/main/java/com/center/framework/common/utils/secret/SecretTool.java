package com.center.framework.common.utils.secret;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

import java.nio.charset.StandardCharsets;

/**
 * AES对称加密类，可以指定加密的私钥，如果不指定，则使用默认的私钥进行加密与解密。
 */
public class SecretTool {

    //密钥
    private String defaultKey = "D5E^v>lvAI&Z12nk";

    //构建
    private SymmetricCrypto aes;


    public SecretTool(){
        aes = new SymmetricCrypto(SymmetricAlgorithm.AES, defaultKey.getBytes(StandardCharsets.UTF_8));
    }

    public SecretTool(String key){
        aes = new SymmetricCrypto(SymmetricAlgorithm.AES, key.getBytes(StandardCharsets.UTF_8));    }
    /**
     * 加密-字符型原文
     *
     * @param data 原文
     * @return String 字符串类型-密文
     */
    public String encryptHex(String data) {
        //加密为16进制表示
        String encryptHex = aes.encryptHex(data);
        return encryptHex;
    }

    /**
     * 解密-返回字符型原文
     *
     * @param data 密文
     * @return String 字符串类型-原文
     */
    public String decryptStrHex(String data) {
        //解密为字符串
        String decryptStr = aes.decryptStr(data, CharsetUtil.CHARSET_UTF_8);
        return decryptStr;
    }


    /**
     * 加密-返回字节型密文
     *
     * @param data 原文
     * @return byte[] 字节数组-密文
     */
    public byte[] encryptByte(String data) {
        //加密
        byte[] encrypt = aes.encrypt(data);
        return encrypt;
    }

    /**
     * 解密-返回字节型原文
     * @param data 密文
     * @return byte[] 字节数组-原文
     */
    public byte[] decryptStrByte(String data){
        //解密
        byte[] decrypt = aes.decrypt(data);
        return decrypt;
    }

}