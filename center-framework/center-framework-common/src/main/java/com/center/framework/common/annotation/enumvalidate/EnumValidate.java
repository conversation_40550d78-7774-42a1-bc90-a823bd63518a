package com.center.framework.common.annotation.enumvalidate;


import com.center.framework.common.enumerate.IEnumerate;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.FIELD,ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumConstrainValidator.class)
public @interface EnumValidate {

  String message() default "参数值不匹配";

  //支持枚举列表验证
  Class<? extends IEnumerate<?>> value();

  //分组
  Class<?>[] groups() default {};


  //负载
  Class<? extends Payload>[] payload() default {};
}
