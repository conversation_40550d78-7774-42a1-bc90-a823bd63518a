package com.center.framework.common.utils.cron;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.center.framework.common.enumerate.JobEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.datetime.DateTimeUtils;
import java.util.Date;

import org.quartz.*;
import org.quartz.spi.OperableTrigger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Cron工具类
 * cron知识
 * 字段 允许值 允许的特殊字符
 * 秒 0-59 , - * /
 * 分 0-59 , - * /
 * 小时 0-23 , - * /
 * 日期 1-31 , - * ? / L W C
 * 月份 1-12 或者 JAN-DEC , - * /
 * 星期 1-7 或者 SUN-SAT , - * ? / L C #
 * 年（可选） 留空, 1970-2099 , - * /
 * 值说明
 * * 表示所有值；
 * ? 表示未说明的值，即不关心它为何值；
 * - 表示一个指定的范围；
 * , 表示附加一个可能值；
 * / 符号前表示开始时间，符号后表示每次递增的值；
 * L("last") ("last") "L" 用在day-of-month字段意思是 "这个月最后一天"；用在 day-of-week字段, 它简单意思是 "7" or "SAT"。 如果在day-of-week字段里和数字联合使用，它的意思就是 "这个月的最后一个星期几" – 例如： "6L" means "这个月的最后一个星期五". 当我们用“L”时，不指明一个列表值或者范围是很重要的，不然的话，我们会得到一些意想不到的结果。
 * W("weekday") 只能用在day-of-month字段。用来描叙最接近指定天的工作日（周一到周五）。例如：在day-of-month字段用“15W”指“最接近这个 月第15天的工作日”，即如果这个月第15天是周六，那么触发器将会在这个月第14天即周五触发；如果这个月第15天是周日，那么触发器将会在这个月第 16天即周一触发；如果这个月第15天是周二，那么就在触发器这天触发。注意一点：这个用法只会在当前月计算值，不会越过当前月。“W”字符仅能在 day-of-month指明一天，不能是一个范围或列表。也可以用“LW”来指定这个月的最后一个工作日。
 * # 只能用在day-of-week字段。用来指定这个月的第几个周几。例：在day-of-week字段用"6#3"指这个月第3个周五（6指周五，3指第3个）。如果指定的日期不存在，触发器就不会触发。
 * C 指和calendar联系后计算过的值。例：在day-of-month 字段用“5C”指在这个月第5天或之后包括calendar的第一天；在day-of-week字段用“1C”指在这周日或之后包括calendar的第一天。
 *
 */
public class CronUtil {

  /**
   * 计算表达式最近几次执行时间
   * @param cronExpress 表达式
   * @param num 次数
   * @return 时间集合
   */
  public static List<String> getCronNextTimes(String cronExpress, Integer num) {
    if (StrUtil.isEmpty(cronExpress)) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "Cron 表达式");
    }

    // 判断cron表达式
    if (!CronExpression.isValidExpression(cronExpress)) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "Cron 表达式:" + cronExpress);
    }

    if (num == null || num == 0) {
      num = 1;
    }

    List<String> list = new ArrayList<>();

    // 使用 TriggerBuilder 创建 CronTrigger
    CronTrigger cronTrigger = TriggerBuilder.newTrigger()
            .withIdentity("cronTrigger", "group1")
            .withSchedule(CronScheduleBuilder.cronSchedule(cronExpress))
            .build();


    // 使用 TriggerUtils 计算下次执行时间
    List<Date> dates = TriggerUtils.computeFireTimes((OperableTrigger) cronTrigger, null, num);

    String format = DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
    for (Date date : dates) {
      list.add(DateUtil.format(date, format));
    }

    return list;
  }

  /**
   *  构建Cron表达式
   * 目前支持三种常用的cron表达式
   * 1.每天的某个时间点执行 例:12 12 12 * * 表示每天12时12分12秒执行
   * 2.每周的哪几天执行    例:12 12 12 ? * 1,2,3表示每周的周1周2周3 ,12时12分12秒执行
   * 3.每月的哪几天执行    例:12 12 12 1,21,13 * ?表示每月的1号21号13号 12时12分12秒执行
   *
   *
   */
  public static String createCronExpression(CronVO cronVO){
    StringBuilder cronExp = new StringBuilder();

    if(null == cronVO.getJobType()) {
      System.out.println("执行周期未配置" );//执行周期未配置
    }

    if (null != cronVO.getSecond()
        && null != cronVO.getMinute()
        && null != cronVO.getHour()) {
      //秒
      cronExp.append(cronVO.getSecond()).append(" ");
      //分
      cronExp.append(cronVO.getMinute()).append(" ");

      //小时
      if(JobEnum.HOUR.getValue().equals(cronVO.getJobType().getValue())) {
        cronExp.append(cronVO.getHour()+"/"+cronVO.getBeApart()).append(" ");
        cronExp.append("* ");//日
        cronExp.append("* ");//月
        cronExp.append("?");//周
      }else {
        cronExp.append(cronVO.getHour()).append(" ");
      }
      //每天
      if(JobEnum.DAY.getValue().equals(cronVO.getJobType().getValue())){
        //12 12 12 1/2 * ? *
        //12 12 12 * * ?
        if(cronVO.getBeApart()!=null){
          cronExp.append("1");//日
          cronExp.append("/");
          cronExp.append(cronVO.getBeApart()+1);//月
          cronExp.append(" ");
          cronExp.append("* ");
          cronExp.append("?");
        }else {
          cronExp.append("* ");//日
          cronExp.append("* ");//月
          cronExp.append("?");//周
        }
      }

      //按每周
      else if(JobEnum.WEEK.getValue().equals(cronVO.getJobType().getValue())){
        //一个月中第几天
        cronExp.append("? ");
        //月份
        cronExp.append("* ");
        //周
        Integer[] weeks = cronVO.getDayOfWeeks();
        for(int i = 0; i < weeks.length; i++){
          if(i == 0){
            cronExp.append(weeks[i]);
          } else{
            cronExp.append(",").append(weeks[i]);
          }
        }

      }
      //按每月
      else if(JobEnum.MONTH.getValue().equals(cronVO.getJobType().getValue())){
        //一个月中的哪几天
        Integer[] days = cronVO.getDayOfMonths();
        for(int i = 0; i < days.length; i++){
          if(i == 0){
            if(days[i]==32){
              //本月最后一天
              return "0 0 0 L * ?";
            }else {
              cronExp.append(days[i]);
            }
          } else{
            cronExp.append(",").append(days[i]);
          }
        }
        //月份
        cronExp.append(" * ");
        //周
        cronExp.append("?");
      }
      //按每年
      else if(JobEnum.YEAR.getValue().equals(cronVO.getJobType().getValue())){
        //一个年中的哪几天
        Integer[] days = cronVO.getDayOfMonths();
        if(ArrayUtil.isEmpty(days)){
          cronExp.append("*");
        }else{
          for(int i = 0; i < days.length; i++){
            if(i == 0){
              cronExp.append(days[i]);
            } else{
              cronExp.append(",").append(days[i]);
            }
          }
        }
        //月份
        Integer[] months = cronVO.getMonths();
        if (ArrayUtil.isEmpty(months)) {
          cronExp.append(" *");
        }else{
          for (int i = 0; i < months.length; i++){
            Integer month = months[i];
            if (month > 12){
              throw new RuntimeException("月份数据异常: "+ Arrays.toString(months));
            }
            if(i == 0){
              cronExp.append(" ").append(month);
            }else{
              cronExp.append(",").append(month);
            }
          }
        }
        cronExp.append(" ?");
      }
      else if(JobEnum.EVERY.getValue().equals(cronVO.getJobType().getValue())){
        cronExp.append("* ");//日
        cronExp.append("* ");//月
        cronExp.append("?");//周
      }
    }
    return cronExp.toString();
  }

  /**
   * 生成计划的详细描述
   *
   *@param  cronVO 表达式数据
   *@return String
   */
  public static String createDescription(CronVO cronVO){
    StringBuilder description = new StringBuilder();
    //计划执行开始时间
//      Date startTime = cronModel.getScheduleStartTime();

    if (null != cronVO.getSecond()
        && null != cronVO.getMinute()
        && null != cronVO.getHour()) {
      //按每天
      if(JobEnum.DAY.getValue().equals(cronVO.getJobType().getValue())){
        Integer beApart = cronVO.getBeApart();
        if(beApart !=null){
          description.append("每间隔").append(beApart).append("天");
        }else{
          description.append("每天");
        }
        description.append(cronVO.getHour()).append("时");
        description.append(cronVO.getMinute()).append("分");
        description.append(cronVO.getSecond()).append("秒");
        description.append("执行");
      }

      //按每周
      else if(JobEnum.WEEK.getValue().equals(cronVO.getJobType().getValue())){
        if(cronVO.getDayOfWeeks() != null && cronVO.getDayOfWeeks().length > 0) {
          StringBuilder days = new StringBuilder();
          for(int i : cronVO.getDayOfWeeks()) {
            days.append("周").append(i);
          }
          description.append("每周的").append(days).append(" ");
        }
        if (null != cronVO.getSecond()
            && null != cronVO.getMinute()
            && null != cronVO.getHour()) {
          description.append(",");
          description.append(cronVO.getHour()).append("时");
          description.append(cronVO.getMinute()).append("分");
          description.append(cronVO.getSecond()).append("秒");
        }
        description.append("执行");
      }

      //按每月
      else if(cronVO.getJobType().getValue().equals(JobEnum.MONTH)){
        //选择月份
        if(cronVO.getDayOfMonths() != null && cronVO.getDayOfMonths().length > 0) {
          StringBuilder days = new StringBuilder();
          for(int i : cronVO.getDayOfMonths()) {
            days.append(i).append("号");
          }
          description.append("每月的").append(days).append(" ");
        }
        description.append(cronVO.getHour()).append("时");
        description.append(cronVO.getMinute()).append("分");
        description.append(cronVO.getSecond()).append("秒");
        description.append("执行");
      }

    }
    return description.toString();
  }


  /**
   * 构建Cron表达式
   * @param rate 第几位
   * @param cycle 数值
   * @return cron表达式
   */
  public static String createLoopCronExpression(int rate, int cycle) {
    String cron = "";
    switch (rate) {
      case 0:// 每cycle秒执行一次
        cron = "0/" + cycle + " * * * * ?";
        break;
      case 1:// 每cycle分钟执行一次
        cron = "0 0/" + cycle + " * * * ?";
        break;
      case 2:// 每cycle小时执行一次
        cron = "0 0 0/" + cycle + " * * ?";
        break;
      case 3:// 每cycle天的0点执行一次
        cron = "0 0 0 1/" + cycle + " * ?";
        break;
      case 4:// 每cycle月的1号0点执行一次
        cron = "0 0 0 1 1/" + cycle + " ? ";
        break;
      case 5://  每天cycle点执行一次
        cron = "0 0 " + cycle+ "  * * ?";
        break;
      default:// 默认每cycle秒执行一次
        cron = "0/1 * * * * ?";
        break;
    }
    return cron;
  }

  public static void main(String[] args) {
    CronVO cronVO = new CronVO();
    cronVO.setJobType(JobEnum.DAY);//按每天
    //每隔几天执行
    cronVO.setBeApart(3);
    String cropExp = createCronExpression(cronVO);
    System.out.println(cropExp + ":" + createDescription(cronVO));
    System.out.println(getCronNextTimes(cropExp, 5));

    cronVO.setJobType(JobEnum.WEEK);//每周的哪几天执行
    Integer[] dayOfWeeks = new Integer[3];
    dayOfWeeks[0] = 1;
    dayOfWeeks[1] = 2;
    dayOfWeeks[2] = 3;
    cronVO.setDayOfWeeks(dayOfWeeks);
    cronVO.setJobType(JobEnum.WEEK);
    cropExp = createCronExpression(cronVO);
    System.out.println(cropExp + ":" + createDescription(cronVO));
    System.out.println(getCronNextTimes(cropExp, 5));

    cronVO.setJobType(JobEnum.MONTH);//每月的哪几天执行
    Integer[] dayOfMonths = new Integer[3];
    dayOfMonths[0] = 1;
    dayOfMonths[1] = 21;
    dayOfMonths[2] = 13;
    cronVO.setDayOfMonths(dayOfMonths);
    cropExp = createCronExpression(cronVO);
    System.out.println(cropExp + ":" + createDescription(cronVO));
    System.out.println(getCronNextTimes(cropExp, 5));

    cronVO.setJobType(JobEnum.EVERY);//每天的几点几分几秒开始
    cropExp = createCronExpression(cronVO);
    System.out.println(cropExp);
    System.out.println(getCronNextTimes(cropExp, 5));

    cronVO.setJobType(JobEnum.YEAR);
    cronVO.setMonths(new Integer[]{1,12});
    cronVO.setDayOfMonths(new Integer[]{1});
    cropExp = createCronExpression(cronVO);
    System.out.println(cropExp);
    System.out.println(getCronNextTimes(cropExp, 5));


    cronVO = new CronVO();
    cronVO.setJobType(JobEnum.HOUR);
    cronVO.setHour(3);
    cronVO.setBeApart(1);
//    System.out.println(createCronExpression(cronVO));
     cropExp = createCronExpression(cronVO);
    System.out.println(cropExp + ":" + createDescription(cronVO));
  }
}
