package com.center.framework.common.enumerate;


import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum EventTypeEnum implements IEnumerate<String>{

    MESSAGE("MESSAGE","消息"),
    FILE("FILE","文件");

    private String value;
    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
