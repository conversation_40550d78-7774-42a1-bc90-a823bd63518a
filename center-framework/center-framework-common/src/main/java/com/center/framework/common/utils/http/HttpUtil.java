package com.center.framework.common.utils.http;

import cn.hutool.core.util.StrUtil;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.http.HttpStatus;

@Slf4j
public class HttpUtil {

  private static OkHttpClient client;

  private static final String DEFAULT_MEDIA_TYPE = "application/json; charset=utf-8";

  private static final int CONNECT_TIMEOUT = 60;

  private static final int READ_TIMEOUT = 60;

  private static final String GET = "GET";

  private static final String POST = "POST";

  private static final String PUT = "PUT";

  private static final String DELETE = "DELETE";

  /**
   * 单例模式  获取类实例
   *
   * @return client
   */
  private static OkHttpClient getInstance() {
    if (client == null) {
      synchronized (OkHttpClient.class) {
        if (client == null) {
          client = new OkHttpClient.Builder()
              .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
              .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
              .build();
        }
      }
    }
    return client;
  }

  public static String doGet(String url,String tokenName,String tokenValue)  {

    try {
      long startTime = System.currentTimeMillis();
      addRequestLog(GET, url, null, null);

      Request request = new Request.Builder().url(url).addHeader(tokenName,tokenValue).build();
      // 创建一个请求

      Response response = getInstance().newCall(request).execute();
      int httpCode = response.code();
      String result;
      ResponseBody body = response.body();
      if (body != null) {
        result = body.string();
        addResponseLog(httpCode, result, startTime);
      } else {
        response.close();
        throw new RuntimeException("exception in OkHttpUtil,response body is null");
      }
      return handleHttpResponse(httpCode, result);
    } catch (Exception ex) {
      handleHttpThrowable(ex, url);
      return StrUtil.EMPTY;
    }
  }

  /**
   *
   * @param url
   * @param postBody
   * @param mediaType
   * @param tokenName
   * @param tokenValue
   * @return
   */
  public static String doPost(String url, String postBody, String mediaType
      ,String tokenName,String tokenValue)  {
    try {
      long startTime = System.currentTimeMillis();
      addRequestLog(POST, url, postBody, null);

      MediaType createMediaType = MediaType.parse(mediaType == null ? DEFAULT_MEDIA_TYPE : mediaType);
      Request request = new Request.Builder()
          .url(url)
          .post(RequestBody.create(postBody,createMediaType))
          .addHeader(tokenName,tokenValue)
          .build();

      Response response = getInstance().newCall(request).execute();
      int httpCode = response.code();
      String result;
      ResponseBody body = response.body();
      if (body != null) {
        result = body.string();
        addResponseLog(httpCode, result, startTime);
      } else {
        response.close();
        throw new IOException("exception in OkHttpUtil,response body is null");
      }
      return handleHttpResponse(httpCode, result);
    } catch (Exception ex) {
      handleHttpThrowable(ex, url);
      return StrUtil.EMPTY;
    }
  }

  public static String doPost(String url, Map<String, String> parameterMap, String callMethod
      ,String tokenName,String tokenValue)  {
    try {
      long startTime = System.currentTimeMillis();
      List<String> parameterList = new ArrayList<>();
      FormBody.Builder builder = new FormBody.Builder();
      if (parameterMap.size() > 0) {
        for (Map.Entry<String, String> entry : parameterMap.entrySet()) {
          String parameterName = entry.getKey();
          String value = entry.getValue();
          builder.add(parameterName, value);
          parameterList.add(parameterName + ":" + value);
        }
      }

      addRequestLog(POST, url, null, Arrays.toString(parameterList.toArray()));

      FormBody formBody = builder.build();
      Request request = new Request.Builder()
          .addHeader(tokenName,tokenValue)
          .url(url)
          .post(formBody)
          .build();

      Response response = getInstance().newCall(request).execute();
      String result;
      int httpCode = response.code();
      ResponseBody body = response.body();
      if (body != null) {
        result = body.string();
        addResponseLog(httpCode, result, startTime);
      } else {
        response.close();
        throw new IOException("exception in OkHttpUtil,response body is null");
      }
      return handleHttpResponse(httpCode, result);

    } catch (Exception ex) {
      handleHttpThrowable(ex, url);
      return StrUtil.EMPTY;
    }
  }

  /**
   * 参数直接拼接在url后面的POST请求
   * @param url
   * @param tokenName
   * @param tokenValue
   * @return
   */
  public static String doPost(String url,String parameters,String tokenName,String tokenValue){
    try {
      long startTime = System.currentTimeMillis();
      addRequestLog(POST, url, null, parameters);

      // 创建一个请求
//      FormBody formBody = new FormBody.Builder().build();
      RequestBody requestBody = RequestBody.create(MediaType.parse("application/x-www-form-urlencoded"),parameters);
      Request request = new Request.Builder().url(url).post(requestBody)
          .addHeader(tokenName, tokenValue).build();

      Response response = getInstance().newCall(request).execute();
      int httpCode = response.code();
      String result;
      ResponseBody body = response.body();
      if (body != null) {
        result = body.string();
        addResponseLog(httpCode, result, startTime);
      } else {
        response.close();
        throw new RuntimeException("exception in OkHttpUtil,response body is null");
      }
      return handleHttpResponse(httpCode, result);
    } catch (Exception ex) {
      handleHttpThrowable(ex, url);
      return StrUtil.EMPTY;
    }
  }
  /**
   * 参数直接拼接在url后面的POST请求
   * @param url
   * @param tokenName
   * @param tokenValue
   * @return
   */
  public static String doPut(String url,String parameters,String tokenName,String tokenValue){
    try {
      long startTime = System.currentTimeMillis();
      addRequestLog(PUT, url, null, parameters);

      // 创建一个请求
      RequestBody requestBody = RequestBody.create(MediaType.parse("application/x-www-form-urlencoded"),parameters);
      Request request = new Request.Builder().url(url).put(requestBody)
          .addHeader(tokenName, tokenValue).build();

      Response response = getInstance().newCall(request).execute();
      int httpCode = response.code();
      String result;
      ResponseBody body = response.body();
      if (body != null) {
        result = body.string();
        addResponseLog(httpCode, result, startTime);
      } else {
        response.close();
        throw new RuntimeException("exception in OkHttpUtil,response body is null");
      }
      return handleHttpResponse(httpCode, result);
    } catch (Exception ex) {
      handleHttpThrowable(ex, url);
      return StrUtil.EMPTY;
    }
  }
  /**
   * @param url
   * @param tokenName
   * @param tokenValue
   * @return
   */
  public static String doDelete(String url,String tokenName,String tokenValue){
    try {
      long startTime = System.currentTimeMillis();
      addRequestLog(DELETE, url, null, null);

      // 创建一个请求
      FormBody formBody = new FormBody.Builder().build();
      Request request = new Request.Builder().url(url).delete(formBody)
          .addHeader(tokenName, tokenValue).build();

      Response response = getInstance().newCall(request).execute();
      int httpCode = response.code();
      String result;
      ResponseBody body = response.body();
      if (body != null) {
        result = body.string();
        addResponseLog(httpCode, result, startTime);
      } else {
        response.close();
        throw new RuntimeException("exception in OkHttpUtil,response body is null");
      }
      return handleHttpResponse(httpCode, result);
    } catch (Exception ex) {
      handleHttpThrowable(ex, url);
      return StrUtil.EMPTY;
    }
  }


  private static void addRequestLog(String method, String url, String body, String formParam) {
    log.info("===========================request begin================================================");
    log.info("URI          : {}", url);
    log.info("Method       : {}", method);
    if (StrUtil.isNotBlank(body)) {
      log.info("Request body : {}", body);
    }
    if (StrUtil.isNotBlank(formParam)) {
      log.info("Request param: {}", formParam);
    }
    log.info("---------------------------request end--------------------------------------------------");
  }

  private static void addResponseLog(int httpCode, String result, long startTime) {
    long endTime = System.currentTimeMillis();
    log.info("Status       : {}", httpCode);
    log.info("Response     : {}", result);
    log.info("Time         : {} ms", endTime - startTime);
    log.info("===========================response end================================================");
  }

//  private static String handleHttpResponse(int httpCode, String result) throws HttpStatusException {
  private static String handleHttpResponse(int httpCode, String result)  {
    if (httpCode == HttpStatus.OK.value()) {
    }
    return result;
//    HttpStatus httpStatus = HttpStatus.valueOf(httpCode);
//    throw new HttpStatusException(httpStatus);
  }

//  private static void handleHttpThrowable(Exception ex, String url) throws HttpStatusException {
  private static void handleHttpThrowable(Exception ex, String url) throws RuntimeException {
//    if (ex instanceof HttpStatusException) {
//      throw (HttpStatusException) ex;
//    }
    log.error("OkHttp error url: " + url, ex);
    if (ex instanceof SocketTimeoutException) {
      throw new RuntimeException("request time out of OkHttp when do url:" + url);
    }
    throw new RuntimeException(ex);
  }

}



