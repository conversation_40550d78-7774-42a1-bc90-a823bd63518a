package com.center.framework.common.utils.datetime;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DateTimeUtils {

  public static final String TIME_ZONE_DEFAULT = "GMT+8";

  public static final String FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd";

  public static final String FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

  public static LocalDateTime getLocalStartDateTime(String startDateTime){
    if(StrUtil.isNotEmpty(startDateTime)){
      try{
        DateTime dateTime = DateUtil.parse(startDateTime, DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        dateTime = DateUtil.beginOfDay(dateTime);
        return DateUtil.toLocalDateTime(dateTime);
      }catch (Exception e){
        log.error("非法的日期格式",e);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"非法的日期格式:"+startDateTime);
      }
    }else {
      return null;
    }
  }
  public static LocalDateTime getLocalEndDateTime(String endDateTime){
    if(StrUtil.isNotEmpty(endDateTime)){
      try {
        DateTime dateTime = DateUtil.parse(endDateTime, DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        dateTime = DateUtil.endOfDay(dateTime);
        return DateUtil.toLocalDateTime(dateTime);
      }catch (Exception e){
        log.error("非法的日期格式",e);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"非法的日期格式:"+endDateTime);
      }
    }else {
      return null;
    }
  }

  public static String formatDateTime(LocalDateTime dateTime){
    if(null != dateTime){
      return DateUtil.format(dateTime,FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
    }
    return StrUtil.EMPTY;
  }
  public static LocalDateTime formatDateTime(String dateTime){
    if(StrUtil.isNotEmpty(dateTime)){
      try {
        return DateUtil.toLocalDateTime(
            DateUtil.parse(dateTime, DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
      }catch (Exception e){
        log.error("非法的日期格式",e);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT,"非法的日期格式:"+dateTime);
      }
    }else {
      return null;
    }
  }


}
