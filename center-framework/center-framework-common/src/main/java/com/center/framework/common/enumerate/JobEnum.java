package com.center.framework.common.enumerate;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum JobEnum implements IEnumerate<String>{

//  EVERY("每天",0),
//  DAY("日",1),
//  <PERSON>ON<PERSON>("月",2),
//  WEEK("周",3),
//  YEAR("年",4),
//  ;
  EVERY("EVERY","每天"),
  HOUR("HOUR","小时"),
  DAY("DAY","日"),
  MONTH("MONTH","月"),
  WEEK("WEEK","周"),
  YEAR("YEAR","年");

  private String value;

  private String description;

  @Override
  public String getValue() {
    return value;
  }

  @Override
  public String getDescription() {
    return description;
  }}
