package com.center.framework.web.web.handler;


import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.BAD_REQUEST;
import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.PARAM_MISMATCH;

import cn.hutool.core.util.StrUtil;
import com.center.framework.common.exception.ErrorCode;
import com.center.framework.common.exception.ServerException;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.secret.SecretTool;
import com.center.framework.web.pojo.CommonResult;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * General exception handler.
 */
@Slf4j
@RestControllerAdvice
public class GeneralExceptionHandler {

  @Value("${spring.profiles.active}")
  private String activeEnv;

  /**
   * handle application exception.
   *
   * @param exception application exception.
   * @param response http servlet response.
   * @return application error response.
   */
  @ExceptionHandler(ServerException.class)
  @ResponseBody
  public CommonResult<String> handleApplicationException(
      ServerException exception, HttpServletResponse response) {
    return CommonResult.error(exception.getCode(),exception.getMessage(), parseStackTrace(exception));
  }
  @ExceptionHandler(ServiceException.class)
  @ResponseBody
  public CommonResult<String> handleApplicationException(
      ServiceException exception, HttpServletResponse response) {
    return CommonResult.error(exception.getCode(),exception.getMessage(), parseStackTrace(exception));
  }

  /**
   * handle method argument not valid exception.
   *
   * @param exception method argument not valid exception.
   * @return validation error response.
   */
  @ExceptionHandler(MethodArgumentNotValidException.class)
  @ResponseBody
  @ResponseStatus(HttpStatus.OK)
  public CommonResult<String> handleValidationException(
      MethodArgumentNotValidException exception) {
    List<String> errorMessages = new ArrayList<>();
    exception.getBindingResult().getAllErrors().forEach(
        objectError -> {
          errorMessages.add(objectError.getDefaultMessage());
        });
    log.error(
        "Method arguments validation failed. errors:" + errorMessages.toString());

    return constructValidationErrorResponseForAnnotation(BAD_REQUEST,
        errorMessages.get(0),parseStackTrace(exception));
  }

  /**
   * handle bad request.
   */
  @ExceptionHandler({
      HttpMessageNotReadableException.class
  })
  @ResponseBody
  @ResponseStatus(HttpStatus.OK)
  public CommonResult<String>
  handleHttpMessageNotReadableException(HttpMessageNotReadableException exception) {
    log.error("UnExpected http message not readable exception: error:" + exception.getMessage(),
        exception);

    return constructValidationErrorResponse(BAD_REQUEST);
  }

  /**
   * handle bad request.
   */
  @ExceptionHandler({
      MethodArgumentTypeMismatchException.class
  })
  @ResponseBody
  @ResponseStatus(HttpStatus.OK)
  public CommonResult<String>
  handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException exception) {
    log.error("UnExpected method argument type mismatch exception: error:" + exception.getMessage(),
        exception);

    return constructValidationErrorResponse(PARAM_MISMATCH);

  }

  /**
   * Handle request/path validation error.
   *
   * @param exception constraint violation exception.
   * @return error map.
   */
  @ExceptionHandler(ConstraintViolationException.class)
  @ResponseBody
  @ResponseStatus(HttpStatus.OK)
  public CommonResult<String> handle(
      ConstraintViolationException exception) {
    log.error("UnExpected constraint violation exception: error:" + exception.getMessage(),
        exception);

    Map errorMessages = Collections.singletonMap("errors", exception
        .getConstraintViolations()
        .stream()
        .map(ConstraintViolation::getMessage)
        .collect(Collectors.toList()));

    List errors = (ArrayList) errorMessages.get("errors");

    return constructValidationErrorResponse(
        BAD_REQUEST, errors.get(0).toString(), parseStackTrace(exception));
  }

  /**
   * handle bad request.
   */
  @ExceptionHandler({
      MaxUploadSizeExceededException.class
  })
  @ResponseBody
  @ResponseStatus(HttpStatus.OK)
  public CommonResult<String>
  handleMaxUploadSizeExceededException(MaxUploadSizeExceededException exception) {
    log.error("UnExpected method argument type mismatch exception: error:" + exception.getMessage(),
        exception);
    return constructValidationErrorResponse(BAD_REQUEST);

  }

  /**
   * Handle unExcepted runtime error.
   *
   * @param exception constraint violation exception.
   */
  @ExceptionHandler({RuntimeException.class})
  @ResponseBody
  @ResponseStatus(HttpStatus.OK)
  public CommonResult<String> handleRuntimeException(
      RuntimeException exception) {
    log.error("UnExpected runtime exception: error:" + exception.getMessage(), exception);

    return constructValidationErrorResponse(
        INTERNAL_SERVER_ERROR, exception.getMessage(), parseStackTrace(exception));
  }

  @ExceptionHandler(AsyncRequestTimeoutException.class)
  public CommonResult<String> handleAsyncTimeoutException(AsyncRequestTimeoutException exception) {
    log.error("Async exception: error:" + exception.getMessage(), exception);

    return constructValidationErrorResponse(
            INTERNAL_SERVER_ERROR, exception.getMessage(), parseStackTrace(exception));
//    return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT).body("Request timed out");
  }

  /**
   * construct validation error response.
   */
  private CommonResult<String>
  constructValidationErrorResponse(ErrorCode errorCode) {
    return CommonResult.error(errorCode.getCode(), errorCode.getMessage());
  }
  private CommonResult<String>
  constructValidationErrorResponseForAnnotation(ErrorCode errorCode,String detail,String trace) {
    return CommonResult.error(errorCode.getCode(), errorCode.getMessage() + detail,trace);
  }

  private CommonResult<String> constructValidationErrorResponse(
      ErrorCode errorCode, String detail, String trace) {
    return CommonResult.error(
        errorCode.getCode(),
        ServiceExceptionUtil.doFormat(errorCode.getCode(), errorCode.getMessage(),detail),trace);
  }

  private String parseStackTrace(Exception exception){
//    local环境不返回堆栈信息
    String stackTrace = StrUtil.EMPTY;
    if("prod".equals(activeEnv)){
      SecretTool secretTool = new SecretTool();
      stackTrace = secretTool.encryptHex(ServiceExceptionUtil.getStackTrace(exception));
    }else if("qa".equals(activeEnv) || "dev".equals(activeEnv)){
      stackTrace = ServiceExceptionUtil.getStackTrace(exception);
    }
    return stackTrace;
  }
}
