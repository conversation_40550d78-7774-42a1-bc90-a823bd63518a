package com.center.framework.storage.file.service;

import cn.hutool.core.lang.Snowflake;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.storage.factory.FileStorageServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

@Service
@Slf4j
public class StorageFileServiceImpl implements StorageFileService {
    @Resource
    private Snowflake snowflake;
    @Resource
    private FileStorageServiceFactory fileStorageServiceFactory;

    @Value("${nginx.previewUrl}")
    private String previewBaseUrl;

    @Value("${nginx.localPath}")
    private String localBasePath;

    @Override
    public ResponseEntity<ByteArrayResource> previewFile(String filePath) {
        try {
            return fileStorageServiceFactory.getFileStorageService().previewFile(filePath);
        } catch (IOException e) {
            log.error("预览文件失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR,"获取文件预览信息失败");
        }
    }

    @Override
    @Transactional
    public Long uploadFile(MultipartFile file) {
        long fileId = snowflake.nextId();
        String localPath = "image/" + fileId + "/" + file.getOriginalFilename();

        // 创建临时文件
        File tempFile = null;
        try {
            tempFile = File.createTempFile("upload_", "_" + file.getOriginalFilename());
            file.transferTo(tempFile); // 将 MultipartFile 存入临时文件

            // 保存到本地
            saveFileToLocal(tempFile, localPath);

        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e, file.getOriginalFilename());
        } finally {
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    log.warn("临时文件未能成功删除：{}", tempFile.getAbsolutePath());
                }
            }
        }
        return fileId;
    }

    public void deleteFileFromLocal(String localPath) throws IOException {

        // 把传入的 file/xxx 改成 image/xxx
        if (localPath.startsWith("file/")) {
            localPath = localPath.replaceFirst("file/", "image/");
        }

        String fullPath = localBasePath + localPath;
        File targetFile = new File(fullPath);

        if (targetFile.exists()) {
            boolean deleted = targetFile.delete();
            if (deleted) {
                log.info("文件已成功删除：{}", fullPath);
            } else {
                log.warn("文件删除失败：{}", fullPath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, "删除文件失败");
            }
        } else {
            log.warn("文件不存在，无需删除：{}", fullPath);
        }
    }


    private void saveFileToLocal(File tempFile, String targetPath) throws IOException {
        String fullPath = localBasePath + targetPath;

        File targetFile = new File(fullPath);
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                throw new IOException("无法创建本地目录：" + parentDir.getAbsolutePath());
            }
        }

        // 拷贝文件内容
        Files.copy(tempFile.toPath(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        log.info("文件已保存到本地路径：{}", fullPath);
    }

    @Override
    public String previewFileUrl(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.BAD_REQUEST, "文件路径不能为空");
        }
        // 将 file/ 开头替换为 image/
        if (filePath.startsWith("file/")) {
            filePath = filePath.replaceFirst("file/", "image/");
        }
        return previewBaseUrl +  filePath;
    }

}
