package com.center.framework.storage.file.service;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface StorageFileService {
    /**
     * 根据路径预览文件
     */
    ResponseEntity<ByteArrayResource> previewFile(String filePath);

    /**
     * 上传文件，保存到本地--但是并未保存到数据库
     */
    Long uploadFile(MultipartFile file);

    /**
     *  通过nginx转发实现预览文件
     */
    String previewFileUrl(String filePath);
}
