<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.center</groupId>
        <artifactId>center-framework-storage</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>center-framework-storage-minio</artifactId>
    <packaging>jar</packaging>
    <description>center-framework-storage-minio</description>

    <dependencies>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-storage-interface</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- MinIO client dependency -->
        <!-- https://mvnrepository.com/artifact/io.minio/minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>
    </dependencies>

</project>
