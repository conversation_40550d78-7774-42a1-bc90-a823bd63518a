package com.center.framework.storage.interfaces.config;

import com.center.framework.storage.interfaces.enums.FileStorageTypeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 文件存储配置类，用于从配置文件中读取存储类型。
 */
@Component
public class FileStorageConfig {

    private final FileStorageTypeEnum storageType;

    /**
     * 构造方法，读取配置文件中的存储类型。
     *
     * @param storageTypeString 配置文件中的存储类型字符串
     */
    public FileStorageConfig(@Value("${storage.type}") String storageTypeString) {
        if (storageTypeString == null || storageTypeString.isEmpty()) {
            throw new IllegalArgumentException("存储类型未正确配置");
        }
        this.storageType = FileStorageTypeEnum.valueOf(storageTypeString.toUpperCase());
    }

    public FileStorageTypeEnum getStorageType() {
        return storageType;
    }
}
