package com.center.framework.storage.interfaces.template;

public class FilePathBuilder {

    private static final String DEFAULT_BASE_PATH = "/knowledge"; // 默认的基础路径
    private final String basePath;

    // 无参构造方法，使用默认路径
    public FilePathBuilder() {
        this.basePath = DEFAULT_BASE_PATH;
    }

    // 有参构造方法，允许自定义 basePath
    public FilePathBuilder(String basePath) {
        this.basePath = basePath != null ? basePath : DEFAULT_BASE_PATH;
    }

    /**
     * 动态构建路径，默认基础路径为 basePath，可以传入 tenantId, kbId, fileId 等可选参数
     *
     * @param pathSegments 可变数量的路径片段
     * @return 构建的路径字符串
     */
    public String buildFilePath(String... pathSegments) {
        StringBuilder pathBuilder = new StringBuilder(basePath);

        for (String segment : pathSegments) {
            if (segment != null && !segment.isEmpty()) {
                pathBuilder.append("/").append(segment);
            }
        }

        return pathBuilder.toString();
    }



    /**
     * 动态构建路径，默认基础路径为 basePath，可以传入 tenantId, kbId, fileId 等可选参数
     *
     * @param pathSegments 可变数量的路径片段
     * @return 构建的路径字符串
     */
    public String buildPathForPreview(String... pathSegments) {
        StringBuilder pathBuilder = new StringBuilder(basePath);
        for (String segment : pathSegments) {
            if (segment != null && !segment.isEmpty()) {
                pathBuilder.append(segment);
            }
        }
        return pathBuilder.toString();
    }
}
