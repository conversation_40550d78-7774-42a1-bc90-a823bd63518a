package com.center.framework.storage.interfaces.enums;


import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * 文件存储类型的枚举类，表示不同的存储类型，例如 HDFS、MinIO 等。
 */
@AllArgsConstructor
public enum FileStorageTypeEnum implements IEnumerate<String> {

    HDFS("HDFS", "Hadoop"),
    MINIO("MINIO", "MinIO"),
    FTP("FTP", "FTP"),
    SFTP("SFTP", "SFTP"),
    OSS("OSS", "Object Storage Service");

    private final String value;
    private final String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
