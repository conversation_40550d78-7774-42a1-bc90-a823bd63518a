package com.center.framework.storage.interfaces.template;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

@Component
public class FileTemplate {

    public String getFileExtension(String path) {
        int lastDotIndex = path.lastIndexOf(".");
        if (lastDotIndex > 0) {
            return path.substring(lastDotIndex + 1);
        }
        return "";
    }


    /**
     * 根据文件路径获取文件类型并返回响应头
     * @param descPath
     * @return headers
     */
    public HttpHeaders getHeaders(String descPath) {
        String fileExtension = getFileExtension(descPath);
        HttpHeaders headers = new HttpHeaders();
        switch (fileExtension) {
            case "png":
                headers.setContentType(MediaType.IMAGE_PNG);
                break;
            case "gif":
                headers.setContentType(MediaType.IMAGE_GIF);
                break;
            case "jpeg":
                headers.setContentType(MediaType.IMAGE_JPEG);
                break;
            case "jpg":
                headers.setContentType(MediaType.IMAGE_JPEG);
                break;
            case "pdf":
                headers.setContentType(MediaType.APPLICATION_PDF);
                break;
            case "txt":
                headers.setContentType(MediaType.TEXT_PLAIN);
                break;
            default:
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                break;
        }
        return headers;
    }

}

