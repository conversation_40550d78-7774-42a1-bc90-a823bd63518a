package com.center.framework.storage.interfaces;

import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.storage.interfaces.pojo.FileMetadata;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.InvalidPathException;
import java.util.List;

/**
 * 文件存储服务接口，定义通用的文件操作方法。
 */
public interface FileStorageService {


    /**
     * 删除根目录
     * @param rootPath 文件夹的根路径
     * @throws IOException 如果删除目录操作中出现IO错误，将抛出服务异常
     */
    void deleteDirectory(String rootPath) throws IOException,InvalidPathException;
    /**
     * 通过路径上传文件
     * @param sourcePath-源文件路径，包括文件名和扩展名
     * @param targetPath-上传目标路径，包括文件名和扩展名
     * @Author: panghanzhong
     */
    void uploadFile(String sourcePath,String targetPath);
    /**
     * 文件下载到本地临时文件
     * @param originPath-源文件路径，包括文件名和扩展名
     * @param tempFilePath-临时文件路径，包括文件名和扩展名
     */
    void copyToLocal(String originPath, String tempFilePath);

    /**
     * 获取指定路径下的所有文件列表
     * @param dirPath 文件路径
     * @return 文件列表
     */
    List<String> getFileList(String dirPath);
    /**
     * 用于删除文件及其所在的文件夹
     * @param filePaths 文件的路径列表
     * @throws IOException 如果删除文件操作中出现IO错误，将抛出服务异常
     */
    void deleteParentDirectoryOfFilePath(List<String> filePaths) throws IOException, InvalidPathException;

    /**
     * 使用流上传文件到指定路径。
     *
     * @param targetPath  上传的目标路径，包括文件名和扩展名
     * @param inputStream 文件输入流
     * @param overwrite 是否覆盖同名文件
     * @throws IOException 如果发生 IO 异常
     */
    void uploadFile(String targetPath, InputStream inputStream,Boolean overwrite) throws IOException;

    /**
     * 删除指定路径的文件。
     *
     * @param filePath 要删除的文件路径
     * @throws IOException 如果发生 IO 异常
     */
    void deleteFile(String filePath) throws IOException;

    /**
     * 下载指定路径的文件。
     *
     * @param filePath 要下载的文件路径
     * @return 文件的输入流
     * @throws IOException 如果发生 IO 异常
     */
    InputStream downloadFile(String filePath) throws IOException;

    /**
     * 获取指定路径文件的元数据。
     *
     * @param filePath 文件路径
     * @return 文件元数据对象
     * @throws IOException 如果发生 IO 异常
     */
    FileMetadata getFileMetadata(String filePath) throws IOException;

    /**
     * @param filePath
     * @return 流式文件-ByteArrayResource
     * @throws IOException
     */
    ResponseEntity<ByteArrayResource> previewFile(String filePath) throws IOException;

    /**
     * 将指定路径的文件转换为 PDF 格式。
     * @param originPath- 源文件路径
     * @return 转换后的 PDF 文件路径
     */
    String convertPdf(String originPath);
    /**
     * 获取指定路径下的所有文件列表
     * @param path 文件路径
     * @return 文件列表
     */
    List<FileListResp> getAllFiles(String path);

    /**
     * 从云端复制文件到云端
     * @param filePath-源文件路径
     * @param dfsPath-目标文件路径
     */
    void copyFromCloudToCloud(String filePath, String dfsPath);
}
