package com.center.cache.memory;

import cn.hutool.cache.CacheUtil;
import com.center.cache.interfaces.Cache;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @description: set实现类
 * @date 2024/11/13 9:11
 */
@Component
public class MemorySetCache implements Cache<String, Set<String>> {
    private MemorySetCache(){

    }
    private static final MemorySetCache INSTANCE = new MemorySetCache();

    public static MemorySetCache getInstance() {
        return INSTANCE;
    }

    //由于hutool的cache不支持不限时间并且不限数量的缓存，所以这里设定一个长时间（100年）的缓存
    private cn.hutool.cache.Cache<String, cn.hutool.cache.Cache<String, Set<String>>> setCache = CacheUtil.newTimedCache(3155760000000L);


    @Override
    public Set<String> get(String key) {
        cn.hutool.cache.Cache<String, Set<String>> cache = setCache.get(key);
        if (cache == null) {
            return null;
        }
        return cache.get(key);
    }

    @Override
    public void put(String key, Set<String> value) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, Set<String>> cache = CacheUtil.newLRUCache(100);
        cache.put(key, value);
        setCache.put(key, cache);
    }

    @Override
    public void put(String key, Set<String> value, Long ttlTime) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, Set<String>> cache = CacheUtil.newTimedCache(ttlTime);
        cache.put(key, value);
        setCache.put(key, cache);
    }

    @Override
    public void remove(String key) {
        cn.hutool.cache.Cache<String, Set<String>> cache = setCache.get(key);
        if(cache != null){
            cache.remove(key);
            setCache.remove(key);
        }
    }
}
