package com.center.cache.memory;

import cn.hutool.cache.CacheUtil;
import com.center.cache.interfaces.Cache;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @description: string实现类
 * @date 2024/11/12 15:24
 */
@Component
public class MemoryStrCache implements Cache<String, String> {
    private MemoryStrCache(){

    }
    private static final MemoryStrCache INSTANCE = new MemoryStrCache();

    public static MemoryStrCache getInstance() {
        return INSTANCE;
    }

    //由于hutool的cache不支持不限时间并且不限数量的缓存，所以这里设定一个长时间（100年）的缓存
    private cn.hutool.cache.Cache<String, cn.hutool.cache.Cache<String, String>> stringCache = CacheUtil.newTimedCache(3155760000000L);


    @Override
    public String get(String key) {
        cn.hutool.cache.Cache<String, String> cache = stringCache.get(key);
        if (cache == null) {
            return null;
        }
        return cache.get(key);
    }

    @Override
    public void put(String key, String value) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, String> cache = CacheUtil.newLRUCache(100);
        cache.put(key, value);
        stringCache.put(key, cache);
    }

    @Override
    public void put(String key, String value, Long ttlTime) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, String> cache = CacheUtil.newTimedCache(ttlTime);
        cache.put(key, value);
        stringCache.put(key, cache);
    }

    @Override
    public void remove(String key) {
        cn.hutool.cache.Cache<String, String> cache = stringCache.get(key);
        if(cache != null){
            cache.remove(key);
            stringCache.remove(key);
        }
    }
}
