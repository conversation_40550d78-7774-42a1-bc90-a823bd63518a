package com.center.emergency.biz.aisearch.pojo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新会话标题请求参数
 */
@Data
@Schema(description = "更新会话标题请求参数")
public class UpdateSessionTitleRequest {
    
    /**
     * 新标题
     */
    @Schema(description = "新标题", required = true, example = "关于人工智能的讨论")
    @NotBlank(message = "标题不能为空")
    @Size(max = 500, message = "标题长度不能超过500个字符")
    private String title;
} 