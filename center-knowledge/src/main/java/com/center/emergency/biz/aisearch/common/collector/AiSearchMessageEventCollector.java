package com.center.emergency.biz.aisearch.common.collector;

import com.center.emergency.biz.aisearch.common.enumeration.AiSearchEventType;
import com.center.emergency.biz.aisearch.common.persistence.AiSearchParsedEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * AI搜索智能事件收集器，用于收集搜索过程中的所有事件
 * 会自动合并连续的MESSAGE事件为一个完整的回答
 */
@Slf4j
public class AiSearchMessageEventCollector {
    
    private final List<AiSearchParsedEvent> mergedEvents = new ArrayList<>();
    private final StringBuilder currentMessageBuilder = new StringBuilder();
    private final StringBuilder totalMessageBuilder = new StringBuilder();
    
    /**
     * 添加事件 - 智能合并连续的MESSAGE事件
     */
    public void addEvent(AiSearchParsedEvent event) {
        if (event.getEventType() == AiSearchEventType.MESSAGE && event.getContent() != null) {
            // 累积MESSAGE内容
            currentMessageBuilder.append(event.getContent());
            totalMessageBuilder.append(event.getContent());
            log.info("累积MESSAGE内容: {}, 当前长度: {}", event.getContent(), currentMessageBuilder.length());
        } else {
            // 非MESSAGE事件，先处理之前累积的MESSAGE
            flushCurrentMessage();
            
            // 添加当前非MESSAGE事件
            mergedEvents.add(event);
            log.info("添加非MESSAGE事件: {}, 当前合并事件总数: {}", event.getEventType(), mergedEvents.size());
        }
    }
    
    /**
     * 将当前累积的MESSAGE内容作为一个事件添加到合并列表
     */
    private void flushCurrentMessage() {
        if (currentMessageBuilder.length() > 0) {
            AiSearchParsedEvent messageEvent = new AiSearchParsedEvent();
            messageEvent.setEventType(AiSearchEventType.MESSAGE);
            messageEvent.setContent(currentMessageBuilder.toString());
            mergedEvents.add(messageEvent);
            
            log.info("合并MESSAGE事件，内容长度: {}, 当前合并事件总数: {}", 
                     currentMessageBuilder.length(), mergedEvents.size());
            
            // 清空当前MESSAGE累积器
            currentMessageBuilder.setLength(0);
        }
    }
    
    /**
     * 获取所有合并后的事件（用于数据库存储）
     */
    public List<AiSearchParsedEvent> getMergedEvents() {
        // 最终处理剩余的MESSAGE内容
        flushCurrentMessage();
        return new ArrayList<>(mergedEvents);
    }
    
    /**
     * 获取完整的回答内容
     */
    public String getTotalMessage() {
        return totalMessageBuilder.toString();
    }
    
    /**
     * 获取当前状态信息（用于调试）
     */
    public String getStatusInfo() {
        return String.format("合并事件数: %d, 当前MESSAGE长度: %d, 总消息长度: %d", 
                           mergedEvents.size(), currentMessageBuilder.length(), totalMessageBuilder.length());
    }
    
    /**
     * 清空所有数据
     */
    public void clear() {
        mergedEvents.clear();
        currentMessageBuilder.setLength(0);
        totalMessageBuilder.setLength(0);
    }
} 