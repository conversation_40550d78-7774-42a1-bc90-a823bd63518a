package com.center.emergency.biz.chat.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "快捷问题生成请求参数")
public class QuickQuestionsReq {

    @NotBlank(message = "机器人名称不能为空")
    @Schema(description = "机器人名称")
    private String robotName;

    @Schema(description = "机器人描述")
    private String description;
}
