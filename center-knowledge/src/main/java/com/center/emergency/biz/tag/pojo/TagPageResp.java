package com.center.emergency.biz.tag.pojo;

import com.center.emergency.common.enumeration.TagTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TagPageResp extends TagBase{

    @Schema(description = "主键id", example = "1")
    private Long id;

    //操作人
    @Schema(description = "操作人",example = "admin")
    private String  displayName;

    //创建时间
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    //引用次数
    @Schema(description = "标签引用次数",example = "3")
    Long fileTagCount;

    @Schema(description = "标签类型", example = "CUSTOM")
    TagTypeEnum tagType;

}
