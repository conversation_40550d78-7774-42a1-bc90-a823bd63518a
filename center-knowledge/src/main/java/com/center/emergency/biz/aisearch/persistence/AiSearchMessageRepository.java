package com.center.emergency.biz.aisearch.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AI搜索消息Repository
 */
@Repository
public interface AiSearchMessageRepository extends JoinFetchCapableQueryDslJpaRepository<AiSearchMessageModel, Long> {
    
    /**
     * 根据会话ID查询消息列表，按创建时间排序
     */
    List<AiSearchMessageModel> findBySessionIdAndTenantIdOrderByCreateTimeAsc(Long sessionId, Long tenantId);
    
    /**
     * 根据会话ID和角色查询消息列表
     */
    List<AiSearchMessageModel> findBySessionIdAndTenantIdAndRoleOrderByCreateTimeAsc(Long sessionId, Long tenantId, String role);
    
    /**
     * 删除指定会话的所有消息
     */
    @Modifying
    void deleteBySessionIdAndTenantId(Long sessionId, Long tenantId);
    
    /**
     * 统计会话的消息数量
     */
    long countBySessionIdAndTenantId(Long sessionId, Long tenantId);
} 