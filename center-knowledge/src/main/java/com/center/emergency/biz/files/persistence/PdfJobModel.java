package com.center.emergency.biz.files.persistence;

import com.center.emergency.common.enumeration.PdfStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "center_pdf_job")
public class PdfJobModel extends BaseTenantModel {

    @Schema(description = "pdf文件名")
    @Column(name = "pdf_url")
    private String pdfUrl;

    @Schema(description = "状态")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private PdfStatusEnum status;
}
