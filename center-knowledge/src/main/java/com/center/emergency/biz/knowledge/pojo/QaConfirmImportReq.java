package com.center.emergency.biz.knowledge.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * QA确认导入请求类
 * <AUTHOR>
 */
@Data
public class QaConfirmImportReq {
    
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private Long kbId;
    
    @Schema(description = "QA名称，默认为文件名")
    private String qaName;
    
    @Schema(description = "要导入的QA对列表")
    private List<QAPair> faqs;
}
