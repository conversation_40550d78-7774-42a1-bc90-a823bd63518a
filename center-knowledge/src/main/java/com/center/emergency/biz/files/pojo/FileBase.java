package com.center.emergency.biz.files.pojo;

import com.center.emergency.biz.tag.pojo.TagUpdateReq;
import com.center.emergency.common.enumeration.FileStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文件基础类，定义文件模块的通用字段
 * <AUTHOR>
 */
@Data
public class FileBase {

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    @NotNull(message = "知识库ID不能为空")
    private Long kbId;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    @NotNull(message = "文件名称不能为空")
    @Length(max = 100, message = "文件名称长度不能超过100个字符")
    private String fileName;



    /**
     * HDFS路径
     */
    @Schema(description = "HDFS路径")
    private String hdfsPath;

    @Schema(description = "pdf在HDFS路径")
    private String pdfHdfsPath;

    /**
     * 文件状态
     */
    @Schema(description = "文件状态")
    private FileStatusEnum status;

    /**
     * 模型文件库的文件ID
     */
    @Schema(description = "模型文件库的文件ID")
    private Long aiFilebFileId;



}