package com.center.emergency.biz.knowledge.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * QA预览响应类
 * 简化的前端友好响应结构
 * <AUTHOR>
 */
@Data
public class QaPreviewResp {

    @Schema(description = "预检查是否成功")
    private Boolean success;

    @Schema(description = "预检查结果消息")
    private String message;

    @Schema(description = "知识库ID")
    private Long kbId;

    @Schema(description = "知识库名称")
    private String kbName;

    @Schema(description = "统计信息")
    private PreviewStats stats;

    @Schema(description = "预览项目列表（统一结构）")
    private List<QaPreviewItem> items;

    @Schema(description = "是否可以导入")
    private Boolean canImport;
    
    @Data
    public static class PreviewStats {
        @Schema(description = "新增数量")
        private Integer newCount = 0;
        
        @Schema(description = "重复数量")
        private Integer duplicateCount = 0;
        
        @Schema(description = "错误数量")
        private Integer errorCount = 0;
        
        @Schema(description = "总数量")
        private Integer totalCount = 0;
    }
    
    @Data
    public static class QaPreviewItem {
        @Schema(description = "行号索引")
        private Integer rowNumber;
        
        @Schema(description = "导入问题")
        private String question;
        
        @Schema(description = "导入答案")
        private String answer;
        
        @Schema(description = "状态：NEW/DUPLICATE/ERROR")
        private String status;
        
        @Schema(description = "状态描述")
        private String statusDesc;
        
        @Schema(description = "错误信息（仅ERROR状态）")
        private String errorMsg;
        
        @Schema(description = "原问题（仅DUPLICATE状态）")
        private String originalQuestion;
        
        @Schema(description = "原答案（仅DUPLICATE状态）")
        private String originalAnswer;
    }
}
