package com.center.emergency.biz.model.pojo;

import com.center.emergency.common.enumeration.ModelProviderEnum;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.utils.datetime.DateTimeUtils;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SelectListLargeModelResp {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "服务商")
    private ModelProviderEnum provider;

    @Schema(description = "模型显示名称")
    private String modelDisplayName;

    @Schema(description = "模型名称")
    private String modelName;
}
