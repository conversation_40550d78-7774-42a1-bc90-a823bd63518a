package com.center.emergency.biz.chat.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ChatAnswerEventRepository extends JoinFetchCapableQueryDslJpaRepository<ChatAnswerEventModel, Long> {

    /**
     * 根据回答ID查询所有事件，按顺序排列
     */
    List<ChatAnswerEventModel> findByAnswerIdOrderBySequenceOrder(Long answerId);

    /**
     * 根据多个回答ID查询所有事件，按回答ID和顺序排列
     */
    List<ChatAnswerEventModel> findByAnswerIdInOrderByAnswerIdAscSequenceOrderAsc(List<Long> answerIds);

    /**
     * 根据回答ID查询参与上下文的事件
     */
    List<ChatAnswerEventModel> findByAnswerIdAndIncludeInContextTrueOrderBySequenceOrder(Long answerId);

    /**
     * 根据回答ID和事件类型查询事件
     */
    List<ChatAnswerEventModel> findByAnswerIdAndEventType(Long answerId, String eventType);

    /**
     * 删除指定回答的所有事件
     * 使用@Modifying注解标记这是一个修改操作
     * 使用@Query注解提供明确的删除SQL
     */
    @Modifying
    @Query("DELETE FROM ChatAnswerEventModel c WHERE c.answerId = :answerId")
    void deleteByAnswerId(@Param("answerId") Long answerId);

    void deleteByAnswerIdIn(List<Long> answerIds);
}