package com.center.emergency.biz.modelgroup.service;

import cn.hutool.core.lang.Snowflake;
import com.center.emergency.biz.model.persistence.LargeModel;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.model.service.LargeModelServiceImpl;
import com.center.emergency.biz.modelgroup.persistence.*;
import com.center.emergency.biz.modelgroup.pojo.*;
import com.center.emergency.common.enumeration.ModelRoleEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.*;

@Slf4j
@Service
public class ModelGroupServiceImpl implements ModelGroupService {

    @Resource
    Snowflake snowflake;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private ModelGroupRepository modelGroupRepository;

    @Autowired
    private ModelGroupMemberRepository modelGroupMemberRepository;

    @Autowired
    private LargeModelServiceImpl largeModelService;

    @Override
    @Transactional
    public Long createModelGroup(ModelGroupCreateReq req) {
        //1. 存储模型组相关内容
        ModelGroup modelGroup = OrikaUtils.convert(req, ModelGroup.class);
        //1.1 模型组名称判重
        boolean existsByGroupNameAndTenantId = modelGroupRepository.existsByGroupNameAndTenantId(req.getGroupName(), LoginContextHolder.getLoginUserTenantId());
        boolean existsByGroupNameAndSourceType = modelGroupRepository.existsByGroupNameAndSourceType(req.getGroupName(), SourceTypeEnum.SYSTEM);
        if (existsByGroupNameAndTenantId  || existsByGroupNameAndSourceType) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "模型组名称重复，请更换");
        }
        //1.2 判断 thinkModelId 是否在 normalModelList 中
        if (!(req.getThinkModelId() == null || req.getNormalModelList().contains(req.getThinkModelId()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "深度思考模型必须属于已选模型");
        }

        // 1.3 判断 adaptiveModelId 是否全部在 normalModelList 中
        if (!(req.getAdaptiveModelId() == null || req.getNormalModelList().contains(req.getAdaptiveModelId()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "自适应模型必须全部属于已选模型");
        }

        Long modelGroupId = snowflake.nextId();
        modelGroup.setId(modelGroupId);
        modelGroup.setSourceType(SourceTypeEnum.CUSTOM);
        modelGroupRepository.save(modelGroup);

        // 2. 处理模型组成员
        List<ModelGroupMember> allMembers = new ArrayList<>();

        // 2.1 常规模型成员
        buildMembers(allMembers, req.getNormalModelList(), modelGroupId, ModelRoleEnum.NORMAL);

        // 2.2 自适应模型成员（只有一个）
        if (req.getAdaptiveModelId() != null) {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(modelGroupId);
            member.setLargeModelId(req.getAdaptiveModelId());
            member.setRole(ModelRoleEnum.ROUTER);
            allMembers.add(member);
        }

        // 2.3 深度思考模型成员（只有一个）
        if (req.getThinkModelId() != null) {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(modelGroupId);
            member.setLargeModelId(req.getThinkModelId());
            member.setRole(ModelRoleEnum.DEEP_THINK);
            allMembers.add(member);
        }

        // 3. 批量保存所有成员
        if (!allMembers.isEmpty()) {
            modelGroupMemberRepository.saveAll(allMembers);
        }
        return modelGroupId;
    }

    private void buildMembers(List<ModelGroupMember> collector, List<Long> modelIds, Long groupId, ModelRoleEnum role) {
        if (CollectionUtils.isEmpty(modelIds)) return;

        List<ModelGroupMember> members = modelIds.stream().filter(Objects::nonNull).map(modelId -> {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(groupId);
            member.setLargeModelId(modelId);
            member.setRole(role);
            return member;
        }).collect(Collectors.toList());

        collector.addAll(members);
    }

    @Override
    @Transactional
    public Long deleteModelGroup(Long id) {
        // 1. 校验模型组是否存在
        ModelGroup modelGroup = modelGroupRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "模型组不存在"));

        // 2. 判断操作权限
        if (SourceTypeEnum.SYSTEM.equals(modelGroup.getSourceType())){
            if (!largeModelService.isSuperAdmin(LoginContextHolder.getLoginUserId())){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "系统内置模型组不允许删除");
            }
        }else if (!modelGroup.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "没有删除模型组权限");
        }

        //TODO 2. 校验是否有助手使用该模型组

        // 3. 删除模型组成员表中的记录
        modelGroupMemberRepository.deleteByModelGroupId(id);

        // 4. 删除模型组表记录
        modelGroupRepository.deleteById(id);

        // 5. 返回删除成功的ID
        return id;
    }

    @Override
    @Transactional
    public ModelGroupResp updateModelGroup(ModelGroupUpdateReq req) {
        Long groupId = req.getModelGroupId();
        // 1. 校验模型组是否存在
        ModelGroup modelGroup = modelGroupRepository.findById(groupId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "模型组不存在"));

        // 1.1. 判断操作权限
        if (SourceTypeEnum.SYSTEM.equals(modelGroup.getSourceType())){
            if (!largeModelService.isSuperAdmin(LoginContextHolder.getLoginUserId())){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "系统内置模型组不允许修改");
            }
        }else if (!modelGroup.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "没有更新该模型组权限");
        }


        //1.2 模型组名称判重
        if (!modelGroup.getGroupName().equals(req.getGroupName())) {
            boolean isExist = modelGroupRepository.existsByGroupNameAndTenantId(req.getGroupName(), LoginContextHolder.getLoginUserTenantId());
            if (isExist) {
                throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "模型组名称重复，请更换");
            }
        }

        //1.3 判断 thinkModelId 是否在 normalModelList 中
        if (!(req.getThinkModelId() == null || req.getNormalModelList().contains(req.getThinkModelId()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "深度思考模型必须属于已选模型");
        }

        // 1.4 判断 adaptiveModelId 是否全部在 normalModelList 中
        if (!(req.getAdaptiveModelId() == null || req.getNormalModelList().contains(req.getAdaptiveModelId()))) {
            throw ServiceExceptionUtil.exception(PARAM_MISMATCH, "自适应模型必须全部属于已选模型");
        }

        // 2. 更新模型组属性
        modelGroup.setGroupName(req.getGroupName());
        modelGroupRepository.save(modelGroup);

        // 3. 清除原有成员
        modelGroupMemberRepository.deleteByModelGroupId(groupId);

        // 4. 构建新成员列表
        List<ModelGroupMember> allMembers = new ArrayList<>();

        // 4.1 普通模型
        buildMembers(allMembers, req.getNormalModelList(), groupId, ModelRoleEnum.NORMAL);
        // 4.2 自适应模型（只有一个）
        if (req.getAdaptiveModelId() != null) {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(groupId);
            member.setLargeModelId(req.getAdaptiveModelId());
            member.setRole(ModelRoleEnum.ROUTER);
            allMembers.add(member);
        }
        // 4.3 深度思考模型（只有一个）
        if (req.getThinkModelId() != null) {
            ModelGroupMember member = new ModelGroupMember();
            member.setModelGroupId(groupId);
            member.setLargeModelId(req.getThinkModelId());
            member.setRole(ModelRoleEnum.DEEP_THINK);
            allMembers.add(member);
        }

        // 批量保存
        if (!allMembers.isEmpty()) {
            modelGroupMemberRepository.saveAll(allMembers);
        }

        // 5. 返回更新后的模型组响应
        ModelGroupResp resp = OrikaUtils.convert(modelGroup, ModelGroupResp.class);
        // 如果需要把成员列表也塞到 resp 中，可以另外查询并转换
        List<Long> normalIds = req.getNormalModelList();
        resp.setNormalModelList(normalIds);
        resp.setAdaptiveModelId(req.getAdaptiveModelId());
        resp.setThinkModelId(req.getThinkModelId());
        return resp;
    }

    @Override
    public PageResult<ModelGroupPageResp> pageModelGroup(ModelGroupPageReq req) {
        // 1. 构建分页条件
        Pageable pageable = PageRequest.of(req.getPageNo() - 1, req.getPageSize());

        // 2. 构建查询条件
        QModelGroup qModelGroup = QModelGroup.modelGroup;
        QModelGroupMember qModelGroupMember = QModelGroupMember.modelGroupMember;
        QLargeModel qLargeModel = QLargeModel.largeModel;
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(req.getGroupName())) {
            builder.and(qModelGroup.groupName.contains(req.getGroupName()));
        }
        builder.and(qModelGroup.tenantId.eq(LoginContextHolder.getLoginUserTenantId()).or(qModelGroup.sourceType.eq(SourceTypeEnum.SYSTEM)));

        // 3. 执行查询
        JPQLQuery jpqlQuery = jpaQueryFactory.select((Projections.bean(
                        ModelGroupPageResp.class,
                        qModelGroup.id,
                        qModelGroup.groupName,
                        qModelGroup.sourceType
                )))
                .from(qModelGroup)
                .where(builder)
                .orderBy(new CaseBuilder()
                        .when(qModelGroup.sourceType.eq(SourceTypeEnum.SYSTEM)).then(1)
                        .otherwise(2).asc(),
                        qModelGroup.updateTime.desc()) // 系统内置模型组放最前面，其余按 updateTime 倒序排列
                .offset(pageable.getOffset()) // 使用 pageable 的偏移量
                .limit(pageable.getPageSize()); // 设置每页记录数

        Long total = jpqlQuery.fetchCount();
        List<ModelGroupPageResp> resultList = jpqlQuery.fetch();

        // 4. 查询该模型组对应的成员模型
        // 4.1 收集所有的模型组ID
        List<Long> groupIDs = new ArrayList<>();
        if (total > 0) {
            groupIDs = resultList.stream().map(ModelGroupPageResp::getId).collect(Collectors.toList());
        }
        // 4.2 批量查询所有的成员及模型名称
        List<LargeModelInfoDTO> largeModelInfoList = jpaQueryFactory.select((Projections.bean(
                        LargeModelInfoDTO.class,
                        qLargeModel.id.as("largeModelId"),
                        qLargeModel.modelDisplayName.as("largeModelDisplayName"),
                        qModelGroupMember.role,
                        qModelGroupMember.modelGroupId
                )))
                .from(qModelGroupMember)
                .join(qLargeModel).on(qModelGroupMember.largeModelId.eq(qLargeModel.id))
                .where(qModelGroupMember.modelGroupId.in(groupIDs))
                .fetch();

        // 4.3 按 modelGroupId 分组
        Map<Long, List<LargeModelInfoDTO>> grouped = largeModelInfoList.stream()
                .collect(Collectors.groupingBy(LargeModelInfoDTO::getModelGroupId));

        // 4.4 将分组后的模型信息填充到分页结果中
        for (ModelGroupPageResp resp : resultList) {
            List<LargeModelInfoDTO> infos = grouped.get(resp.getId());
            resp.setModelList(infos != null ? infos : Collections.emptyList());
        }

        //  构建分页响应并返回
        return PageResult.of(resultList, total);
    }

    @Override
    public ModelGroupDetailResp getModelGroup(Long id) {
        // 1. 查询模型组基本信息
        ModelGroup modelGroup = modelGroupRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "模型组不存在"));

        // 1.1. 判断操作权限
        if (SourceTypeEnum.SYSTEM.equals(modelGroup.getSourceType())){
            if (!largeModelService.isSuperAdmin(LoginContextHolder.getLoginUserId())){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "系统内置模型组无法查看");
            }
        }else if (!modelGroup.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "没有查看该模型组权限");
        }

        // 2. 查询该模型组的所有成员
        List<ModelGroupMember> members = modelGroupMemberRepository.findByModelGroupId(id);

        // 3. 收集所有模型 ID
        Set<Long> modelIds = members.stream()
                .map(ModelGroupMember::getLargeModelId)
                .collect(Collectors.toSet());

        // 4. 查询所有大模型id和名称
        QLargeModel qLargeModel = QLargeModel.largeModel;
        List<LargeModelBase> LargeModelInfoList = jpaQueryFactory
                .select((Projections.bean(
                        LargeModelBase.class,
                        qLargeModel.id.as("largeModelId"),
                        qLargeModel.modelDisplayName.as("largeModelDisplayName")
                )))
                .from(qLargeModel)
                .where(qLargeModel.id.in(modelIds))
                .fetch();
        Map<Long, String> modelIdNameMap = LargeModelInfoList
                .stream()
                .collect(Collectors.toMap(
                        LargeModelBase::getLargeModelId,
                        LargeModelBase::getLargeModelDisplayName
                ));


        // 5. 构造返回数据
        List<LargeModelBase> normalModelList = new ArrayList<>();
        Long adaptiveModelId = null;
        Long thinkModelId = null;

        for (ModelGroupMember member : members) {
            Long modelId = member.getLargeModelId();
            String modelDisplayName = modelIdNameMap.get(modelId);
            LargeModelBase info = new LargeModelBase();
            info.setLargeModelId(modelId);
            info.setLargeModelDisplayName(modelDisplayName);

            switch (member.getRole()) {
                case NORMAL:
                    normalModelList.add(info);
                    break;
                case ROUTER:
                    adaptiveModelId = modelId;
                    break;
                case DEEP_THINK:
                    thinkModelId = modelId;
                    break;
            }
        }

        // 6. 封装响应
        ModelGroupDetailResp resp = new ModelGroupDetailResp();
        resp.setModelGroupId(modelGroup.getId());
        resp.setGroupName(modelGroup.getGroupName());
        resp.setSourceType(modelGroup.getSourceType());
        resp.setNormalModelList(normalModelList);
        resp.setAdaptiveModelId(adaptiveModelId);
        resp.setThinkModelId(thinkModelId);

        return resp;
    }

    @Override
    public List<LargeModelBase> listLargeModel() {
        QLargeModel qLargeModel = QLargeModel.largeModel;
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        // 查询条件：租户模型 或 系统模型
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qLargeModel.tenantId.eq(tenantId).or(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM)));

        List<LargeModelBase> modelsList = jpaQueryFactory.select((Projections.bean(
                        LargeModelBase.class,
                        qLargeModel.id.as("largeModelId"),
                        qLargeModel.modelDisplayName.as("largeModelDisplayName"),
                        qLargeModel.sourceType
                ))).
                from(qLargeModel)
                .where(builder)
                .orderBy(new CaseBuilder()
                                .when(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM)).then(1)
                                .otherwise(2).asc(),
                        qLargeModel.updateTime.desc()) // 系统内置模型放最前面，其余按 updateTime 倒序排列
                .fetch();

        return OrikaUtils.convertList(modelsList, LargeModelBase.class);
    }


    @Override
    public List<ModelGroupBase> listModelGroup() {
        QModelGroup qModelGroup = QModelGroup.modelGroup;
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        // 查询条件：租户模型组 或 系统模型组
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qModelGroup.tenantId.eq(tenantId).or(qModelGroup.sourceType.eq(SourceTypeEnum.SYSTEM)));

        List<ModelGroupBase> modelGroupList = jpaQueryFactory.select((Projections.bean(
                        ModelGroupBase.class,
                        qModelGroup.id.as("modelGroupId"),
                        qModelGroup.groupName,
                        qModelGroup.sourceType
                ))).
                from(qModelGroup)
                .where(builder)
                .orderBy(new CaseBuilder()
                                .when(qModelGroup.sourceType.eq(SourceTypeEnum.SYSTEM)).then(1)
                                .otherwise(2).asc(),
                        qModelGroup.updateTime.desc()) // 系统内置模型放最前面，其余按 updateTime 倒序排列
                .fetch();

        return OrikaUtils.convertList(modelGroupList, ModelGroupBase.class);
    }


    @Override
    public boolean isModelGroupExists(Long modelGroupId) {
        if (modelGroupId == null) {
            return false;
        }
        return modelGroupRepository.existsById(modelGroupId);
    }

    @Override
    public List<Map<String, Object>> buildModelGroupConfig(Long modelGroupId) {
        log.info("开始构建模型组配置，modelGroupId: {}", modelGroupId);

        if (modelGroupId == null) {
            log.info("模型组ID为空，返回空配置");
            return Collections.emptyList();
        }

        // 查询模型组成员
        List<ModelGroupMember> members = modelGroupMemberRepository.findByModelGroupId(modelGroupId);
        log.info("查询模型组成员结果: modelGroupId={}, 成员数量={}", modelGroupId, members.size());

        if (members.isEmpty()) {
            log.warn("模型组 {} 没有配置任何模型成员", modelGroupId);
            return Collections.emptyList();
        }

        // 构建模型组配置
        List<Map<String, Object>> modelGroupConfig = new ArrayList<>();

        for (ModelGroupMember member : members) {
            log.debug("处理模型组成员: memberRole={}, largeModelId={}", member.getRole(), member.getLargeModelId());

            // 查询模型详情
            QLargeModel qModel = QLargeModel.largeModel;
            LargeModel model = jpaQueryFactory
                    .selectFrom(qModel)
                    .where(qModel.id.eq(member.getLargeModelId()))
                    .fetchOne();

            if (model != null) {
                Map<String, Object> modelConfig = new HashMap<>();
                modelConfig.put("model", model.getModelName());
                modelConfig.put("api_base", model.getBaseUrl());
                modelConfig.put("api_key", model.getApiKey());

                // 根据角色设置role字段
                String role = convertModelRole(member.getRole());
                modelConfig.put("role", role);

                // 添加模型参数配置（从数据库查询，不再写死）
                if (model.getTemperature() != null) {
                    modelConfig.put("temperature", model.getTemperature());
                }
                if (model.getTopP() != null) {
                    modelConfig.put("top_p", model.getTopP());
                }
                if (model.getMaxTokens() != null) {
                    modelConfig.put("max_token", model.getMaxTokens());
                }
                if (model.getInputContextLength() != null) {
                    modelConfig.put("api_context_length", model.getInputContextLength());
                }

                modelGroupConfig.add(modelConfig);
                log.info("✅ 添加模型配置: model={}, role={}, api_base={}, temperature={}, top_p={}, max_token={}, api_context_length={}",
                        model.getModelName(), role, model.getBaseUrl(),
                        model.getTemperature(), model.getTopP(), model.getMaxTokens(), model.getInputContextLength());
            } else {
                log.warn("⚠️ 模型不存在: largeModelId={}", member.getLargeModelId());
            }
        }

        log.info("构建模型组配置完成，模型组ID: {}, 模型数量: {}, 配置详情: {}",
                modelGroupId, modelGroupConfig.size(), modelGroupConfig);
        return modelGroupConfig;
    }

    /**
     * 获取系统内置模型组ID
     * @return 系统内置模型组ID，如果不存在则返回null
     */
    public Long getSystemDefaultModelGroupId() {
        QModelGroup qModelGroup = QModelGroup.modelGroup;

        ModelGroup systemModelGroup = jpaQueryFactory
                .selectFrom(qModelGroup)
                .where(qModelGroup.sourceType.eq(SourceTypeEnum.SYSTEM))
                .orderBy(qModelGroup.createTime.asc())
                .fetchFirst();

        if (systemModelGroup != null) {
            log.info("找到系统内置模型组: id={}, name={}", systemModelGroup.getId(), systemModelGroup.getGroupName());
            return systemModelGroup.getId();
        }

        log.warn("未找到系统内置模型组");
        return null;
    }

    @Override
    public Map<String, Object> getSystemDefaultModelConfig() {
        // 查询系统默认模型（第一个系统内置模型）
        QLargeModel qModel = QLargeModel.largeModel;

        LargeModel defaultModel = jpaQueryFactory
                .selectFrom(qModel)
                .where(qModel.sourceType.eq(SourceTypeEnum.SYSTEM))
                .orderBy(qModel.createTime.asc())
                .fetchFirst();

        if (defaultModel != null) {
            Map<String, Object> config = new HashMap<>();
            config.put("model", defaultModel.getModelName());
            config.put("api_base", defaultModel.getBaseUrl());
            config.put("api_key", defaultModel.getApiKey());

            // 添加模型参数配置（从数据库查询，不再写死）
            if (defaultModel.getTemperature() != null) {
                config.put("temperature", defaultModel.getTemperature());
            }
            if (defaultModel.getTopP() != null) {
                config.put("top_p", defaultModel.getTopP());
            }
            if (defaultModel.getMaxTokens() != null) {
                config.put("max_token", defaultModel.getMaxTokens());
            }
            if (defaultModel.getInputContextLength() != null) {
                config.put("api_context_length", defaultModel.getInputContextLength());
            }

            log.info("使用系统默认模型: {}, temperature={}, top_p={}, max_token={}, api_context_length={}",
                    defaultModel.getModelName(), defaultModel.getTemperature(), defaultModel.getTopP(),
                    defaultModel.getMaxTokens(), defaultModel.getInputContextLength());
            return config;
        }

        log.warn("未找到系统默认模型，返回空配置");
        return Collections.emptyMap();
    }

    /**
     * 转换模型角色枚举为字符串
     */
    private String convertModelRole(ModelRoleEnum roleEnum) {
        if (roleEnum == null) {
            return "normal";
        }

        switch (roleEnum) {
            case ROUTER:
                return "router";
            case DEEP_THINK:
                return "reasoner";
            case NORMAL:
            default:
                return "normal";
        }
    }
}
