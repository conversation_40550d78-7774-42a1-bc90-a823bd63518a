package com.center.emergency.biz.datasource.pojo;

import com.center.framework.common.enumerate.SingleAndMultipleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "数据库基础信息")
public class DataSourceBase {
    @NotBlank(message = "数据库名称不能为空")
    @Schema(description = "数据库名称")
    @Size(message = "数据库名称长度不能超过50。",max = 50)
    private String datasourceName;

    @Schema(description = "数据库描述说明")
    @Size(message = "数据库描述说明不能超过50。",max = 50)
    private String datasourceDescription;

    @Schema(description = "数据库类型(SINGLE/MULTIPLE)")
    private SingleAndMultipleEnum datasourceType;

    @Schema(description = "Relation")
    @Size(message = "Relation最长不超过500个字符",max = 500)
    private String relation;
}
