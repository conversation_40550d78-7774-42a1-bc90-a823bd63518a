package com.center.emergency.biz.modelgroup.pojo;

import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ModelGroupResp {

    @Schema(description = "模型组id")
    private Long modelGroupId;

    @Schema(description = "模型组名称")
    private String groupName;

    @Schema(description = "模型组来源", example = "CUSTOM")
    private SourceTypeEnum sourceType;

    @EnumConvert(value = SourceTypeEnum.class, srcFieldName = "sourceType")
    @Schema(description = "模型组来源名称", example = "用户自定义")
    private String sourceTypeName;

    @Schema(description = "模型组的模型id列表")
    private List<Long> normalModelList;

    @Schema(description = "模型组的自适应模型ID")
    private Long adaptiveModelId;

    @Schema(description = "模型组深度思考模型ID")
    private Long thinkModelId;
}
