package com.center.emergency.biz.chat.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;

/**
 * 聊天参数服务
 * 迁移自AgentChatUtils中的参数构建方法
 */
@Service
@Slf4j
public class ChatParameterService {
    
    /**
     * 构建知识库对话参数（基础参数）
     * 迁移自AgentChatUtils.buildRequestParam
     * 
     * @param chatVO 对话VO
     * @param kbIds 知识库ID集合
     * @param fileIds 文件ID列表
     * @param tags 标签列表
     * @param isSimulate 是否模拟（知识库对话场景下固定为TRUE）
     * @return 请求参数映射
     */
    public HashMap<String, Object> buildKnowledgeBaseChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags, Boolean isSimulate) {
        log.info("构建基础请求参数（知识库对话）: chatVO={}, kbIds.size={}, fileIds.size={}, isSimulate={}",
                chatVO.getQuestion(), kbIds.size(), fileIds.size(), isSimulate);

        HashMap<String, Object> param = new HashMap<>();

        // ===== 必填参数 =====
        param.put("kb_ids", kbIds);
        param.put("question", chatVO.getQuestion());
        param.put("user_id", "zyx");
        param.put("streaming", 1);
        param.put("history", null); // 由调用方补充
        param.put("tags_list", tags);

        // ===== 可选参数（算法文档要求的完整参数） =====
        param.put("file_ids", fileIds);
        param.put("user_token", String.valueOf(LoginContextHolder.getLoginUserId()));
        // 移除写死的知识库策略专用参数，这些参数现在从机器人配置中获取：
        // - search_mode: 从机器人的search_mode字段获取（SearchModeEnum）
        // - answer_mode: 从机器人的answer_mode字段获取（AnswerModeEnum）
        // - score_threshold: 从机器人的similarity_threshold字段获取
        // - top_k: 从机器人的max_hits字段获取
        param.put("rerank", 1);
        param.put("only_need_search_results", 0);
        // 移除写死的模型参数，这些参数现在从模型组配置中获取：
        // - temperature: 从模型组中的每个模型配置获取
        // - top_p: 从模型组中的每个模型配置获取
        // - max_token: 从模型组中的每个模型配置获取
        // - api_context_length: 从模型组中的每个模型配置获取（对应input_context_length字段）
        // 移除answer_strategy参数，不再传递给算法接口
        param.put("query_decompose", 1);
        param.put("enable_recommend_questions", 0);

        log.debug("生成的基础请求参数: {}", param);
        return param;
    }
    
    /**
     * 构建机器人对话参数（完整参数）
     * 迁移自AgentChatUtils.newBuildRequestParam
     * 
     * @param chatVO 对话VO
     * @param kbIds 知识库ID集合
     * @param fileIds 文件ID列表
     * @param tags 标签列表
     * @param isSimulate 是否模拟
     * @param modelId 模型ID（用于查询个性化配置）
     * @param jpaQueryFactory JPA查询工厂
     * @return 请求参数映射
     */
    public HashMap<String, Object> buildRobotChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags, Boolean isSimulate, Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("构建完整请求参数（机器人对话/重新回答）: chatVO={}, kbIds.size={}, fileIds.size={}, modelId={}, isSimulate={}", 
                chatVO.getQuestion(), kbIds.size(), fileIds.size(), modelId, isSimulate);

        QRobotModel qRobotModel = QRobotModel.robotModel;
        QLargeModel qLargeModel = QLargeModel.largeModel;

        // ===== 查询个性化模型配置 =====
        RobotModelDTO robotModelDTO;
        if (modelId != null) {
            // 根据指定modelId查询
            robotModelDTO = jpaQueryFactory.select(
                            Projections.bean(
                                    RobotModelDTO.class,
                                    qLargeModel.baseUrl.as("apiBase"),
                                    qLargeModel.apiKey,
                                    qLargeModel.modelName.as("model"),
                                    qLargeModel.maxTokens.as("maxToken"),
                                    qLargeModel.inputContextLength,
                                    qRobotModel.searchMode,
                                    qRobotModel.answerMode,
                                    qRobotModel.similarityThreshold.as("scoreThreshold"),
                                    qLargeModel.temperature,
                                    qLargeModel.topP,
                                    qRobotModel.maxHits.as("topK"),
                                    qRobotModel.systemPrompt.as("customPrompt")
                            ))
                    .from(qRobotModel)
                    .leftJoin(qLargeModel).on(qRobotModel.modelGroupId.eq(qLargeModel.id))
                    .where(qRobotModel.id.eq(chatVO.getRobotId()))
                    .fetchOne();
        }else {
            // 使用系统默认模型
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(qRobotModel.id.eq(chatVO.getRobotId()));
            builder.and(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM));
            robotModelDTO = jpaQueryFactory.select(
                            Projections.bean(
                                    RobotModelDTO.class,
                                    qLargeModel.baseUrl.as("apiBase"),
                                    qLargeModel.apiKey,
                                    qLargeModel.modelName.as("model"),
                                    qLargeModel.maxTokens.as("maxToken"),
                                    qLargeModel.inputContextLength,
                                    qRobotModel.searchMode,
                                    qRobotModel.answerMode,
                                    qRobotModel.similarityThreshold.as("scoreThreshold"),
                                    qLargeModel.temperature,
                                    qLargeModel.topP,
                                    qRobotModel.maxHits.as("topK"),
                                    qRobotModel.systemPrompt.as("customPrompt")
                            ))
                    .from(qRobotModel,qLargeModel)
                    .where(builder)
                    .fetchOne();
        }

        HashMap<String, Object> param = new HashMap<>();
        // 安全地添加非空参数方法
        BiConsumer<String, Object> putIfNotNull = (key, value) -> {
            if (value != null) {
                param.put(key, value);
            }
        };

        // ===== 基础参数 =====
        param.put("kb_ids", kbIds);
        param.put("file_ids", fileIds);
        param.put("question", chatVO.getQuestion());
        param.put("user_id", "zyx");
        param.put("history", null); // 由调用方补充
        
        // ===== 个性化搜索和回答配置（知识库策略专用参数） =====
        // 注意：这些参数现在通过知识库策略的专用方法添加，这里保留是为了向后兼容
        if (robotModelDTO.getSearchMode() != null) {
            param.put("search_mode", Integer.valueOf(robotModelDTO.getSearchMode().getDescription()));
        }
        if (robotModelDTO.getAnswerMode() != null) {
            param.put("answer_mode", Integer.valueOf(robotModelDTO.getAnswerMode().getDescription()));
        }
        putIfNotNull.accept("top_k", robotModelDTO.getTopK());
        putIfNotNull.accept("custom_prompt", StringUtils.isNotBlank(robotModelDTO.getCustomPrompt()) ? robotModelDTO.getCustomPrompt() : null);
        putIfNotNull.accept("score_threshold", robotModelDTO.getScoreThreshold());
        
        // ===== 个性化模型API配置 =====
        param.put("api_base", robotModelDTO.getApiBase());
        param.put("api_key", robotModelDTO.getApiKey());
        param.put("model", robotModelDTO.getModel());
        // 注意：以下参数现在通过模型组配置传递，这里保留是为了向后兼容单模型配置
        putIfNotNull.accept("max_token", robotModelDTO.getMaxToken());
        putIfNotNull.accept("api_context_length", robotModelDTO.getInputContextLength());
        putIfNotNull.accept("temperature", robotModelDTO.getTemperature());
        putIfNotNull.accept("top_p", robotModelDTO.getTopP());

        log.info("传给算法的完整参数{}", param);
        return param;
    }

    /**
     * 根据策略构建参数（未来扩展预留）
     *
     * @param strategy 回答策略
     * @param chatVO 对话VO
     * @param kbIds 知识库ID集合
     * @param fileIds 文件ID列表
     * @param tags 标签列表
     * @param isSimulate 是否模拟
     * @param modelId 模型ID
     * @param jpaQueryFactory JPA查询工厂
     * @return 请求参数映射
     */
    public HashMap<String, Object> buildParametersByStrategy(AnswerStrategyEnum strategy, ChatVO chatVO, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags, Boolean isSimulate, Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("根据策略构建参数: strategy={}, chatVO={}", strategy, chatVO.getQuestion());

        switch (strategy) {
            case KNOWLEDGE_BASE:
                return buildRobotChatParams(chatVO, kbIds, fileIds, tags, isSimulate, modelId, jpaQueryFactory);
            case DATABASE:
                // TODO: 实现数据库策略
                throw new UnsupportedOperationException("DATABASE strategy not implemented yet");
            case MCP:
                // TODO: 实现MCP策略
                throw new UnsupportedOperationException("MCP strategy not implemented yet");
            case FILE:
                // TODO: 实现FILE策略
                throw new UnsupportedOperationException("FILE strategy not implemented yet");
            default:
                throw new IllegalArgumentException("Unsupported strategy: " + strategy);
        }
    }
}
