package com.center.emergency.biz.database.connector;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

public interface DatabaseConnector {
    Connection getConnection() ;
    List getColumns(DatabaseConnector connector,String databaseName,String tableName) ;
    List getTables(String databaseName) ;
    void checkConnection();
    String getSql(String tableName);
    void close();
    String convertDataType(int dataType);
    List getTableNameList(ResultSet tableSet, String databaseName) ;
    List getColumnList(DatabaseConnector connector,ResultSet columnResultSet,ResultSet primaryKeyResultSet) ;

    String getTableDDL(DatabaseConnector connector,String tableName);

    String toString();
    }
