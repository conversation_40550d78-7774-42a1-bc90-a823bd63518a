package com.center.emergency.biz.robot.pojo;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class AssistantCreateReq {

    // 回答策略
    @NotNull(message = "智能助手回答策略不能为空")
    @Schema(description = "回答策略")
    private AnswerStrategyEnum answerStrategy;

    // 智能助手名称
    @NotBlank(message = "智能助手名称不能为空")
    @Size(max = 20, message = "智能助手名称长度不能超过20个字符")
    @Schema(description = "智能助手名称")
    private String robotName;

    // Logo文件的ID
    @Schema(description = "Logo文件的ID")
    private Long logoId;

    // 智能助手描述
    @Schema(description = "描述",example = "这是一个机器人")
    @Size(max = 200, message = "智能助手描述长度不能超过200个字符")
    private String remark;

}
