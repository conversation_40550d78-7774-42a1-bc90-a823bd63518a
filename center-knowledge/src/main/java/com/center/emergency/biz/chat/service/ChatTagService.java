package com.center.emergency.biz.chat.service;

import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 聊天标签服务
 * 迁移自AgentChatUtils中的标签提取方法
 */
@Service
@Slf4j
public class ChatTagService {
    
    /**
     * 从机器人获取标签信息（机器人对话和重新回答场景使用）
     * 迁移自AgentChatUtils.getTagsFromRobot
     * 
     * @param robotId 机器人ID
     * @param queryFactory JPA查询工厂
     * @return 标签信息列表，包含标签ID和名称的映射
     */
    public List<Map<String, String>> getTagsFromRobot(Long robotId, JPAQueryFactory queryFactory) {
        log.debug("从机器人获取标签信息: robotId={}", robotId);
        
        Map<String, String> map = new HashMap<>();
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(robotId));
        builder.and(qKnowledgeModel.kbId.eq(qRobotKnowledgeModel.kbId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qFileModel.kbId.eq(qRobotKnowledgeModel.kbId));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qRobotKnowledgeModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
                
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            map.put(String.valueOf(tuple.get(qTagModel.id)), tuple.get(qTagModel.tagName));
        }
        
        List<Map<String, String>> result = new ArrayList<>();
        result.add(map);
        
        log.debug("机器人标签信息获取完成: 共{}个标签", map.size());
        return result;
    }
    
    /**
     * 从知识库获取标签信息（知识库对话场景使用）
     * 迁移自AgentChatUtils.getTagsFromKnowledgeBase
     * 
     * @param knowledgeBaseId 知识库ID
     * @param queryFactory JPA查询工厂
     * @return 标签信息列表，包含标签ID和名称的映射
     */
    public List<Map<String, String>> getTagsFromKnowledgeBase(Long knowledgeBaseId, JPAQueryFactory queryFactory) {
        log.debug("从知识库获取标签信息: knowledgeBaseId={}", knowledgeBaseId);
        
        Map<String, String> map = new HashMap<>();
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeModel.kbId.eq(knowledgeBaseId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qKnowledgeModel.fileId.eq(qFileModel.id));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
                
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            map.put(String.valueOf(tuple.get(qTagModel.id)), tuple.get(qTagModel.tagName));
        }
        
        List<Map<String, String>> result = new ArrayList<>();
        result.add(map);
        
        log.debug("知识库标签信息获取完成: 共{}个标签", map.size());
        return result;
    }
    
    /**
     * 从知识库获取标签ID列表（只包含标签ID）
     * 迁移自AgentChatUtils.getTagsList
     * 
     * @param knowledgeBaseId 知识库ID
     * @param queryFactory JPA查询工厂
     * @return 标签ID列表
     */
    public List<Long> getTagsList(Long knowledgeBaseId, JPAQueryFactory queryFactory) {
        log.debug("从知识库获取标签ID列表: knowledgeBaseId={}", knowledgeBaseId);
        
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeModel.kbId.eq(knowledgeBaseId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qKnowledgeModel.fileId.eq(qFileModel.id));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
                
        Iterator<Tuple> iterator = tupleList.iterator();
        List<Long> result = new ArrayList<>();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            result.add(tuple.get(qTagModel.id));
        }
        
        log.debug("知识库标签ID列表获取完成: 共{}个标签", result.size());
        return result;
    }
}
