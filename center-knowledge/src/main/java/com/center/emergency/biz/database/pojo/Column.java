package com.center.emergency.biz.database.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class Column {
    /**
     * 字段名
     */
    @Schema(description = "字段名")
    private String name;
    /**
     * 数据库名
     */
    @Schema(description = "数据库名")
    private String schema;
    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    private String category;
    /**
     * 是否为空
     */
    @Schema(description = "是否为空")
    private String isNullable;
    /**
     * 是否为主键
     */
    @Schema(description = "是否为主键")
    private String key;
    /**
     * 注释
     */
    @Schema(description = "注释")
    private String comment;
    /**
     * 默认值
     */
    @Schema(description = "默认值")
    private String defaultVal;
}
