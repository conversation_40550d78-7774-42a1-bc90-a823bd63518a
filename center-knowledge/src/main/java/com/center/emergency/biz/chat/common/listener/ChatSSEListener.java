package com.center.emergency.biz.chat.common.listener;

import cn.hutool.json.JSONUtil;
import com.center.emergency.biz.chat.common.collector.EventCollector;
import com.center.emergency.biz.chat.common.enumeration.ParsedEventType;
import com.center.emergency.biz.chat.common.parser.StreamParser;
import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.service.ChatServiceImpl;
import com.center.framework.common.context.LoginContextHolder;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * 业务层 Listener：把解析后的事件按业务需求推送前端、落库。
 */
@Slf4j
public class ChatSSEListener extends GenericSSEListener {

    private final ChatServiceImpl service;
    private final PlatformTransactionManager transactionManager;
    private final ChatVO chatVO;
    private final boolean simulate;
    private final StringBuilder builder;
    private final EventCollector eventCollector;

    public ChatSSEListener(StreamParser parser,
                           CountDownLatch latch,
                           SseEmitter emitter,
                           ChatServiceImpl service,
                           PlatformTransactionManager transactionManager,
                           ChatVO chatVO,
                           boolean simulate,
                           StringBuilder sharedBuilder) {
        super(emitter, parser, latch);
        this.service   = service;
        this.transactionManager = transactionManager;
        this.chatVO    = chatVO;
        this.simulate  = simulate;
        this.builder   = sharedBuilder;
        this.eventCollector = new EventCollector();
    }

    /* ------------- 处理业务事件 ------------- */
    @Override
    protected void handleParsedEvent(ParsedEvent ev, EventSource es) throws IOException {
        // 收集所有事件（智能合并MESSAGE）
        eventCollector.addEvent(ev);
        log.debug("事件收集状态: {}", eventCollector.getStatusInfo());
        
        switch (ev.getEventType()) {
            case MESSAGE:
                if (StringUtils.isBlank(ev.getContent())) break;   // ★ 空串不处理
                sendEvent("message",
                        Collections.singletonMap("content", ev.getContent()));
                break;

            case TOOL_CALL:
                // 直接透传工具调用事件到前端 - 统一格式
                sendEvent("tool_call", Collections.singletonMap("content", ev.getContent()));
                break;

            case TOOL_RESPONSE:
                // 直接透传工具响应事件到前端 - 统一格式
                sendEvent("tool_response", Collections.singletonMap("content", ev.getContent()));
                break;

            case COMPLETED:
                // 先发送completed事件到前端，再触发结束流程 - 统一格式
                sendEvent("completed", Collections.singletonMap("content", ev.getContent()));
                finishConversation();
                break;

            case SPAM:
                sendEvent("spam", ev.getContent());
                finishConversation();       // 统一使用finishConversation
                es.cancel();
                break;

            case END:
                finishConversation();
                break;

            case ERROR:
                sendError(ev.getContent());
                finishConversation();       // 统一使用finishConversation
                es.cancel();
                break;

            default:
                log.warn("未识别事件类型: {}", ev.getEventType());
        }
    }

    /* ------------- 私有辅助 ------------- */

    /**
     * 结束对话：发送end事件，关闭连接
     */
    private void finishConversation() throws IOException {
        try {
            // 1. 发送end事件给前端
            sendEvent("end", chatVO);
        } catch (Exception e) {
            log.error("发送end事件失败，会话ID: {}", chatVO.getSessionId(), e);
        } finally {
            // 2. 关闭连接
            complete();
        }
    }

    /**
     * 重写onClosed方法，在连接关闭后同步保存数据库
     */
    @Override
    public void onClosed(EventSource es) {
        log.info("Chat SSE 连接已关闭，开始同步保存数据库");

        // 1. 先调用父类方法完成基础清理
        super.onClosed(es);

        // 2. 同步保存数据库（连接关闭后）
        saveDataSafely();
    }

    /**
     * 重写onFailure方法，确保异常时也能保存数据
     */
    @Override
    public void onFailure(EventSource es, Throwable t, Response res) {
        log.error("Chat SSE 连接失败: {}", t != null ? t.getMessage() : "未知错误", t);

        // 1. 先保存数据
        saveDataSafely();

        // 2. 再调用父类方法处理异常
        super.onFailure(es, t, res);
    }

    /**
     * 安全保存数据的统一方法
     */
    private void saveDataSafely() {
        if (!simulate) {
            try {
                long startTime = System.currentTimeMillis();
                log.info("开始同步保存对话数据，事件状态: {}", eventCollector.getStatusInfo());

                // 使用编程式事务管理确保事务正确执行
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                def.setName("ChatSSEListener-saveData");
                def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

                TransactionStatus status = transactionManager.getTransaction(def);

                try {
                    service.saveQuestionAndAnswerWithEvents(chatVO, eventCollector.getMergedEvents(), simulate);
                    transactionManager.commit(status);

                    long duration = System.currentTimeMillis() - startTime;
                    log.info("对话数据保存完成，耗时: {}ms, 会话ID: {}", duration, chatVO.getSessionId());
                } catch (Exception e) {
                    transactionManager.rollback(status);
                    log.error("保存对话数据失败，会话ID: {}", chatVO.getSessionId(), e);
                    throw e;
                }
            } catch (Exception e) {
                log.error("同步保存对话数据失败，会话ID: {}", chatVO.getSessionId(), e);
            }
        } else {
            log.debug("模拟模式，跳过数据保存");
        }
    }
}
