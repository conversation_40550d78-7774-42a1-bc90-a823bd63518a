package com.center.emergency.biz.modelgroup.persistence;

import com.center.emergency.common.enumeration.ModelRoleEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "model_group_member")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ModelGroupMember extends BaseTenantModel {

    @Column(name = "model_group_id")
    Long modelGroupId;

    @Column(name = "large_model_id")
    Long largeModelId;

    @Column(name = "role",nullable = false, length = 50)
    @Enumerated(value =EnumType.STRING)
    ModelRoleEnum role;


}