package com.center.emergency.biz.approval.service;

import com.center.emergency.biz.approval.pojo.RecordView;
import com.center.emergency.common.enumeration.RecordStatusEnum;

import java.util.List;

public interface RecordService {

    /**
     * 获取知识审批日志记录
     * @param knowledgeId 知识ID
     * @return List<RecordView> 审批记录列表
     */
    List<RecordView> getRecordList(Long knowledgeId);

    /**
     * 审批通过
     * @param knowledgeId 知识ID
     */
    void pass(Long knowledgeId);

    /**
     * 审批拒绝
     * @param knowledgeId 知识ID
     * @param refuseReason 拒绝原因
     */
    void refuse(Long knowledgeId, String refuseReason);

    /**
     * 记录审批日志
     * @param knowledgeId-知识ID
     * @param status-审批状态
     * @param reason-审批原因
     */
    void RecordSave(Long knowledgeId, RecordStatusEnum status, String reason);

    void recordDelete(Long knowledgeBaseId);
}
