package com.center.emergency.biz.aisearch.common.parser;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.center.emergency.biz.aisearch.common.AiSearchUtils;
import com.center.emergency.biz.aisearch.common.enumeration.AiSearchEventType;
import com.center.emergency.biz.aisearch.common.persistence.AiSearchParsedEvent;
import com.center.emergency.biz.aisearch.pojo.SearchRequest;
import com.center.emergency.biz.aisearch.service.impl.AiSearchServiceImpl;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.CountDownLatch;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;

/**
 * AI搜索SSE解析器，参考ChatSseParser实现
 */
@Slf4j
public class AiSearchSseParser implements AiSearchStreamParser, AiSearchUtils.CountDownLatchHolder {

    private static final String SEARCH_DEFAULT_ERROR_MESSAGE = "搜索服务正忙，请稍后再试。";
    private static final String SEARCH_DEFAULT_SPAM_MESSAGE = "我们的平台致力于提供一个安全、尊重的环境，某些话题可能不适宜讨论。我会很乐意帮助您解答其他问题，谢谢您的理解和配合。";

    private final SearchRequest searchRequest;
    private final SseEmitter emitter;
    private final AiSearchServiceImpl searchService;
    private final AtomicBoolean completed;        // 线程安全
    private final StringBuilder builder;
    private final CountDownLatch latch;

    public AiSearchSseParser(SseEmitter emitter,
                           AiSearchServiceImpl searchService,
                           boolean initCompleted,
                           SearchRequest searchRequest,
                           StringBuilder builder,
                           CountDownLatch latch) {

        this.emitter = emitter;
        this.searchService = searchService;
        this.searchRequest = searchRequest;
        this.builder = builder;
        this.completed = new AtomicBoolean(initCompleted);
        this.latch = latch;
    }

    /* -------------------- 入口 -------------------- */

    @Override
    public AiSearchParsedEvent parse(String rawData, String eventType) throws Exception {
        AiSearchParsedEvent event = new AiSearchParsedEvent();
        event.setRawData(rawData);
        event.setOriginalEventType(eventType);   // 保留服务端原事件名
        event.setSessionId(searchRequest.getSessionId());
        
        // 统一解析成 JSON
        JSONObject json = JSONUtil.parseObj(rawData);
        log.info("AI搜索原始数据：{}", rawData);
        
        /* ---------- ① 处理服务端显式事件类型 ---------- */
        String serverEvent = json.getStr("event");
        if ("completed".equalsIgnoreCase(serverEvent)) {
            event.setEventType(AiSearchEventType.COMPLETED);
            event.setContent(rawData);  // 保存整个JSON字符串
            return event;
        } else if ("search_results".equalsIgnoreCase(serverEvent)) {
            event.setEventType(AiSearchEventType.SEARCH_RESULTS);
            event.setContent(rawData);  // 保存整个JSON字符串
            return event;
        } else if ("tool_call".equalsIgnoreCase(serverEvent)) {
            event.setEventType(AiSearchEventType.TOOL_CALL);
            event.setContent(rawData);  // 保存整个JSON字符串
            return event;
        } else if ("tool_response".equalsIgnoreCase(serverEvent)) {
            event.setEventType(AiSearchEventType.TOOL_RESPONSE);
            event.setContent(rawData);  // 保存整个JSON字符串
            return event;
        } else if ("status_update".equalsIgnoreCase(serverEvent)) {
            event.setEventType(AiSearchEventType.STATUS_UPDATE);
            event.setContent(rawData);  // 保存整个JSON字符串
            return event;
        }
        
        /* ---------- ② 正常 message 流 ---------- */
        if ("200".equals(json.getStr("code"))) {
            return parseMessage(json, event);          // 提取 response 字段
        }
        
        /* ---------- ③ 其它情况统一抛给上层做 error ---------- */
        log.info("解析到非 200 码数据：{}", rawData);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED);
    }

    /* -------------------- 私有实现 -------------------- */

    /** 处理 message 数据 */
    private AiSearchParsedEvent parseMessage(JSONObject json, AiSearchParsedEvent event) {
        event.setEventType(AiSearchEventType.MESSAGE);

        String content = json.getStr("response");
        if (StringUtils.isBlank(content)) {   // 空串直接丢弃
            return event;                     // 不再返回 MESSAGE
        }

        builder.append(content);

        /* ---------- 流式敏感词检查（可选，如果有的话） ---------- */
        // 这里可以添加敏感词检查逻辑
        // if (builder.length() >= 20 && checkSpam(builder.toString())) {
        //     event.setEventType(AiSearchEventType.SPAM);
        //     event.setContent(SEARCH_DEFAULT_SPAM_MESSAGE);
        //     return event;
        // }

        event.setContent(content);
        return event;
    }

    /* -------------------- error / spam 工具 -------------------- */

    /** 供 Listener 收尾兜底使用 */
    @Override
    public void handleSpamResponse(String message) {
        if (completed.get()) {
            log.warn("已完成的搜索SSE再次spam结束，忽略");
            return;
        }
        try {
            // 这里需要调用搜索服务的保存方法
            // searchService.saveSearchResult(searchRequest, builder.toString());
            emitter.send(SseEmitter.event().name("spam").data(message));
            emitter.send(SseEmitter.event().name("end").data(CommonResult.success(searchRequest)));
        } catch (IOException ioe) {
            log.error("spam事件写出失败", ioe);
            handleException("spam事件写出失败：" + ioe.getMessage());
        } finally {
            safeComplete();
        }
    }

    public void handleException(String trace) {
        if (completed.get()) {
            log.warn("已完成的搜索SSE再次发送error，忽略");
            return;
        }
        try {
            emitter.send(SseEmitter.event()
                    .name("error")
                    .data(CommonResult.error(
                            OUTER_SERVER_EXCEPTION.getCode(),
                            SEARCH_DEFAULT_ERROR_MESSAGE,
                            trace)));
        } catch (IOException ioe) {
            log.error("发送error事件失败", ioe);
        } finally {
            safeComplete();
        }
    }

    /** 幂等 complete */
    private void safeComplete() {
        if (completed.compareAndSet(false, true)) {
            try { 
                emitter.complete(); 
            } catch (Exception ignore) {}
        }
    }

    @Override
    public CountDownLatch getLatch() {
        return latch;
    }
} 