package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.datasource.persistence.QDataSourceExtensionModel;
import com.center.emergency.biz.datasource.persistence.QDataSourceModel;
import com.center.emergency.biz.datasource.pojo.DataBaseTableInfo;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.enumerate.SingleAndMultipleEnum;
import com.center.framework.common.pojo.IdAndValue;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Component("DATABASE")
@Slf4j
public class DataBaseServiceForAnswerStrategy extends AbstractAnswerStrategy implements AnswerStrategyService {

    @Resource
    private ModelGroupService modelGroupService;

    @Override
    public List<Long> listAnswerStrategyIds(Long robotId) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QDataSourceModel qDataSourceModel = QDataSourceModel.dataSourceModel;
        JPQLQuery query = queryFactory.select(qDataSourceModel.id)
                .from(qRobotKnowledgeModel)
                .join(qDataSourceModel).on(qRobotKnowledgeModel.kbId.eq(qDataSourceModel.id))
                .where(qRobotKnowledgeModel.robotId.eq(robotId));
        return query.fetch();
    }

    /**
     * 数据库策略的参数构建 - 实现数据库特定的参数构建逻辑
     */
    @Override
    public HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                                   List<Map<String, String>> tags, Boolean isSimulate,
                                                   Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("构建数据库策略对话参数: chatVO={}, kbIds.size={}, fileIds.size={}, modelId={}, isSimulate={}",
                chatVO.getQuestion(), kbIds.size(), fileIds.size(), modelId, isSimulate);

        // 1. 收集数据库配置信息
        List<DataBaseTableInfo> dataBaseTableInfoList = collectDatabaseInfo(chatVO.getRobotId(), jpaQueryFactory);

        // 2. 构建数据库策略基础参数
        HashMap<String, Object> param = buildDatabaseBaseParams(chatVO);

        // 3. 查询并添加模型配置
        if (modelId != null) {
            RobotModelDTO robotModelDTO = queryRobotModelConfig(chatVO.getRobotId(), modelId, jpaQueryFactory);
            // 数据库策略只需要基础模型配置，不需要策略特有参数
            addBaseModelConfigParams(param, robotModelDTO);
        } else {
            // 使用系统默认模型配置
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            param.putAll(defaultModelConfig);
        }

        // 4. 添加数据库配置参数（使用db_configs格式）
        List<Map<String, Object>> dbConfigs = buildDatabaseConfigs(dataBaseTableInfoList);
        param.put("db_configs", dbConfigs);

        log.info("数据库策略参数构建完成，db_configs数量: {}", dbConfigs.size());
        return param;
    }

    /**
     * 数据库策略的资源收集和参数构建 - 实现数据库特定的参数构建逻辑
     */
    @Override
    public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                         Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
        log.info("数据库策略自主收集资源并构建参数: chatVO={}, modelGroupId={}, isSimulate={}",
                chatVO.getQuestion(), modelGroupId, isSimulate);

        // 1. 收集数据库配置信息
        List<DataBaseTableInfo> dataBaseTableInfoList = collectDatabaseInfo(chatVO.getRobotId(), jpaQueryFactory);

        // 2. 构建数据库策略基础参数
        HashMap<String, Object> param = buildDatabaseBaseParams(chatVO);

        // 3. 添加模型组配置
        addModelGroupParams(param, modelGroupId);

        // 4. 添加数据库配置（使用db_configs格式）
        List<Map<String, Object>> dbConfigs = buildDatabaseConfigs(dataBaseTableInfoList);
        param.put("db_configs", dbConfigs);

        log.info("数据库策略参数构建完成，db_configs数量: {}", dbConfigs.size());
        return param;
    }

    /**
     * 构建数据库策略的基础参数
     * 包含数据库策略特有的参数格式
     */
    private HashMap<String, Object> buildDatabaseBaseParams(ChatVO chatVO) {
        HashMap<String, Object> param = buildCommonBaseParams(chatVO);

        // 数据库策略特有参数（使用query字段而不是question）
        param.put("query", chatVO.getQuestion()); // 数据库策略使用query字段

        return param;
    }

    /**
     * 收集数据库信息
     */
    private List<DataBaseTableInfo> collectDatabaseInfo(Long robotId, JPAQueryFactory jpaQueryFactory) {
        QDataSourceModel qDataSourceModel = QDataSourceModel.dataSourceModel;
        QDataSourceExtensionModel qDataSourceExtensionModel = QDataSourceExtensionModel.dataSourceExtensionModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;

        return jpaQueryFactory.select(
                        Projections.bean(DataBaseTableInfo.class,
                                qDataSourceExtensionModel.ip,
                                qDataSourceExtensionModel.port,
                                qDataSourceExtensionModel.databaseName,
                                qDataSourceExtensionModel.userName,
                                qDataSourceExtensionModel.password,
                                qDataSourceExtensionModel.tableName,
                                qDataSourceModel.datasourceType))
                .from(qDataSourceExtensionModel)
                .join(qDataSourceModel).on(qDataSourceExtensionModel.datasourceId.eq(qDataSourceModel.id))
                .join(qRobotKnowledgeModel).on(qRobotKnowledgeModel.kbId.eq(qDataSourceExtensionModel.datasourceId))
                .where(qRobotKnowledgeModel.robotId.eq(robotId)
                        .and(qRobotKnowledgeModel.answerStrategy.eq(AnswerStrategyEnum.DATABASE)))
                .fetch();
    }



    /**
     * 构建数据库配置列表（统一返回数组格式）
     */
    private List<Map<String, Object>> buildDatabaseConfigs(List<DataBaseTableInfo> dataBaseTableInfoList) {
        if (dataBaseTableInfoList == null || dataBaseTableInfoList.isEmpty()) {
            log.warn("数据库表信息列表为空，返回空配置列表");
            return new ArrayList<>();
        }

        log.info("构建数据库配置列表，表信息数量: {}", dataBaseTableInfoList.size());

        // 按数据库分组并构建配置列表
        List<Map<String, Object>> configList = groupTablesByDatabase(dataBaseTableInfoList).entrySet().stream()
                .map(entry -> createDatabaseConfig(entry.getValue()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("数据库配置列表构建完成，数据库数量: {}", configList.size());
        return configList;
    }

    /**
     * 根据数据库表信息构建数据库配置（兼容旧方法）
     * @param dataBaseTableInfoList 数据库表信息列表
     * @return 数据库配置对象，SINGLE类型返回单个配置对象，MULTIPLE类型返回配置列表
     */
    private Object buildDatabaseConfig(List<DataBaseTableInfo> dataBaseTableInfoList) {
        if (dataBaseTableInfoList == null || dataBaseTableInfoList.isEmpty()) {
            log.warn("数据库表信息列表为空，无法构建数据库配置");
            return null;
        }

        // 按数据源类型分组处理
        Map<SingleAndMultipleEnum, List<DataBaseTableInfo>> groupedByType = dataBaseTableInfoList.stream()
                .collect(Collectors.groupingBy(DataBaseTableInfo::getDatasourceType));

        // 如果包含MULTIPLE类型，返回列表格式
        if (groupedByType.containsKey(SingleAndMultipleEnum.MULTIPLE)) {
            return buildMultipleDatabaseConfig(dataBaseTableInfoList);
        }
        // 如果只有SINGLE类型，返回单个对象格式
        else if (groupedByType.containsKey(SingleAndMultipleEnum.SINGLE)) {
            return buildSingleDatabaseConfig(dataBaseTableInfoList);
        }

        log.warn("未识别的数据源类型，无法构建数据库配置");
        return null;
    }

    /**
     * 构建MULTIPLE类型的数据库配置（返回列表）
     */
    private List<Map<String, Object>> buildMultipleDatabaseConfig(List<DataBaseTableInfo> dataBaseTableInfoList) {
        log.info("构建MULTIPLE类型数据库配置，表信息数量: {}", dataBaseTableInfoList.size());

        // 按数据库分组并构建配置列表
        List<Map<String, Object>> configList = groupTablesByDatabase(dataBaseTableInfoList).entrySet().stream()
                .map(entry -> createDatabaseConfig(entry.getValue()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("MULTIPLE类型数据库配置构建完成，数据库数量: {}", configList.size());
        return configList;
    }

    /**
     * 构建SINGLE类型的数据库配置（返回单个对象）
     */
    private Map<String, Object> buildSingleDatabaseConfig(List<DataBaseTableInfo> dataBaseTableInfoList) {
        log.info("构建SINGLE类型数据库配置，表信息数量: {}", dataBaseTableInfoList.size());

        if (dataBaseTableInfoList.isEmpty()) {
            return null;
        }

        // 对于SINGLE类型，直接使用所有表信息创建单个配置
        Map<String, Object> dbConfig = createDatabaseConfig(dataBaseTableInfoList);

        if (dbConfig != null) {
            log.info("SINGLE类型数据库配置构建完成: host={}, database={}, tables={}",
                    dbConfig.get("host"), dbConfig.get("database"), dbConfig.get("tables"));
        }

        return dbConfig;
    }

    /**
     * 按数据库分组表信息
     * @param dataBaseTableInfoList 数据库表信息列表
     * @return 按数据库分组的映射
     */
    private Map<String, List<DataBaseTableInfo>> groupTablesByDatabase(List<DataBaseTableInfo> dataBaseTableInfoList) {
        return dataBaseTableInfoList.stream()
                .collect(Collectors.groupingBy(this::generateDatabaseKey));
    }

    /**
     * 生成数据库唯一标识key
     * @param info 数据库表信息
     * @return 数据库唯一标识
     */
    private String generateDatabaseKey(DataBaseTableInfo info) {
        return String.join(":",
                info.getIp(),
                String.valueOf(info.getPort()),
                info.getDatabaseName(),
                info.getUserName());
    }

    /**
     * 创建单个数据库配置对象
     * @param tablesInSameDb 同一数据库下的表信息列表
     * @return 数据库配置对象
     */
    private Map<String, Object> createDatabaseConfig(List<DataBaseTableInfo> tablesInSameDb) {
        if (tablesInSameDb == null || tablesInSameDb.isEmpty()) {
            return null;
        }

        DataBaseTableInfo firstTable = tablesInSameDb.get(0);

        Map<String, Object> dbConfig = new HashMap<>();
        dbConfig.put("host", firstTable.getIp());
        dbConfig.put("port", firstTable.getPort());
        dbConfig.put("user", firstTable.getUserName());
        dbConfig.put("password", firstTable.getPassword());
        dbConfig.put("database", firstTable.getDatabaseName());

        // 收集该数据库下的所有表名
        List<String> tableNames = extractTableNames(tablesInSameDb);
        dbConfig.put("tables", tableNames);

        log.debug("创建数据库配置: host={}, database={}, tables={}",
                firstTable.getIp(), firstTable.getDatabaseName(), tableNames);

        return dbConfig;
    }

    /**
     * 提取表名列表
     * @param tableInfoList 表信息列表
     * @return 去重后的表名列表
     */
    private List<String> extractTableNames(List<DataBaseTableInfo> tableInfoList) {
        return tableInfoList.stream()
                .map(DataBaseTableInfo::getTableName)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
}
