package com.center.emergency.biz.chat.common.parser;

import com.center.emergency.biz.chat.common.persitence.ParsedEvent;

public interface StreamParser {
    /**
     * @param rawData  SSE返回的分段（字符串）
     * @param eventType SSE的事件名
     * @return 解析后的 ParsedEvent
     * @throws Exception 解析中出现的异常
     */
    ParsedEvent parse(String rawData, String eventType) throws Exception;

    /**
     * 处理敏感词情况，立即结束SSE
     *
     * @param message  需要处理的敏感词字符串
     */
    void handleSpamResponse(String message);

}
