package com.center.emergency.biz.tag.pojo;

import com.center.emergency.common.enumeration.TagTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;

@Data
public class TagBase {

    @NotBlank(message = "标签名称不能为空")
    @Length(max = 50, message = "标签名称长度不能超过50个字符")
    @Schema(description = "标签名称", example = "AI")
    private String tagName;

    @Enumerated(EnumType.STRING)
    @NotBlank(message = "标签类型不能为空")
    @Schema(description = "标签类型", example = "CUSTOM")
    private TagTypeEnum tagType;

    @Length(max = 255, message = "标签备注信息长度不能超过255个字符")
    @Schema(description = "备注信息", example = "人工智能相关")
    private String remark;
}