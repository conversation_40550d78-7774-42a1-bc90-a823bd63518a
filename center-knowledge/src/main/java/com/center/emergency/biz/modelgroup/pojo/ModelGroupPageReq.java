package com.center.emergency.biz.modelgroup.pojo;

import com.center.framework.web.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class ModelGroupPageReq extends PageParam {

    @Schema(description = "模型组名称（可模糊搜索）")
    @Length(min = 1, max = 50, message = "模型名称长度必须在1-50字符之间")
    private String groupName;
}

