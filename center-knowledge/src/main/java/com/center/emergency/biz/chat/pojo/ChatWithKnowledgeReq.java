package com.center.emergency.biz.chat.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
@Schema(description = "与知识库模拟对话")
public class ChatWithKnowledgeReq {

    @NotBlank(message = "问题不能为空")
    @Schema(description = "问题内容")
    private String question;

    @Schema(description = "知识库ID")
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeBaseId;
}
