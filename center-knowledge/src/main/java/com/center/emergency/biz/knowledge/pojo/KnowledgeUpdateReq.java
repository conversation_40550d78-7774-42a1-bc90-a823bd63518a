package com.center.emergency.biz.knowledge.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 知识更新请求类，继承 KnowledgeBase，增加 id 字段
 * <AUTHOR>
 */
@Data
public class KnowledgeUpdateReq extends KnowledgeBase {

    @NotNull(message = "知识ID不能为空")
    @Schema(description = "知识ID")
    private Long id;


    private List<QAPair> faqs;

}