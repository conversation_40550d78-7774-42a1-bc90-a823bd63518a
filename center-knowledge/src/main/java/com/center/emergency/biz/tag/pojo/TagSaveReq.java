package com.center.emergency.biz.tag.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class TagSaveReq {

    //标签ID
    @Schema(description = "标签ID",example = "3")
    @NotNull(message = "标签ID不能为空")
    private Long id;

    //标签名称
    @NotBlank(message = "标签名称不能为空")
    @Length(max = 50, message = "标签名称长度不能超过50个字符")
    @Schema(description = "标签名称",example = "tag")
    private String tagName;
}
