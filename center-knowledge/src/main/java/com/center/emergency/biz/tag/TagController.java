package com.center.emergency.biz.tag;

import com.center.emergency.biz.tag.pojo.*;
import com.center.emergency.biz.tag.service.TagService;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 标签管理接口
 * @date 2024/10/16 18:06
 */
@Tag(name = "标签管理")
@RestController
@RequestMapping("/tag")
public class TagController {

    @Resource
    private TagService tagService;


    @GetMapping("/search")
    @Operation(summary = "查询标签分页")
    public CommonResult<PageResult<TagPageResp>> getPageTag(@Valid TagPageReq pageReq){
        return CommonResult.success(tagService.getPageTags(pageReq));
    }

    @PostMapping("/create")
    @Operation(summary = "新增标签")
    public CommonResult<String> createTag(@Valid @RequestBody TagCreateReq tagCreateReq){
        tagService.createTag(tagCreateReq.getTagName());
        return CommonResult.successWithMessageOnly("标签创建成功");
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除标签")
    public CommonResult<String> deleteTag(@PathVariable Long id){
        tagService.deleteTag(id);
        return CommonResult.successWithMessageOnly("标签删除成功");
    }

    @PostMapping("/get/{id}")
    @Operation(summary = "获取标签内容")
    public CommonResult<TagGetResp> getTag(@PathVariable Long id){
        return CommonResult.success(tagService.getTag(id));
    }

    @PostMapping("/save")
    @Operation(summary = "保存修改后的标签信息")
    public CommonResult<String> saveTag(@Valid @RequestBody TagSaveReq req) {
        tagService.saveTag(req);
        return CommonResult.successWithMessageOnly("标签修改保存成功");
    }

    @Operation(summary = "查询当前租户的自定义标签以及系统标签")
    @GetMapping("/tenantAndSystemTags")
    public CommonResult<List<TagResp>> getTenantAndSystemTags() {
        // 从上下文获取当前登录用户的租户ID
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        return CommonResult.success(tagService.getTenantAndSystemTags(tenantId));
    }
}
