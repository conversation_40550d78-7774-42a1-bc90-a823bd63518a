package com.center.emergency.biz.apikeys.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ApiKeyValidateReq {

    @Schema(description = "service", example = "openai-compatible-router")
    private String service;

    @Schema(description = "密钥")
    @NotBlank(message = "ApiKey密钥不能为空")
    private String api_key;

    @Schema(description = "request_id",example = "req_abc123def456")
    private String request_id;
}
