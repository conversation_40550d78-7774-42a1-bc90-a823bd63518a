package com.center.emergency.biz.aisearch.common;

import com.center.emergency.biz.aisearch.pojo.SearchMode;
import com.center.emergency.biz.aisearch.pojo.SearchRequest;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.emergency.common.utils.OkHttpUtils;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import com.center.framework.web.pojo.CommonResult;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;

/**
 * AI搜索工具类
 * 参考chat模块的AgentChatUtils设计，提供搜索请求的公共方法
 */
public class AiSearchUtils {
    
    private static final Logger log = LoggerFactory.getLogger(AiSearchUtils.class);
    private static final String SEARCH_DEFAULT_ERROR_MESSAGE = "搜索服务正忙，请稍后再试。";
    
    // 常量配置
    private static final long SSE_TIMEOUT_MS = 300000L; // 5分钟超时
    private static final int DEFAULT_MAX_RESULTS = 10; // 默认最大搜索结果数
    private static final int DEFAULT_MAX_TITLE_LENGTH = 50; // 会话标题最大长度
    
    /**
     * 异步执行搜索请求，使用SSE返回流式数据
     * 
     * @param emitter SSE发射器
     * @param request 搜索请求参数
     * @param url 算法接口URL
     * @param param 请求参数
     * @param executor 异步执行器
     * @param parser 解析器（由调用方构造）
     * @param listenerFactory listener工厂，避免循环依赖
     */
    public static void searchRunAsync(SseEmitter emitter,
                                      SearchRequest request,
                                      String url,
                                      HashMap<String, Object> param,
                                      Executor executor,
                                      Object parser,
                                      Object listenerFactory) {
        log.info("发起AI搜索SSE异步请求, URL: {}, 搜索模式: {}, 问题: {}", 
                url, request.getSearchMode(), request.getQuestion());
        
        CompletableFuture.runAsync(() -> {
            Object listener = null;
            if (listenerFactory instanceof ListenerFactory) {
                listener = ((ListenerFactory) listenerFactory).create(parser, emitter, request);
            }
            try {
                // 使用OkHttpUtils创建client
                OkHttpClient client = OkHttpUtils.getOkHttpClient();
                
                // 为SSE连接创建正确的请求，包含必要的Accept头
                String json = "";
                if (param != null) {
                    json = com.alibaba.fastjson.JSON.toJSONString(param);
                    log.info("json=={}", json);
                }
                RequestBody requestBody = RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), json);
                
                Request httpRequest = new Request.Builder()
                    .post(requestBody)
                    .url(url)
                    .addHeader("Accept", "text/event-stream") // 关键：SSE必需的Accept头
                    .addHeader("Cache-Control", "no-cache")   // SSE推荐设置
                    .addHeader("Connection", "keep-alive")    // 保持连接
                    .build();
                
                log.info("创建搜索SSE请求, URL: {}", url);
                
                EventSource.Factory factory = EventSources.createFactory(client);
                factory.newEventSource(httpRequest, (okhttp3.sse.EventSourceListener) listener);
                
                log.info("搜索SSE请求已建立连接, 等待数据流结束");

                // 恢复对parser latch的等待，与chat模块对齐
                if (parser instanceof CountDownLatchHolder) {
                    log.info("Parser是CountDownLatchHolder的实例，开始等待latch");
                    ((CountDownLatchHolder) parser).getLatch().await();
                    log.info("Parser的latch已释放");
                } else {
                    log.warn("Parser不是CountDownLatchHolder的实例，无法等待latch");
                }

                log.info("搜索SSE请求已完成");
            } catch (Exception e) {
                log.error("搜索SSE请求异常: {}", e.getMessage(), e);
                try {
                    emitter.send(
                        SseEmitter.event()
                            .name("error")
                            .data(CommonResult.error(
                                    500, 
                                    SEARCH_DEFAULT_ERROR_MESSAGE,
                                    e.getMessage())));
                } catch (IOException ex) {
                    log.error("发送error事件失败: {}", ex.getMessage());
                }
            }
        }, executor);
    }
    
    /**
     * 查询系统默认大模型配置
     * 
     * @param jpaQueryFactory JPA查询工厂
     * @return 系统默认模型信息
     */
    public static Map<String, Object> getSystemDefaultModel(JPAQueryFactory jpaQueryFactory) {
        log.info("查询系统默认大模型配置");
        
        QLargeModel qLargeModel = QLargeModel.largeModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM));
        
        Tuple defaultModel = jpaQueryFactory.select(
                        qLargeModel.baseUrl,
                        qLargeModel.apiKey,
                        qLargeModel.modelName,
                        qLargeModel.maxTokens,
                        qLargeModel.inputContextLength,
                        qLargeModel.temperature,
                        qLargeModel.topP)
                .from(qLargeModel)
                .where(builder)
                .fetchFirst();
        
        Map<String, Object> modelConfig = new HashMap<>();
        if (defaultModel != null) {
            modelConfig.put("api_url", defaultModel.get(qLargeModel.baseUrl));
            modelConfig.put("api_key", defaultModel.get(qLargeModel.apiKey));
            modelConfig.put("model", defaultModel.get(qLargeModel.modelName));
            modelConfig.put("max_tokens", defaultModel.get(qLargeModel.maxTokens));
            modelConfig.put("context_length", defaultModel.get(qLargeModel.inputContextLength));
            modelConfig.put("temperature", defaultModel.get(qLargeModel.temperature));
            modelConfig.put("top_p", defaultModel.get(qLargeModel.topP));
            
            log.info("获取到系统默认模型: {}", defaultModel.get(qLargeModel.modelName));
        } else {
            log.warn("未找到系统默认模型，使用硬编码默认值");
            // 硬编码默认值作为备份
            modelConfig.put("api_url", "https://dashscope.aliyuncs.com/compatible-mode/v1");
            modelConfig.put("api_key", "sk-897fd687b2ef402db4428331cfb89884");
            modelConfig.put("model", "qwen-turbo-latest");
        }
        
        return modelConfig;
    }
    
    /**
     * 构建全网搜索请求参数
     * 
     * @param request 搜索请求
     * @param history 历史对话记录（格式：[{"role": "user", "content": "..."}]）
     * @param jpaQueryFactory JPA查询工厂
     * @return 请求参数映射
     */
    public static HashMap<String, Object> buildGlobalSearchParam(SearchRequest request, 
                                                                List<Map<String, Object>> history,
                                                                JPAQueryFactory jpaQueryFactory) {
        log.info("构建全网搜索参数: question={}, maxResults={}", 
                request.getQuestion(), request.getMaxResults());
        
        // 查询系统默认模型配置
        Map<String, Object> modelConfig = getSystemDefaultModel(jpaQueryFactory);
                
        HashMap<String, Object> param = new HashMap<>();
        param.put("query", request.getQuestion());
        param.put("max_results", request.getMaxResults());
        param.put("llm_api_url", modelConfig.get("api_url"));
        param.put("llm_api_key", modelConfig.get("api_key"));
        param.put("model", modelConfig.get("model"));
        param.put("history", history);
        param.put("streaming", 1);  // 启用流式响应
        param.put("user_id", "zyx");  // 添加用户ID
        
        log.info("生成的全网搜索参数: {}", param);
        return param;
    }
    
    /**
     * 构建企业内部搜索请求参数
     * 
     * @param request 搜索请求
     * @param history 历史对话记录
     * @param kbIds 知识库ID集合
     * @param fileIds 文件ID列表
     * @param tagsList 标签列表
     * @param modelGroup 模型组配置
     * @return 请求参数映射
     */
    public static HashMap<String, Object> buildInternalSearchParam(SearchRequest request,
                                                                  List<Map<String, Object>> history,
                                                                  Set<String> kbIds,
                                                                  List<String> fileIds,
                                                                  List<Map<String, String>> tagsList,
                                                                  List<Map<String, Object>> modelGroup) {
        log.info("构建企业内部搜索参数: question={}, kbIds.size={}, fileIds.size={}", 
                request.getQuestion(), kbIds.size(), fileIds.size());
                
        HashMap<String, Object> param = new HashMap<>();
        param.put("kb_ids", new ArrayList<>(kbIds));
        param.put("file_ids", fileIds);
        param.put("question", request.getQuestion());
        param.put("user_id", "zyx");
        param.put("streaming", 1);
        param.put("history", history);
        param.put("tags_list", tagsList);
        param.put("max_token", 4096);
        param.put("search_mode", 3);
        param.put("score_threshold", 0.2);
        param.put("rerank", 1);
        param.put("only_need_search_results", 0);
        param.put("api_context_length", 16000);
        param.put("temperature", 0.5);
        param.put("top_p", 0.99);
        param.put("top_k", 20);
        param.put("answer_mode", 2);
        param.put("answer_strategy", "kb_answer");
        param.put("query_decompose", 1);
        param.put("model_group", modelGroup);
        param.put("enable_model_router", 1);
        param.put("enable_reasoning", 1);
        
        log.debug("生成的企业内部搜索参数: {}", param);
        return param;
    }
    
    /**
     * 构建默认模型组配置
     * 
     * @return 默认模型组配置
     */
    public static List<Map<String, Object>> buildDefaultModelGroup() {
        List<Map<String, Object>> modelGroup = new ArrayList<>();
        
        // Router模型
        Map<String, Object> routerModel = new HashMap<>();
        routerModel.put("model", "deepseek-chat");
        routerModel.put("api_base", "https://api.deepseek.com/v1");
        routerModel.put("api_key", "sk-dafa3dcc495247ea80e3d609fe463336");
        routerModel.put("role", "router");
        modelGroup.add(routerModel);
        
        // Reasoner模型1
        Map<String, Object> reasonerModel1 = new HashMap<>();
        reasonerModel1.put("model", "deepseek-reasoner");
        reasonerModel1.put("api_base", "https://api.deepseek.com/v1");
        reasonerModel1.put("api_key", "sk-dafa3dcc495247ea80e3d609fe463336");
        reasonerModel1.put("role", "reasoner");
        modelGroup.add(reasonerModel1);
        
        // Normal模型
        Map<String, Object> normalModel = new HashMap<>();
        normalModel.put("model", "qwen-turbo-latest");
        normalModel.put("api_base", "https://dashscope.aliyuncs.com/compatible-mode/v1");
        normalModel.put("api_key", "sk-897fd687b2ef402db4428331cfb89884");
        normalModel.put("role", "normal");
        modelGroup.add(normalModel);
        
        // Reasoner模型2
        Map<String, Object> reasonerModel2 = new HashMap<>();
        reasonerModel2.put("model", "qwen-plus");
        reasonerModel2.put("api_base", "https://dashscope.aliyuncs.com/compatible-mode/v1");
        reasonerModel2.put("api_key", "sk-897fd687b2ef402db4428331cfb89884");
        reasonerModel2.put("role", "reasoner");
        modelGroup.add(reasonerModel2);
        
        return modelGroup;
    }
    

    
    /**
     * 生成会话标题
     * 
     * @param question 用户问题
     * @param maxLength 最大长度
     * @return 会话标题
     */
    public static String generateSessionTitle(String question, int maxLength) {
        if (question == null || question.trim().isEmpty()) {
            return "新的搜索会话";
        }
        
        String trimmed = question.trim();
        if (trimmed.length() <= maxLength) {
            return trimmed;
        }
        
        return trimmed.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * 获取SSE超时时间
     */
    public static long getSseTimeoutMs() {
        return SSE_TIMEOUT_MS;
    }
    
    /**
     * 获取默认最大搜索结果数
     */
    public static int getDefaultMaxResults() {
        return DEFAULT_MAX_RESULTS;
    }
    
    /**
     * 获取默认标题最大长度
     */
    public static int getDefaultMaxTitleLength() {
        return DEFAULT_MAX_TITLE_LENGTH;
    }
    
    /**
     * CountDownLatch持有者接口
     */
    public interface CountDownLatchHolder {
        CountDownLatch getLatch();
    }
    
    /**
     * 监听器工厂接口
     */
    public interface ListenerFactory {
        Object create(Object parser, SseEmitter emitter, SearchRequest request);
    }
} 