package com.center.emergency.biz.knowledge.pojo;

import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 知识创建请求类，继承 KnowledgeBase，去掉了 id 字段
 * <AUTHOR>
 */
@Data
public class KnowledgeNoFileCreateReq {
    // 知识库ID
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private Long kbId;
    // 关联文件ID（可选）
    @NotNull(message = "知识名称不能为空")
    @Schema(description = "知识库名称")
    private String knowledgeName;

    @Schema(description = "关联文件ID")
    private Long fileId;

    private List<QAPair> faqs;

}