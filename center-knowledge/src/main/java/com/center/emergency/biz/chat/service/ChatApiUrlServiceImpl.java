package com.center.emergency.biz.chat.service;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Chat模块API接口URL服务实现类
 * 根据不同的答案策略返回对应的算法接口URL
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class ChatApiUrlServiceImpl implements ChatApiUrlService {
    
    @Value("${python.api-base-url}")
    private String modelUrl;
    
    @Override
    public String getApiUrl(AnswerStrategyEnum answerStrategy) {
        log.info("获取答案策略对应的API接口URL: strategy={}", answerStrategy);
        
        switch (answerStrategy) {
            case KNOWLEDGE_BASE:
                // 知识库策略 - 使用现有的知识库对话接口
                String knowledgeBaseUrl = modelUrl + "/api/local_doc_qa/local_doc_chat";
                log.info("知识库策略API URL: {}", knowledgeBaseUrl);
                return knowledgeBaseUrl;
                
            case DATABASE:
                // 数据库策略 - 使用多数据库查询接口
                String databaseUrl = modelUrl + "/api/local_doc_qa/multi_db_query";
                log.info("数据库策略API URL: {}", databaseUrl);
                return databaseUrl;
                
            case MCP:
                // MCP策略 - 使用MCP流式对话接口
                String mcpUrl = modelUrl + "/api/local_doc_qa/mcp_chat_stream";
                log.info("MCP策略API URL: {}", mcpUrl);
                return mcpUrl;
                
            case FILE:
                // 文件策略 - 使用文件问答接口
                String fileUrl = modelUrl + "/api/local_doc_qa/doc_chat_qa";
                log.info("文件策略API URL: {}", fileUrl);
                return fileUrl;
                
            default:
                // 默认回退到知识库策略
                log.warn("未知的答案策略: {}, 回退到知识库策略", answerStrategy);
                String defaultUrl = modelUrl + "/api/local_doc_qa/local_doc_chat";
                return defaultUrl;
        }
    }
}
