package com.center.emergency.biz.knowledge.pojo;

import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 知识响应类
 * <AUTHOR>
 */
@Data
public class KnowledgeWithQaResp extends KnowledgeResp {
    private String pdfPreviewUrl;
    private List<QaWithCiteResponse> qaWithCiteResponses;
}