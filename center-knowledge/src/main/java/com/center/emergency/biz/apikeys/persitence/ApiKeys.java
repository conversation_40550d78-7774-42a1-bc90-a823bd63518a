package com.center.emergency.biz.apikeys.persitence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Data
@Entity
@QueryEntity
@Table(name = "api_keys")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ApiKeys extends BaseTenantModel {

    @Column(name = "api_key_name", nullable = false, length = 100)
    private String apiKeyName;

    @Column(name = "model_id")
    private Long modelId;

    @Column(name = "api_key")
    private String apiKey;

    @Column(name = "robot_id")
    private Long robotId;

    @Column(name = "start_time")
    @CreatedDate
    protected LocalDateTime startTime;

    @Column(name = "end_time")
    protected LocalDateTime endTime;

    @Column(name = "last_used_time")
    protected LocalDateTime lastUsedTime;
}