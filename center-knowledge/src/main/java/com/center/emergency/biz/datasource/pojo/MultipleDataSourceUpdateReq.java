package com.center.emergency.biz.datasource.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MultipleDataSourceUpdateReq {

    @Schema(description = "数据库表信息")
    private List<DataSourceExtensionBase> dataSourceExtensionBaseList;

    @Schema(description = "数据库信息")
    private DataSourceBase dataSourceBase;

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;
}
