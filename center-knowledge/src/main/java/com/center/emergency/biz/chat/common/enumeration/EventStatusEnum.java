package com.center.emergency.biz.chat.common.enumeration;

/**
 * 事件状态枚举
 */
public enum EventStatusEnum {
    SUCCESS("SUCCESS", "成功"),
    FAILED("FAILED", "失败"),
    PENDING("PENDING", "进行中"),
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String description;

    EventStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
} 