package com.center.emergency.biz.mcp;


import com.center.emergency.biz.mcp.pojo.McpCreateReq;
import com.center.emergency.biz.mcp.pojo.McpPageReq;
import com.center.emergency.biz.mcp.pojo.McpResp;
import com.center.emergency.biz.mcp.pojo.McpUpdateReq;
import com.center.emergency.biz.mcp.service.McpService;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "MCP管理", description = "提供MCP创建、更新、删除、查询功能")
@RequestMapping("/mcp")
@Validated
public class McpController {

    @Resource
    private McpService mcpService;

    @PostMapping("/create")
    @Operation(summary = "创建MCP")
    @EnumConvertPoint
    public CommonResult<Long> createMcp(@RequestBody @Validated McpCreateReq req) {
        Long id = mcpService.createMcp(req);
        return CommonResult.success(id,"创建成功");
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除MCP服务")
    public CommonResult<Long> deleteMcp(@PathVariable("id") Long id) {
        //TODO 需要确认是否被助手引用，如果被引用不能删除，可以通过加一个boolean参数实现真假删除
        mcpService.deleteMcp(id);
        return CommonResult.success(id, "删除成功");
    }

    @PostMapping("/update")
    @Operation(summary = "更新MCP服务")
    @EnumConvertPoint
    public CommonResult<McpResp> updateMcp(@RequestBody @Validated McpUpdateReq req) {
        mcpService.updateMcp(req);
        return CommonResult.successWithMessageOnly("更新成功");
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "查询MCP服务详情")
    public CommonResult<McpResp> getMcp(@PathVariable("id") Long id) {
        McpResp resp = mcpService.getMcp(id);
        return CommonResult.success(resp, "查询成功");
    }

    @GetMapping("/list")
    @Operation(summary = "查询所有MCP服务")
    public CommonResult<List<McpResp>> listMcp( @RequestParam(required = false) String keyword) {
        List<McpResp>  mcpRespList = mcpService.listMcp(keyword);
        return CommonResult.success(mcpRespList, "查询成功");
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询MCP服务")
    @EnumConvertPoint
    public CommonResult<PageResult<McpResp>> pageMcp(McpPageReq req) {
        return CommonResult.success(mcpService.pageMcp(req));
    }

}