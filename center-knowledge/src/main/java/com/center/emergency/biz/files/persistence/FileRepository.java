package com.center.emergency.biz.files.persistence;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 文件持久化接口，操作文件表
 * <AUTHOR>
 */
@Repository
public interface FileRepository extends JpaRepository<FileModel, Long> {
    // 可以根据业务需求扩展自定义查询方法
    void deleteByKbId(Long kbId);

    Boolean existsByFileNameAndKbId(String fileName, Long kbId);

    List<FileModel> findByFileNameAndKbId(String fileName, Long kbId);
    List<FileModel> findByOriginalNameAndKbId(String fileName, Long kbId);

    boolean existsByFileNameAndKbIdAndIdNot(String fileName, Long kbId, Long id);
}