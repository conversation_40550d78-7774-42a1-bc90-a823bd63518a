package com.center.emergency.biz.aisearch.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

/**
 * AI搜索会话实体
 */
@Data
@Entity
@QueryEntity
@Table(name = "ai_search_session")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class AiSearchSessionModel extends BaseTenantModel {
    
    /**
     * 会话标题
     */
    @Column(name = "title", length = 500)
    private String title;
    
    /**
     * 搜索模式：1-全网搜索，2-企业内部搜索
     */
    @Column(name = "search_mode", nullable = false)
    private Integer searchMode;
    
    /**
     * 搜索引擎类型（全网搜索时使用，如bing、google等）
     */
    @Column(name = "search_engine", length = 50)
    private String searchEngine;
    
    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Long departmentId;
} 