package com.center.emergency.biz.chat.pojo;

import com.center.emergency.biz.files.pojo.FileInfo;
import com.center.framework.common.enumerate.EventTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ChatQuestionBase {

    @Schema(description = "问答ID")
    private Long id;

    @Schema(description = "问题内容")
    private String content;

    @Schema(description = "消息类型")
    private EventTypeEnum eventType;

    @Schema(description = "文件的基本信息")
    private FileInfo fileInfo;

}
