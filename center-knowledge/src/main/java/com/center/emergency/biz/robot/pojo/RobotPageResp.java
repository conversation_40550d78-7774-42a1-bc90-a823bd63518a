package com.center.emergency.biz.robot.pojo;

import com.center.emergency.common.enumeration.AnswerModeEnum;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class RobotPageResp extends RobotBase{

    @Schema(description = "机器人ID")
    private Long id;

    //操作人
    @Schema(description = "操作人",example = "admin")
    String  displayName;

    //创建时间
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "机器人状态",example = "ACTIVE")
    @EnumValidate(message = "机器人状态不正确",value = CommonStatusEnum.class)
    @NotBlank(message = "机器人状态不能为空")
    private CommonStatusEnum robotStatus;

    @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "robotStatus")
    @Schema(description = "机器人状态名称",example = "激活")
    private String statusName;

    // 关联模型组ID
    @Schema(description = "机器人关联的模型组ID", example = "1001")
    private Long modelGroupId;

    // 系统提示词
    @Schema(description = "机器人系统提示词", example = "你是一个专业的医疗咨询机器人")
    private String systemPrompt;

    // 对话示例
    @Schema(description = "机器人对话示例", example = "用户：你好 机器人：您好，有什么我可以帮您的吗？")
    private String dialogueExamples;

    // 检索模式
    @Schema(description = "检索模式：VECTOR / TEXT / HYBRID", example = "HYBRID")
    private SearchModeEnum searchMode;

    // 问答模式
    @Schema(description = "问答模式：ONLY_KB / KB_FIRST_MODEL / ONLY_QA", example = "KB_FIRST_MODEL")
    private AnswerModeEnum answerMode;

    // 回答策略
    @Schema(description = "回答策略", example = "MCP")
    private AnswerStrategyEnum answerStrategy;

    // 文本匹配相似度阈值
    @Schema(description = "文本匹配相似度阈值", example = "0.80")
    private BigDecimal similarityThreshold;

    // 最大召回数量
    @Schema(description = "最大召回数量", example = "3")
    private Integer maxHits;

}
