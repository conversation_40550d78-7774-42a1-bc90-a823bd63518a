package com.center.emergency.biz.datasource.persistence;

import com.center.framework.db.annotation.NotIgnoreNullField;
import com.center.framework.db.core.BaseModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@QueryEntity
@Table(name = "center_datasource_extension")
public class DataSourceExtensionModel extends BaseModel {
    @Column(name = "datasource_id", nullable = false)
    private Long datasourceId;

    @Column(name = "category", nullable = false, length = 100)
    private String category;

    @Column(name = "ip", nullable = false, length = 100)
    private String ip;

    @Column(name = "port", nullable = false)
    private Integer port;

    @Column(name = "database_name", length = 100)
    private String databaseName;

    @Column(name = "user_name", length = 100)
    private String userName;

    @Column(name = "password", length = 100)
    private String password;

    @Column(name = "jdbc_parameter", length = 100)
    private String jdbcParameter;

    @Column(name = "table_name", length = 50)
    private String tableName;

    @Column(name = "table_description", length = 500)
    private String tableDescription;

}
