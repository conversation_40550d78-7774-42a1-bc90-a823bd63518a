package com.center.emergency.biz.model.pojo;

import com.center.emergency.common.enumeration.ModelProviderEnum;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.utils.datetime.DateTimeUtils;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LargeModelPageResp {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "模型显示名称")
    private String modelDisplayName;

    @Schema(description = "模型描述")
    private String modelDesc;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "status")
    @Schema(description = "模型状态名称",example = "激活")
    private String statusName;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = DateTimeUtils.TIME_ZONE_DEFAULT)
    private LocalDateTime updateTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DateTimeUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = DateTimeUtils.TIME_ZONE_DEFAULT)
    private LocalDateTime createTime;

}
