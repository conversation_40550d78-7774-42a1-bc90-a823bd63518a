package com.center.emergency.biz.knowledge;

import com.center.emergency.biz.knowledge.pojo.QaConfirmImportReq;
import com.center.emergency.biz.knowledge.pojo.QaImportResultResp;
import com.center.emergency.biz.knowledge.pojo.QaPreviewResp;

import com.center.emergency.biz.knowledge.service.QaImportService;
import com.center.framework.web.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * QA导入控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "QA导入管理")
@RequestMapping("/knowledge/qa-import")
@Validated
public class QaImportController {

    @Resource
    private QaImportService qaImportService;

    @PostMapping("/preview")
    @Operation(summary = "步骤一：预览Excel文件QA内容", description = "上传Excel文件，解析内容并预检查重复性，返回预览结果")
    public CommonResult<QaPreviewResp> previewFromExcel(
            @Parameter(description = "Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @Parameter(description = "知识库ID", required = true) @RequestParam("kbId") Long kbId) {

        QaPreviewResp response = qaImportService.previewFromExcel(file, kbId);
        return CommonResult.success(response);
    }

    @PostMapping("/confirm")
    @Operation(summary = "步骤二：确认导入QA数据", description = "确认导入预览后的QA数据到知识库")
    public CommonResult<QaImportResultResp> confirmImport(
            @Parameter(description = "确认导入请求", required = true) @Valid @RequestBody QaConfirmImportReq req) {

        QaImportResultResp response = qaImportService.confirmImport(req);
        return CommonResult.success(response);
    }

    @PostMapping("/import")
    @Operation(summary = "Excel文件QA导入（直接导入）", description = "上传Excel文件，解析内容并直接导入到知识库，返回导入结果")
    public CommonResult<QaImportResultResp> importFromExcel(
            @Parameter(description = "Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @Parameter(description = "知识库ID", required = true) @RequestParam("kbId") Long kbId) {

        QaImportResultResp response = qaImportService.importFromExcel(file, kbId);
        return CommonResult.success(response);
    }

    @GetMapping("/template")
    @Operation(summary = "下载QA导入模板", description = "下载标准的Excel导入模板文件")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            byte[] templateData = qaImportService.downloadTemplate();

            // 使用英文文件名避免Tomcat编码问题
            String filename = "QA_Import_Template.xlsx";

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .body(templateData);
        } catch (Exception e) {
            log.error("下载QA导入模板失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }


}