package com.center.emergency.biz.knowledge.pojo;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.framework.web.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;

/**
 * 根据知识库id查询文件列表---分页查询
 * <AUTHOR>
 */
@Data
public class KnowledgeQueryPageReq extends PageParam {

    // 知识库ID
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private Long kbId;


}