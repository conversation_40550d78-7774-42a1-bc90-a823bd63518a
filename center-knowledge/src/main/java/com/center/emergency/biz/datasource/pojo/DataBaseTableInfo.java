package com.center.emergency.biz.datasource.pojo;

import com.center.framework.common.enumerate.SingleAndMultipleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DataBaseTableInfo extends DataBaseInfo{

    @Schema(description = "表名称")
    private String tableName;

    @Schema(description = "数据库类型(SINGLE/MULTIPLE)")
    private SingleAndMultipleEnum datasourceType;
}
