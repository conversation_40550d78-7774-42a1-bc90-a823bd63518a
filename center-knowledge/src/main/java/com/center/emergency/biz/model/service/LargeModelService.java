package com.center.emergency.biz.model.service;

import com.center.emergency.biz.model.persistence.LargeModel;
import com.center.emergency.biz.model.pojo.*;
import com.center.framework.web.pojo.PageResult;

import java.util.List;

/**
 * 大模型管理服务接口
 */
public interface LargeModelService {

    /**
     * 创建大模型
     *
     * @param req 创建请求参数
     * @return 创建成功后的大模型信息
     */
    LargeModelResp createLargeModel(LargeModelCreateReq req);

    /**
     * 更新大模型
     *
     * @param req 更新请求参数
     * @return 更新后的大模型信息
     */
    LargeModelResp updateLargeModel(LargeModelUpdateReq req);

    /**
     * 删除大模型（如果被引用则不允许删除）
     *
     * @param id 模型 ID
     */
    void deleteLargeModel(Long id);

    /**
     * 分页查询大模型及关联的机器人信息
     *
     * @param req 分页查询参数
     * @return 分页结果，包含大模型及其关联的机器人信息
     */
    PageResult<PageLargeModelWithRobotsResp> pageLargeModels(LargeModelPageReq req);

    /**
     * 查询所有模型（用于下拉列表等场景）
     *
     * @return 模型列表
     */
    List<SelectListLargeModelResp> listAllModelsForSelect();

    /**
     * 获取某个模型被机器人引用的情况
     *
     * @param modelId 模型 ID
     * @return 被引用的机器人详情及引用数量
     */
    LargeModelUsageResp getModelUsageInfo(Long modelId);

    LargeModelWithRobotsResp toggleModelStatus(Long modelId);
    LargeModelWithRobotsResp getModelDetail(Long modelId);
    List<AvailableModelResp> getAvailableModels(LargeModelCreateReq req);
}
