package com.center.emergency.biz.tag.persistence;

import com.center.emergency.common.enumeration.TagTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TagRepository extends JpaRepository<TagModel,Long> {
    /**
     * 根据租户ID查询自定义标签
     * @param tenantId 租户ID
     * @return 自定义标签列表
     */
    List<TagModel> findByTenantId(Long tenantId);

    /**
     * 根据标签类型查询标签
     * @param tagType 标签类型
     * @return 标签列表
     */
    List<TagModel> findByTagType(TagTypeEnum tagType);

    boolean existsByTagNameAndTenantId(String tagName, Long tenantId);

    /**
     * 根据标签名称与系统/企业标签判重
     * @param tagName
     * @param tagTypeEnum
     * @return
     */
    boolean existsByTagNameAndTagType(String tagName, TagTypeEnum tagTypeEnum);

    boolean existsByTagName(String tagName);

    void deleteById(Long id);

    Optional<TagModel> findByIdAndTenantId(Long id, Long tenantId);

    List<TagModel> findByTagName(String tagName);
}
