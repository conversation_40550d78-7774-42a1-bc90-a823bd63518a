package com.center.emergency.biz.knowledgebase.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
/**
 * 创建知识库的请求对象
 * 继承自 KnowledgeBaseBase，包含创建时额外需要的字段
 */
@Data
public class KnowledgeBaseCreateReq extends KnowledgeBaseBase {

    @Schema(description = "模型文件库ID")
    private String aiFilebId;

    @Schema(description = "模型知识库ID")
    private String aiFaqbId;
}