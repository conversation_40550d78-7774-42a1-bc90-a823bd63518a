package com.center.emergency.biz.knowledge.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * QA导入结果响应类
 * 简化的前端友好响应结构
 * <AUTHOR>
 */
@Data
public class QaImportResultResp {

    @Schema(description = "导入是否成功")
    private Boolean success;

    @Schema(description = "导入结果消息")
    private String message;

    @Schema(description = "导入状态：SUCCESS/FAILED")
    private String status;

    @Schema(description = "知识库ID")
    private Long kbId;

    @Schema(description = "知识库名称")
    private String kbName;

    @Schema(description = "创建的知识ID")
    private Long knowledgeId;

    @Schema(description = "创建的知识名称")
    private String knowledgeName;

    @Schema(description = "导入统计")
    private ImportStats stats;

    @Schema(description = "错误详情（如有）")
    private List<ErrorDetail> errorDetails;
    
    @Data
    public static class ImportStats {
        @Schema(description = "新增数量")
        private Integer newCount = 0;
        
        @Schema(description = "重复跳过数量")
        private Integer duplicateCount = 0;
        
        @Schema(description = "错误数量")
        private Integer errorCount = 0;
        
        @Schema(description = "总处理数量")
        private Integer totalCount = 0;
    }
    
    @Data
    public static class ErrorDetail {
        @Schema(description = "错误行号")
        private Integer rowNumber;
        
        @Schema(description = "问题内容")
        private String question;
        
        @Schema(description = "错误信息")
        private String errorMessage;
    }
}
