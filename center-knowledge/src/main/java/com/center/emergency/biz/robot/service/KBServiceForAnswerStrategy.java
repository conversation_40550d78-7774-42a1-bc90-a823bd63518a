package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.chat.service.ChatResourceService;
import com.center.emergency.biz.chat.service.ChatTagService;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.robot.persitence.RobotModel;
import com.center.framework.common.pojo.IdAndValue;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import org.apache.commons.lang3.StringUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component("KNOWLEDGE_BASE")
@Slf4j
public class KBServiceForAnswerStrategy extends AbstractAnswerStrategy  implements AnswerStrategyService {

    @Resource
    private ChatResourceService chatResourceService;

    @Resource
    private ChatTagService chatTagService;

    @Resource
    private DepartRepository departRepository;

    @Resource
    private ModelGroupService modelGroupService;

    @Override
    public List<Long> listAnswerStrategyIds(Long robotId) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        List<Long> result = queryFactory.select(qRobotKnowledgeModel.kbId.as("id"))
                .from(qRobotKnowledgeModel).join(qKnowledgeBaseModel).on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .where(qRobotKnowledgeModel.robotId.eq(robotId))
                .fetch();

        return result;
    }

    /**
     * 知识库策略的参数构建 - 实现知识库特定的参数构建逻辑
     * 这是旧版本的参数构建接口，主要用于向后兼容
     */
    @Override
    public HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                                   List<Map<String, String>> tags, Boolean isSimulate,
                                                   Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("构建知识库策略对话参数: chatVO={}, kbIds.size={}, fileIds.size={}, modelId={}, isSimulate={}",
                chatVO.getQuestion(), kbIds.size(), fileIds.size(), modelId, isSimulate);

        // 1. 构建知识库策略基础参数
        HashMap<String, Object> param = buildKnowledgeBaseParams(chatVO, kbIds, fileIds, tags);

        // 2. 查询并添加模型配置
        if (modelId != null) {
            RobotModelDTO robotModelDTO = queryRobotModelConfig(chatVO.getRobotId(), modelId, jpaQueryFactory);
            // 只添加基础模型配置，策略特有参数由专门方法处理
            addBaseModelConfigParams(param, robotModelDTO);
            // 添加知识库策略特有的模型配置参数
            addKnowledgeBaseModelParams(param, robotModelDTO);
        } else {
            // 使用系统默认模型配置
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            param.putAll(defaultModelConfig);
        }

        // 3. 添加知识库策略专用的机器人配置参数
        addKnowledgeBaseRobotParams(param, chatVO.getRobotId(), jpaQueryFactory);

        log.info("知识库策略参数构建完成，传给算法的参数: {}", param);
        return param;
    }

    /**
     * 知识库策略的资源收集和参数构建 - 策略自主收集资源
     * 在策略内部收集知识库相关的资源，实现更好的职责分离
     */
    @Override
    public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                         Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
        log.info("知识库策略自主收集资源并构建参数: chatVO={}, modelGroupId={}, isSimulate={}",
                chatVO.getQuestion(), modelGroupId, isSimulate);

        // 1. 收集知识库策略特定的资源
        List<Map<String, String>> tags = chatTagService.getTagsFromRobot(chatVO.getRobotId(), jpaQueryFactory);
        Set<String> kbIds = new HashSet<>();
        List<String> fileIds = new ArrayList<>();
        chatResourceService.buildFromRobot(chatVO.getRobotId(), kbIds, fileIds, jpaQueryFactory, departRepository);

        log.info("知识库策略收集到的资源: kbIds.size={}, fileIds.size={}, tags.size={}",
                kbIds.size(), fileIds.size(), tags.size());

        // 2. 构建知识库策略基础参数
        HashMap<String, Object> param = buildKnowledgeBaseParams(chatVO, kbIds, fileIds, tags);

        // 3. 添加模型组配置
        addModelGroupParams(param, modelGroupId);

        // 4. 添加知识库策略专用的机器人配置参数
        addKnowledgeBaseRobotParams(param, chatVO.getRobotId(), jpaQueryFactory);

        log.info("知识库策略模型组参数构建完成，传给算法的参数: {}", param);
        return param;
    }

    /**
     * 构建知识库策略的基础参数
     * 包含知识库策略特有的参数格式
     */
    private HashMap<String, Object> buildKnowledgeBaseParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds, List<Map<String, String>> tags) {
        HashMap<String, Object> param = buildCommonBaseParams(chatVO);

        // 知识库策略特有参数
        param.put("kb_ids", kbIds);
        param.put("question", chatVO.getQuestion());
        param.put("user_id", "zyx"); // 知识库策略固定用户ID
        param.put("streaming", 1);
        param.put("tags_list", tags);

        return param;
    }

    /**
     * 添加知识库策略专用的机器人配置参数
     * 包括：search_mode、answer_mode、score_threshold、top_k
     * 这些参数只在知识库策略中使用
     */
    private void addKnowledgeBaseRobotParams(HashMap<String, Object> param, Long robotId, JPAQueryFactory jpaQueryFactory) {
        QRobotModel qRobotModel = QRobotModel.robotModel;

        RobotModel robotModel = jpaQueryFactory.selectFrom(qRobotModel)
                .where(qRobotModel.id.eq(robotId))
                .fetchOne();

        if (robotModel != null) {
            // 知识库策略专用参数（从数据库查询，不再写死）
            if (robotModel.getSearchMode() != null) {
                param.put("search_mode", Integer.valueOf(robotModel.getSearchMode().getDescription()));
            }
            if (robotModel.getAnswerMode() != null) {
                param.put("answer_mode", Integer.valueOf(robotModel.getAnswerMode().getDescription()));
            }
            if (robotModel.getMaxHits() != null) {
                param.put("top_k", robotModel.getMaxHits());
            }
            if (robotModel.getSimilarityThreshold() != null) {
                param.put("score_threshold", robotModel.getSimilarityThreshold());
            }

            log.info("添加知识库策略机器人配置: robotId={}, search_mode={}, answer_mode={}, top_k={}, score_threshold={}",
                    robotId, robotModel.getSearchMode(), robotModel.getAnswerMode(),
                    robotModel.getMaxHits(), robotModel.getSimilarityThreshold());
        } else {
            log.warn("机器人不存在，无法添加知识库策略配置: robotId={}", robotId);
        }
    }

    /**
     * 添加知识库策略特有的模型配置参数
     * 从RobotModelDTO中提取知识库策略特有的参数
     */
    private void addKnowledgeBaseModelParams(HashMap<String, Object> param, RobotModelDTO robotModelDTO) {
        if (robotModelDTO.getSearchMode() != null) {
            param.put("search_mode", Integer.valueOf(robotModelDTO.getSearchMode().getDescription()));
        }
        if (robotModelDTO.getAnswerMode() != null) {
            param.put("answer_mode", Integer.valueOf(robotModelDTO.getAnswerMode().getDescription()));
        }
        if (robotModelDTO.getTopK() != null) {
            param.put("top_k", robotModelDTO.getTopK());
        }
        if (robotModelDTO.getScoreThreshold() != null) {
            param.put("score_threshold", robotModelDTO.getScoreThreshold());
        }
        if (StringUtils.isNotBlank(robotModelDTO.getCustomPrompt())) {
            param.put("custom_prompt", robotModelDTO.getCustomPrompt());
        }

        log.info("添加知识库策略模型配置: search_mode={}, answer_mode={}, top_k={}, score_threshold={}",
                robotModelDTO.getSearchMode(), robotModelDTO.getAnswerMode(),
                robotModelDTO.getTopK(), robotModelDTO.getScoreThreshold());
    }
}
