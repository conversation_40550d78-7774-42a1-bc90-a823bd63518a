package com.center.emergency.biz.robot.pojo;

import com.center.emergency.common.enumeration.AnswerModeEnum;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RobotSaveReq extends RobotBase {

    //机器人ID
    @Schema(description = "机器人ID",example = "3")
    @NotNull(message = "机器人ID不能为空")
    private Long id;

    // 关联知识库
    @Schema(description = "关联Id列表，可能是MCP,数据库，知识库",example = "")
    private List<Long> answerStrategyIds;

    // 关联模型组ID
    @Schema(description = "机器人关联的模型组ID", example = "1001")
    private Long modelGroupId;

    // 系统提示词
    @Schema(description = "机器人系统提示词", example = "你是一个小可爱")
    private String systemPrompt;

    // 对话示例
    @Schema(description = "机器人对话示例", example = "用户：你好 机器人：您好，有什么我可以帮您的吗？")
    private List<DialogueExample> dialogueExamples;

    // 检索模式
    @Schema(description = "检索模式：VECTOR / TEXT / HYBRID", example = "HYBRID")
    @NotNull(message = "检索模式不能为空！")
    private SearchModeEnum searchMode;

    // 问答模式
    @Schema(description = "问答模式：ONLY_KB / KB_FIRST_MODEL / ONLY_QA", example = "KB_FIRST_MODEL")
    @NotNull(message = "问答模式不能为空！")
    private AnswerModeEnum answerMode;

    // 文本匹配相似度阈值
    @Schema(description = "文本匹配相似度阈值", example = "0.20")
    @DecimalMin(value = "0.00", inclusive = true, message = "相似度阈值不能小于0")
    @DecimalMax(value = "1.00", inclusive = true, message = "相似度阈值不能大于1")
    private BigDecimal similarityThreshold;

    // 最大召回数量
    @Schema(description = "最大召回数量", example = "20")
    @Min(value = 1, message = "最大召回数量不能小于1")
    @Max(value = 100, message = "最大召回数量不能大于100")
    private Integer maxHits;

    @Schema(description = "Agent问答策略",example = "MCP")
    @NotNull(message = "Agent问答策略不能为空")
    private AnswerStrategyEnum answerStrategy;
}
