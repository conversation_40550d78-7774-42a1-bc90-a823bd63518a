package com.center.emergency.biz.tag.persistence;

import com.center.emergency.common.enumeration.TagTypeEnum;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @description: 标签实体类
 * @date 2024/10/16 18:01
 */
@Data
@Entity
@Table(name = "center_tags")
public class TagModel extends BaseTenantModel {
    @Column(name = "tag_name",nullable = false, unique = true)
    @Schema(description = "标签名称")
    private String tagName;

    @Column(name = "remark")
    @Schema(description = "备注")
    private String remark;

    @Column(name = "tag_type")
    @Schema(description = "标签类型")
    @Enumerated(value = EnumType.STRING)
    private TagTypeEnum tagType;
}
