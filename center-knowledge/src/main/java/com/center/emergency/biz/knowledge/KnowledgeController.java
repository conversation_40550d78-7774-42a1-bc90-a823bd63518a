package com.center.emergency.biz.knowledge;

import com.center.emergency.biz.files.service.AsyncPythonApiService;
import com.center.emergency.biz.knowledge.persistence.KnowledgeRepository;
import com.center.emergency.biz.knowledge.pojo.*;
import com.center.emergency.biz.knowledge.service.KnowledgeService;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "知识管理")
@RequestMapping("/knowledge")
@Validated
public class KnowledgeController {

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private AsyncPythonApiService asyncPythonApiService;

    @Resource
    private KnowledgeRepository knowledgeRepository;

    @PostMapping("/createKnowledgeNoFile")
    @Operation(summary = "自定义添加知识qa对，为知识库单独生成知识，不用依赖文件")
    public CommonResult<String> createNoFile(@Valid @RequestBody KnowledgeNoFileCreateReq req) {
        knowledgeService.createKnowledgeNoFile(req);
        return CommonResult.successWithMessageOnly("知识创建成功");
    }
    @GetMapping("/listByKb/{kbId}")
    @Operation(summary = "根据知识库ID查询知识列表")
    @EnumConvertPoint
    public CommonResult<List<KnowledgeResp>> getKnowledgeByKnowledgeBaseId(@PathVariable Long kbId) {
        List<KnowledgeResp> knowledgeResps = knowledgeService.getKnowledgeByKnowledgeBaseId(kbId);
        return CommonResult.success(knowledgeResps);
    }

    @GetMapping("/listPageKnowledgeByKbid")
    @Operation(summary = "分页-根据知识库ID查询知识列表")
    @EnumConvertPoint
    public CommonResult<PageResult<KnowledgeResp>> getKnowledgeByKnowledgeBaseId(
            @Valid KnowledgeQueryPageReq knowledgeQueryPageReq) {
        // 调用服务层获取分页结果
        PageResult<KnowledgeResp> knowledgeResps = knowledgeService.getKnowledgeByKnowledgeBaseId(knowledgeQueryPageReq);
        return CommonResult.success(knowledgeResps);
    }



    @PostMapping("/update")
    @Operation(summary = "更新知识")
    public CommonResult<String> updateKnowledge(@Valid @RequestBody KnowledgeUpdateReq req) {
        knowledgeService.updateKnowledge(req);
        return CommonResult.successWithMessageOnly("知识更新成功");
    }

    @PostMapping("/submit/{id}")
    @Operation(summary = "知识提审")
    public CommonResult<String> submitKnowledge(@Valid @PathVariable Long id) {
        knowledgeService.submitKnowledge(id);
        return CommonResult.successWithMessageOnly("知识提审成功");
    }
    @PostMapping("/active/{id}")
    @Operation(summary = "知识启用")
    public CommonResult<String> activeKnowledge(@Valid @PathVariable Long id) {
        knowledgeService.activeKnowledge(id);
        return CommonResult.successWithMessageOnly("知识启用成功");
    }
    @PostMapping("/inactive/{id}")
    @Operation(summary = "知识停用")
    public CommonResult<String> inactiveKnowledge(@Valid @PathVariable Long id) {
        knowledgeService.inactiveKnowledge(id);
        return CommonResult.successWithMessageOnly("知识停用成功");
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除知识", description = "根据知识ID删除知识及其关联的FAQ")
    public CommonResult<String> deleteKnowledge(@Valid @PathVariable Long id) {
        knowledgeService.deleteKnowledge(id);
        return CommonResult.successWithMessageOnly("知识删除成功");
    }

    @GetMapping("/get/{id}")
    @Operation(summary = "获取知识详细信息")
    @EnumConvertPoint
    public CommonResult<KnowledgeResp> getKnowledgeById(@PathVariable Long id) {
        KnowledgeResp knowledgeResp = knowledgeService.getKnowledgeById(id);
        return CommonResult.success(knowledgeResp);
    }

    @GetMapping("/getQaWithCite/{id}")
    @Operation(summary = "获取知识的 QA 对信息", description = "根据文件ID获取知识的 QA 对信息，包括知识相关信息")
    @EnumConvertPoint
    public CommonResult<KnowledgeWithQaResp> getQaWithCite(@PathVariable Long id) {
        // 调用服务层方法获取知识和 QA 对
        KnowledgeWithQaResp knowledgeResp = knowledgeService.getKnowledgeWithQa(id);
        return CommonResult.success(knowledgeResp);
    }

    /**
     * 接收知识生成结果的回调通知
     *
     * @param knowledgeUpdateStatusReq 回调通知的 JSON 数据
     * @return 通用响应结果
     */
    @PostMapping("/updateStatusByPython")
    @Operation(summary = "算法回调接口，用于生成知识后算法回调进行状态更新", description = "传入文件ID，以及更新状态")
    @Transactional
    public CommonResult<String> updateKnowledgeStatus(@RequestBody KnowledgeUpdateStatusReq knowledgeUpdateStatusReq) {
        // 将请求对象传递给服务层处理
        knowledgeService.updateKnowledgeAndFileStatus(knowledgeUpdateStatusReq);
        return CommonResult.successWithMessageOnly("知识和文件状态更新成功");
    }

}