package com.center.emergency.biz.mcp.service;

import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.files.service.FileComponent;
import com.center.emergency.biz.files.service.FileService;
import com.center.emergency.biz.mcp.persistence.McpModel;
import com.center.emergency.biz.mcp.persistence.McpModelRepository;
import com.center.emergency.biz.mcp.persistence.QMcpModel;
import com.center.emergency.biz.mcp.pojo.McpCreateReq;
import com.center.emergency.biz.mcp.pojo.McpPageReq;
import com.center.emergency.biz.mcp.pojo.McpResp;
import com.center.emergency.biz.mcp.pojo.McpUpdateReq;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.stream.Collectors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.DUPLICATED_OBJECT;
import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OBJECT_NOT_EXISTED;

/**
 * MCP（Microservice Control Platform）服务接入与管理平台实现类：处理MCP的创建、更新、查询、删除操作。
 */
@Service
@Slf4j
public class McpServiceImpl implements McpService {

    @Autowired
    private McpModelRepository mcpModelRepository;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private FileService fileService;

    @Resource
    private FileComponent fileComponent;

    @Override
    @Transactional
    public Long createMcp(McpCreateReq req) {
        //1. 企业内名称判重
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        if (mcpModelRepository.existsByTenantIdAndMcpName(tenantId, req.getMcpName())) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "名称重复，请更换");
        }
        //2. 服务名企业内全局唯一
        if (mcpModelRepository.existsByTenantIdAndServerName(tenantId, req.getServerName())) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "Server Name重复，请更换");
        }

        //3. 保存并返回id
        McpModel mcpModel = OrikaUtils.convert(req, McpModel.class);
        return mcpModelRepository.save(mcpModel).getId();
    }

    @Override
    @Transactional
    public void deleteMcp(Long id) {
        // 1. 校验Mcp是否存在
        McpModel mcpModel = mcpModelRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "MCP不存在"));

        //2. 判断删除操作权限
        if (!mcpModel.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "没有删除MCP权限");
        }
        //3. 执行删除
        mcpModelRepository.deleteById(id);
    }

    @Override
    public void updateMcp(McpUpdateReq req) {
        Long mcpId = req.getMcpId();
        // 1. 校验Mcp是否存在
        McpModel mcpModel = mcpModelRepository.findById(mcpId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "MCP不存在"));

        //2. 判断操作权限
        if (!mcpModel.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "没有更新MCP权限");
        }

        //3. 企业内名称判重
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        if (!mcpModel.getMcpName().equals(req.getMcpName())) {
            if (mcpModelRepository.existsByTenantIdAndMcpName(tenantId, req.getMcpName())) {
                throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "名称重复，请更换");
            }
        }

        //4. 企业内服务名判重
        if (!mcpModel.getServerName().equals(req.getServerName())) {
            if (mcpModelRepository.existsByTenantIdAndServerName(tenantId, req.getServerName())) {
                throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "Server Name重复，请更换");
            }
        }

        //5. 更新属性
        OrikaUtils.copy(req, mcpModel);
        mcpModelRepository.save(mcpModel);
    }

    @Override
    public McpResp getMcp(Long id) {
        // 1. 校验Mcp是否存在
        McpModel mcpModel = mcpModelRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "MCP不存在"));

        //2. 判断操作权限
        if (!mcpModel.getTenantId().equals(LoginContextHolder.getLoginUserTenantId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "没有获取MCP详情权限");
        }

        //3. 构建返回
        McpResp mcpResp = OrikaUtils.convert(mcpModel, McpResp.class);
        mcpResp.setLogoUrl(fileComponent.previewUrlById(mcpResp.getLogoId()));
        return mcpResp;
    }

    @Override
    public List<McpResp> listMcp(String keyword) {
        //1. 构建查询条件
        QMcpModel qMcp = QMcpModel.mcpModel;
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(keyword)) {
            builder.and(qMcp.mcpName.containsIgnoreCase(keyword))
                    .or(qMcp.mcpDesc.containsIgnoreCase(keyword));
        }

        List<McpModel> mcpModeList = jpaQueryFactory.selectFrom(qMcp)
                .where(builder)
                .fetch();

        List<McpResp> mcpRespList = mcpModeList.stream().map(model -> {
            McpResp mcpResp = OrikaUtils.convert(model, McpResp.class);
            if (mcpResp.getLogoId()!=null){
                mcpResp.setLogoUrl(fileComponent.previewUrlById(mcpResp.getLogoId()));
            }
            return mcpResp;
        }).collect(Collectors.toList());

        return mcpRespList;
    }

    @Override
    public PageResult<McpResp> pageMcp(McpPageReq req) {
        // 1. 构建分页条件
        Pageable pageable = PageRequest.of(req.getPageNo() - 1, req.getPageSize());

        //2. 构建查询条件
        QMcpModel qMcpModel = QMcpModel.mcpModel;
        QFileModel qFileModel = QFileModel.fileModel;
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(req.getKeyword())) {
            builder.and(qMcpModel.mcpName.containsIgnoreCase(req.getKeyword()))
                    .or(qMcpModel.mcpDesc.containsIgnoreCase(req.getKeyword()));
        }
        builder.and(qMcpModel.tenantId.eq(LoginContextHolder.getLoginUserTenantId()));

        JPQLQuery<McpResp> jpqlQuery = jpaQueryFactory.select((
                        Projections.bean(
                                McpResp.class,
                                qMcpModel.id,
                                qMcpModel.mcpName,
                                qMcpModel.mcpDesc,
                                qMcpModel.installMethod,
                                qMcpModel.serverName,
                                qMcpModel.logoId,
                                qFileModel.hdfsPath
                        )))
                .from(qMcpModel)
                .leftJoin(qFileModel).on(qMcpModel.logoId.eq(qFileModel.id))
                .where(builder)
                .orderBy(qMcpModel.updateTime.desc()) //按 updateTime 倒序排列
                .offset(pageable.getOffset()) // 使用 pageable 的偏移量
                .limit(pageable.getPageSize()); // 设置每页记录数;

        Long total = jpqlQuery.fetchCount();
        List<McpResp> mcpModeList = jpqlQuery.fetch();

        for (McpResp mcpResp : mcpModeList) {
            if (mcpResp.getLogoId()!=null){
                mcpResp.setLogoUrl(fileComponent.previewUrlById(mcpResp.getLogoId()));
            }
        }

        //3.  构建分页响应并返回
        return PageResult.of(mcpModeList, total);
    }


}