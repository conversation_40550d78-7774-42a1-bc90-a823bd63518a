package com.center.emergency.biz.knowledge.pojo;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;
/**
 * 知识基础类，定义通用的字段
 * <AUTHOR>
 */
@Data
public class KnowledgeBase {

    // 知识库ID
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private Long kbId;

    // 知识名称
    @NotNull(message = "知识名称不能为空")
    @Length(max = 100, message = "知识名称长度不能超过100个字符")
    @Schema(description = "知识名称")
    private String knowledgeName;

    // 关联文件ID（可选）
    @Schema(description = "关联文件ID")
    private Long fileId;

//    // 模型知识库的知识ID
//
//    @Schema(description = "模型知识库的知识ID")
//    private Long aiFaqbFaqId;
//
//    // 模型文件库的文件ID（可选）
//    @Schema(description = "模型文件库的文件ID")
//    private Long aiFilebFileId;

    // 知识状态
    @Enumerated(EnumType.STRING)
    @Schema(description = "知识状态")
    private KnowledgeStatusEnum status;
}