package com.center.emergency.biz.knowledgebase.service;


import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.JSONObject;
import com.center.cache.factory.CacheFactory;
import com.center.emergency.biz.approval.service.RecordService;
import com.center.emergency.biz.files.persistence.FileRepository;
import com.center.emergency.biz.files.persistence.FileTagRepository;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.knowledge.persistence.KnowledgeRepository;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.pojo.*;
import com.center.emergency.biz.robot.persitence.RobotKBRepository;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.biz.tag.persistence.TagModel;
import com.center.emergency.biz.tag.persistence.TagRepository;
import com.center.emergency.biz.tag.pojo.TagResp;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.knowledge.KnowledgeApiTool;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.storage.factory.FileStorageServiceFactory;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.template.FilePathBuilder;
import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.center.infrastructure.system.biz.depart.persistence.QDepartModel;
import com.center.infrastructure.system.biz.depart.service.DepartService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 知识库服务实现类：处理知识库的创建、更新、查询、删除操作。
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Autowired
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Autowired
    private FileRepository fileRepository;

    @Autowired
    private KnowledgeRepository knowledgeRepository;

    @Resource
    private RecordService recordService;

    @Autowired
    private FileStorageServiceFactory fileStorageServiceFactory;

    @Autowired
    private FileTagRepository fileTagRepository;
    @Autowired
    private RobotKBRepository robotKBRepository;
    @Autowired
    private DepartRepository departRepository;
    @Autowired
    private TagRepository tagRepository;
    @Resource
    private DepartService departService;
    @Value("${python.api-base-url}")
    private String pythonApiBaseUrl;

    @Resource
    private JPAQueryFactory queryFactory;
    @Resource
    private CacheFactory cacheFactory;
    @Value("${cache.type}")
    private String cacheType;

    /**
     * 创建知识库
     * 使用 HttpUtil 调用 Python 接口
     *
     * @param req 创建知识库的请求数据
     */
    @Override
    public void createKnowledgeBase(KnowledgeBaseCreateReq req) {
        // 1. 校验知识库名称是否重复
        boolean exists = knowledgeBaseRepository.existsByKbNameAndDepartmentId(req.getKbName(), req.getDepartmentId());
        if (exists) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "部门下已存在同名知识库,请重新输入");
        }

        // 2. 构建Python接口的请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_name", req.getKbName());

        // 3. 调用Python接口
        JSONObject responseJson;
        try {
            responseJson = KnowledgeApiTool.post(pythonApiBaseUrl + "/api/local_doc_qa/new_knowledge_base_and_faq_base", requestBody);
        } catch (Exception e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "算法服务异常");
        }

        // 4. 解析返回的JSON字符串
        String code = responseJson.getString("code");
        if (!"200".equals(code)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "算法服务异常");
        }

        // 5. 提取返回的kb_id和kb_id_faq
        JSONObject data = responseJson.getJSONObject("data");
        String kbId = data.getString("kb_id");
        String kbIdFaq = data.getString("kb_id_faq");

        if (kbId == null || kbIdFaq == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "算法服务异常");
        }

        // 6. 保存到数据库
        KnowledgeBaseModel model = OrikaUtils.convert(req, KnowledgeBaseModel.class);
        model.setTenantId(LoginContextHolder.getLoginUserTenantId());
        ;
        model.setAiFilebId(kbId);
        model.setAiFaqbId(kbIdFaq);
        knowledgeBaseRepository.save(model);
    }


    /**
     * 更新已有的知识库
     *
     * @param req 更新知识库的请求对象，包含知识库ID
     * @return 更新后的知识库信息
     */
    @Override
    public KnowledgeBaseResp updateKnowledgeBase(KnowledgeBaseUpdateReq req) {
        // 1. 检查是否存在相同部门下的同名知识库
        Specification<KnowledgeBaseModel> spec = (root, query, cb) -> {
            // 部门ID相同
            Predicate departmentPredicate = cb.equal(root.get("departmentId"), req.getDepartmentId());

            // 知识库名称相同
            Predicate namePredicate = cb.equal(root.get("kbName"), req.getKbName());

            // 排除当前正在更新的知识库ID
            Predicate idNotPredicate = cb.notEqual(root.get("id"), req.getId());

            // 组合条件
            return cb.and(departmentPredicate, namePredicate, idNotPredicate);
        };

        boolean exists = knowledgeBaseRepository.exists(spec);
        if (exists) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "部门下已存在同名知识库，请重新输入");
        }

        // 2. 根据ID查找知识库实体，若不存在则抛出异常
        KnowledgeBaseModel model = knowledgeBaseRepository.findById(req.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

        // 3. 更新知识库名称以及知识库路径
        model.setKbName(req.getKbName());
        model.setDepartmentId(req.getDepartmentId());

        // 4. 保存更新后的知识库实体
        KnowledgeBaseModel updated = knowledgeBaseRepository.save(model);

        // 5. 将更新后的实体转换为响应对象并返回
        return OrikaUtils.convert(updated, KnowledgeBaseResp.class);
    }


    /**
     * 删除知识库（物理删除），同时删除关联的文件、知识和文件标签关联。
     *
     * @param id 知识库的ID
     */
    @Override
    @Transactional
    public void deleteKnowledgeBase(Long id) {
        // 1. 根据ID从数据库获取知识库实体
        KnowledgeBaseModel knowledgeBase = knowledgeBaseRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

        // 2. 检查是否有机器人引用该知识库
        boolean isReferencedByRobot = robotKBRepository.existsByKbId(id);
        if (isReferencedByRobot) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "有机器人引入该知识库，请解绑后删除");
        }
        // 3. 调用 Python 接口删除知识库和 FAQ 知识库
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_ids", Arrays.asList(knowledgeBase.getAiFilebId()));

        JSONObject responseJson;
        try {
            responseJson = KnowledgeApiTool.post(pythonApiBaseUrl + "/api/local_doc_qa/delete_knowledge_base_and_faq_base", requestBody);
        } catch (Exception e) {
            log.error("调用Python API删除知识库失败: {}", e.getMessage());
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, "删除知识库失败");
        }

        // 解析 API 返回结果
        if (!"200".equals(responseJson.getString("code"))) {
            log.error("Python API 删除知识库失败，错误信息: {}");
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, "删除知识库失败");
        }

        // 4. 删除数据库中的关联文件
        fileRepository.deleteByKbId(knowledgeBase.getId());

        // 4.1 构建HDFS路径
        FilePathBuilder filePathBuilder = new FilePathBuilder();
        String dfsPath = filePathBuilder.buildFilePath(LoginContextHolder.getLoginUserTenantId().toString(), id.toString());

        // 5. 删除对应知识库目录。如果没有跳过
        FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
        try {
            fileStorageService.deleteDirectory(dfsPath);
        } catch (Exception e) {
            log.warn("删除HDFS目录失败", e);
        }

        // 6. 删除数据库中的关联知识
        knowledgeRepository.deleteByKbId(knowledgeBase.getId());

        // 7. 删除文件标签关联关系
        fileTagRepository.deleteByKbId(knowledgeBase.getId());

        // 8. 删除审批记录
        recordService.recordDelete(knowledgeBase.getId());

        // 9. 删除数据库中的知识库记录
        knowledgeBaseRepository.deleteById(id);
    }


    /**
     * 根据ID获取知识库详细信息
     *
     * @param id 知识库的ID
     * @return 知识库的详细信息
     */
    @Override
    public KnowledgeBaseResp getKnowledgeBaseByIdWithTags(Long id) {
        QFileTagModel fileTagModel = QFileTagModel.fileTagModel;
        QTagModel tagModel = QTagModel.tagModel;
        QKnowledgeBaseModel knowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;
        QDepartModel departModel = QDepartModel.departModel;

        // 查询知识库基本信息和关联的部门名称
        Tuple knowledgeBaseTuple = queryFactory
                .select(knowledgeBaseModel, departModel.departName)
                .from(knowledgeBaseModel)
                .leftJoin(departModel).on(knowledgeBaseModel.departmentId.eq(departModel.id))
                .where(knowledgeBaseModel.id.eq(id))
                .fetchOne();

        if (knowledgeBaseTuple == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在");
        }

        // 获取知识库模型和部门名称
        KnowledgeBaseModel knowledgeBase = knowledgeBaseTuple.get(knowledgeBaseModel);
        String departmentName = knowledgeBaseTuple.get(departModel.departName);

        // 查询该知识库下的所有标签（去重）
        List<TagModel> tags = queryFactory
                .select(tagModel)
                .distinct()
                .from(fileTagModel)
                .leftJoin(tagModel).on(fileTagModel.tagId.eq(tagModel.id))
                .where(fileTagModel.kbId.eq(id))
                .fetch();

        // 过滤掉 null 标签，并转换为响应对象
        List<TagResp> tagResps = tags.stream()
                .filter(Objects::nonNull) // 过滤掉 null 值
                .map(tag -> OrikaUtils.convert(tag, TagResp.class))
                .collect(Collectors.toList());

        // 转换为响应对象
        KnowledgeBaseResp resp = OrikaUtils.convert(knowledgeBase, KnowledgeBaseResp.class);

        // 设置标签信息和部门名称
        resp.setTags(tagResps);
        resp.setDepartmentName(departmentName);

        return resp;
    }


    /**
     * 根据查询条件获取知识库列表
     */
    @Override
    public List<KnowledgeBaseResp> getKnowledgeBases(KnowledgeBaseQueryReq queryReq) {
        // 动态构建查询条件
        Specification<KnowledgeBaseModel> spec = Specification.where(null);

        // 根据部门ID查询
        if (Objects.nonNull(queryReq.getDepartmentId())) {
            if (queryReq.getDepartmentId() == 0) {
                // 查询公司层级的知识库
                spec = spec.and((root, query, cb) -> cb.isNull(root.get("departmentId")));
            } else {
                // 查询特定部门的知识库
                spec = spec.and((root, query, cb) -> cb.equal(root.get("departmentId"), queryReq.getDepartmentId()));
            }
        }

        // 根据租户ID查询
        if (Objects.nonNull(queryReq.getTenantId())) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("tenantId"), queryReq.getTenantId()));
        }

        // 根据知识库名称模糊查询
        if (Objects.nonNull(queryReq.getKbName()) && !queryReq.getKbName().isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.like(root.get("kbName"), "%" + queryReq.getKbName() + "%"));
        }

        // 添加更新时间倒序排序
        Sort sort = Sort.by(Sort.Direction.DESC, "updateTime");

        // 执行查询并添加排序条件
        List<KnowledgeBaseModel> models = knowledgeBaseRepository.findAll(spec, sort);

        // 将查询结果转换为响应对象并返回
        return models.stream()
                .map(model -> OrikaUtils.convert(model, KnowledgeBaseResp.class))
                .collect(Collectors.toList());
    }


    @Override
    public List<KnowledgeBaseResp> getKnowledgeBasesByDeptId(Long deptId, String path) {
        // 获取当前用户的租户和部门信息
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        String userDepartId = LoginContextHolder.getLoginUserDepartId().toString();

        // 查询部门信息
        Optional<DepartModel> optionalDepartment = departRepository.findById(deptId);
        if (!optionalDepartment.isPresent()) {
            // 使用全局错误码返回错误信息
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_FOUND, "部门不存在，部门ID: " + deptId);
        }
        DepartModel department = optionalDepartment.get();

        // 判断是否为根部门（假设根部门的父ID为0）
        boolean isRootDepartment = department.getParentId() == 0;

        // 校验是否符合登录用户部门权限
        if (!isRootDepartment && !isPermission(deptId)) {
            return Collections.emptyList();
        }

        // 获取部门名称
        String departmentName = department.getDepartName();

        // 根据部门ID和租户ID查询知识库，并根据更新时间倒序排序
        List<KnowledgeBaseModel> knowledgeBases = knowledgeBaseRepository
                .findByDepartmentIdAndTenantId(
                        deptId,
                        tenantId,
                        Sort.by(Sort.Direction.DESC, "updateTime")
                );

        if (knowledgeBases.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为响应对象列表，并设置部门名称
        return knowledgeBases.stream()
                .map(knowledgeBase -> {
                    KnowledgeBaseResp resp = OrikaUtils.convert(knowledgeBase, KnowledgeBaseResp.class);
                    resp.setDepartmentName(departmentName); // 设置部门名称
                    return resp;
                })
                .collect(Collectors.toList());
    }

    private boolean isPermission(Long deptId) {
        List<String> parentsId = cacheFactory.getListCache().get(departService.getParentsIdKey(deptId));
        if (parentsId == null) {
            parentsId = departService.getParentsId((Tree<String>) cacheFactory.getHashCache().get(deptId.toString()), true);
            cacheFactory.getListCache().put(departService.getParentsIdKey(deptId), parentsId);
        }
        return parentsId.contains(LoginContextHolder.getLoginUserDepartId().toString());
    }


    @Override
    public SearchResult searchKBases(String keyWord) {

        //1. 调用方法获取部门树
        List<Tree<String>> departmentTree = departService.getDepartTree();
        //1.1 构建所有部门 ID 的列表
        List<Long> departmentIds = new ArrayList<>();
        for (Tree<String> tree : departmentTree) {
            collectDepartmentIds(tree, departmentIds);
        }

        // 2. 查询符合条件的知识库名称以及部门名称
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;
        QDepartModel qDepartModel = QDepartModel.departModel;
        // 2.1 构建查询条件
        BooleanBuilder whereBuilder = new BooleanBuilder();
        whereBuilder.and(qKnowledgeBaseModel.kbName.contains(keyWord));
        whereBuilder.and(qDepartModel.id.in(departmentIds));

        // 2.2 查询知识库基本信息和关联的部门名称
        JPQLQuery<KnowledgeBaseDetail> jpqlQuery = queryFactory.select(Projections.bean(
                        KnowledgeBaseDetail.class,
                        qKnowledgeBaseModel.id,
                        qKnowledgeBaseModel.kbName,
                        qKnowledgeBaseModel.departmentId,
                        qDepartModel.departName,
                        Expressions.stringTemplate("CONCAT({0},' > ', {1})", qDepartModel.departName, qKnowledgeBaseModel.kbName).as("pathName") //// 拼接 departName 和 kbName
                ))
                .from(qKnowledgeBaseModel)
                .leftJoin(qDepartModel).on(qKnowledgeBaseModel.departmentId.eq(qDepartModel.id))  // left join 确保所有知识库都返回
                .where(whereBuilder)
                .orderBy(qDepartModel.departName.desc());

        // 3 获取查询结果
        List<KnowledgeBaseDetail> resultList = jpqlQuery.fetch();

        // 4.设置返回内容
        SearchResult searchResult = new SearchResult();
        searchResult.setKnowledgeBases(resultList);
        searchResult.setFiles(new ArrayList<>());
        // 5. 返回
        return searchResult;
    }


    /**
     * 递归遍历部门树，收集所有部门 ID
     */
    private void collectDepartmentIds(Tree<String> tree, List<Long> departmentIds) {
        if (tree == null) {
            return;
        }
        departmentIds.add(Long.parseLong(tree.getId()));
        if (tree.getChildren() != null) {
            for (Tree<String> child : tree.getChildren()) {
                collectDepartmentIds(child, departmentIds);
            }
        }
    }
}