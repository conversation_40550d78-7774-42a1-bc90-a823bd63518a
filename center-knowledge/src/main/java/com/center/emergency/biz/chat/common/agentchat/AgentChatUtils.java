package com.center.emergency.biz.chat.common.agentchat;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.common.utils.OkHttpUtils;
import com.center.framework.web.pojo.CommonResult;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;

public class AgentChatUtils {
    private static final Logger log = LoggerFactory.getLogger(AgentChatUtils.class);
    private static final String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";
    
    /**
     * 异步执行SSE聊天请求
     * 功能：通过SSE（Server-Sent Events）方式异步发起聊天请求，支持实时流式响应
     * 使用场景：所有聊天场景（知识库对话、机器人对话、重新回答）
     * 核心逻辑：
     * 1. 使用CompletableFuture异步执行
     * 2. 创建OkHttp客户端和SSE连接
     * 3. 通过ListenerFactory创建事件监听器处理响应
     * 4. 等待CountDownLatch确保请求完成
     * 5. 异常时发送错误事件给前端
     *
     * 建议重命名：executeAsyncSseChat 或 startAsyncSseChatRequest
     *
     * @param emitter SSE发射器，用于向前端发送实时数据
     * @param chatVO 聊天VO对象，包含问题和机器人信息
     * @param url 算法服务的请求URL
     * @param param 请求参数映射
     * @param isSimulate 是否模拟模式（影响数据库操作）
     * @param executor 异步执行器
     * @param parser 响应解析器（由调用方构造）
     * @param listenerFactory 监听器工厂（避免循环依赖）
     */
    public static void executeAsyncSseChat(SseEmitter emitter,
                                           ChatVO chatVO,
                                           String url,
                                           HashMap<String, Object> param,
                                           Boolean isSimulate,
                                           Executor executor,
                                           Object parser, // 由调用方构造
                                           Object listenerFactory // listener工厂，避免循环依赖
    ) {
        log.info("发起SSE异步请求, URL: {}, 参数: {}", url, param);
        
        CompletableFuture.runAsync(() -> {
            Object listener = null;
            if (listenerFactory instanceof ListenerFactory) {
                listener = ((ListenerFactory) listenerFactory).create(parser, emitter, chatVO, isSimulate);
            }
            try {
                // 使用OkHttpUtils创建client和request
                OkHttpClient client = OkHttpUtils.getOkHttpClient();
                Request request = OkHttpUtils.getJsonPostRequest(url, param);
                
                log.info("创建SSE请求, URL: {}", url);
                
                EventSource.Factory factory = EventSources.createFactory(client);
                factory.newEventSource(request, (okhttp3.sse.EventSourceListener) listener);
                
                log.info("SSE请求已建立连接, 等待数据流结束");
                
                if (parser instanceof CountDownLatchHolder) {
                    ((CountDownLatchHolder) parser).getLatch().await();
                }
                
                log.info("SSE请求已完成");
            } catch (Exception e) {
                log.error("SSE请求异常: {}", e.getMessage(), e);
                try {
                    emitter.send(
                        SseEmitter.event()
                            .name("error")
                            .data(CommonResult.error(
                                    500, 
                                    CHAT_DEFAULT_ERROR_MESSAGE,
                                    e.getMessage())));
                } catch (IOException ex) {
                    log.error("发送error事件失败: {}", ex.getMessage());
                }
            }
        }, executor);
    }

    /**
     * 监听器工厂接口
     * 用于创建SSE事件监听器，避免循环依赖
     */
    public interface ListenerFactory {
        Object create(Object parser, SseEmitter emitter, ChatVO chatVO, Boolean isSimulate);
    }

    /**
     * CountDownLatch持有者接口
     * 用于等待异步操作完成
     */
    public interface CountDownLatchHolder {
        CountDownLatch getLatch();
    }
}