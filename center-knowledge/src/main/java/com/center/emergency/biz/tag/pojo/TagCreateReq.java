package com.center.emergency.biz.tag.pojo;

import com.netease.yidun.sdk.core.validation.limitation.NotBlank;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class TagCreateReq {

    @NotBlank(message = "标签名称不能为空")
    @Length(max = 50, message = "标签名称长度不能超过50个字符")
    @Schema(description = "标签名称", example = "AI")
    private String tagName;
}
