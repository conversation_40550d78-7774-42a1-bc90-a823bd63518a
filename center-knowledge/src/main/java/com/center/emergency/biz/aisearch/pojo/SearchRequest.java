package com.center.emergency.biz.aisearch.pojo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.*;
import java.util.List;

/**
 * AI搜索请求参数
 */
@Data
@Schema(description = "AI搜索请求参数")
public class SearchRequest {
    
    /**
     * 搜索问题
     */
    @Schema(description = "搜索问题", required = true, example = "如何提高工作效率？")
    @NotBlank(message = "搜索问题不能为空")
    @Size(max = 2000, message = "搜索问题长度不能超过2000个字符")
    private String question;
    
    /**
     * 搜索模式：1-全网搜索，2-企业内部搜索
     */
    @Schema(description = "搜索模式：1-全网搜索，2-企业内部搜索", required = true, example = "1")
    @NotNull(message = "搜索模式不能为空")
    @Min(value = 1, message = "搜索模式必须为1或2")
    @Max(value = 2, message = "搜索模式必须为1或2")
    private Integer searchMode;
    
    /**
     * 搜索引擎类型（全网搜索时使用）
     */
    @Schema(description = "搜索引擎类型", example = "tavily")
    @Size(max = 50, message = "搜索引擎类型长度不能超过50个字符")
    private String searchEngine = "tavily";
    
    /**
     * 最大搜索结果数
     */
    @Schema(description = "最大搜索结果数", example = "10")
    @Min(value = 1, message = "最大搜索结果数必须大于0")
    @Max(value = 100, message = "最大搜索结果数不能超过100")
    private Integer maxResults = 10;
    
    /**
     * 知识库ID列表（企业内部搜索时使用）
     * 如果提供，系统会验证用户权限并仅搜索有权限的知识库；
     * 如果为空，系统将自动获取用户所有有权限的知识库进行搜索
     */
    @Schema(description = "知识库ID列表（可选，用于指定搜索范围）")
    @Size(max = 100, message = "知识库ID列表最多支持100个")
    private List<@NotNull(message = "知识库ID不能为空") Long> kbIds;
    
    /**
     * 文件ID列表（企业内部搜索时使用）
     * 如果指定了kbIds，此字段通常无需设置；
     * 系统会根据权限验证后的知识库自动构建文件范围
     */
    @Schema(description = "文件ID列表（可选，系统会根据知识库权限自动构建）")
    @Size(max = 1000, message = "文件ID列表最多支持1000个")
    private List<@NotNull(message = "文件ID不能为空") Long> fileIds;
    
    /**
     * 会话ID（续聊时提供）
     */
    @Schema(description = "会话ID（续聊时提供）", example = "1234567890")
    @Positive(message = "会话ID必须为正数")
    private Long sessionId;
} 