package com.center.emergency.biz.knowledge.persistence;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface KnowledgeRepository extends JpaRepository<KnowledgeModel, Long> {
    // 可添加额外查询方法
    List<KnowledgeModel> findAllByKbId(Long kbId);
    Optional<KnowledgeModel> findByFileId(Long fileId);
    void deleteByKbId(Long kbId);

    void deleteByFileId(Long fileId);

    // 检查同一个知识库下是否存在相同的知识名称
    boolean existsByKbIdAndKnowledgeName(Long kbId, String knowledgeName);

    // 检查同一个知识库下是否存在相同的知识名称,修改名称的时候需要，进行判断本id之外的。
    boolean existsByKbIdAndKnowledgeNameAndIdNot(Long kbId, String knowledgeName,Long id);

    List<KnowledgeModel> findByKbId(Long knowledgeBaseId);

}