package com.center.emergency.biz.chat.persistence;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import com.center.framework.db.core.BaseModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import javax.persistence.*;


@Data
@Entity
@QueryEntity
@Table(name = "center_chat_session")
public class ChatSessionModel extends BaseModel {

    @Column(name = "robot_id",nullable = false)
    private Long robotId;

    @Column(name = "title",nullable = false)
    private String title;

    @Column(name = "chat_type",nullable = false)
    @Enumerated(value = EnumType.STRING)
    private ChatTypeEnum chatType;
}
