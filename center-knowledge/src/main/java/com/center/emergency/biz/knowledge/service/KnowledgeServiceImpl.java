package com.center.emergency.biz.knowledge.service;


import cn.hutool.core.util.StrUtil;
import com.center.emergency.biz.approval.service.RecordService;
import com.center.emergency.biz.files.persistence.FileModel;
import com.center.emergency.biz.files.persistence.FileRepository;
import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.files.pojo.FileResp;
import com.center.emergency.biz.files.pojo.KnowledgeFileUpdateMessage;
import com.center.emergency.biz.files.service.AsyncPythonApiService;
import com.center.emergency.biz.files.service.FileComponent;
import com.center.emergency.biz.knowledge.persistence.KnowledgeModel;
import com.center.emergency.biz.knowledge.persistence.KnowledgeRepository;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.knowledge.pojo.*;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.emergency.biz.knowledgebase.pojo.KnowledgeBaseResp;
import com.center.emergency.biz.knowledgebase.service.KnowledgeBaseService;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.biz.tag.persistence.TagModel;
import com.center.emergency.biz.tag.pojo.TagFileRsp;
import com.center.emergency.biz.webskt.WebSocketServer;
import com.center.emergency.common.enumeration.FileStatusEnum;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.emergency.common.enumeration.RecordStatusEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.db.config.SnowFlakeConfig;
import com.center.framework.web.pojo.PageResult;
import com.center.framework.web.pojo.SortProperties;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识模块业务实现类
 */
@Slf4j
@Service
public class KnowledgeServiceImpl implements KnowledgeService {
    @Resource
    private JPAQueryFactory queryFactory;
    @Resource
    private KnowledgeRepository knowledgeRepository;
    @Resource
    private FileRepository fileRepository;
    @Resource
    private RecordService recordService;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;
    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @Resource
    private AsyncPythonApiService asyncPythonApiService;

    @Resource
    private SnowFlakeConfig snowFlakeConfig;

    @Resource
    private UserRepository userRepository;

    @Resource
    private FileComponent fileComponent;
    /**
     * 创建知识
     *
     * @param req 知识创建请求
     */
    @Override
    @Transactional
    public void createKnowledge(KnowledgeCreateReq req) {
        try {
            // 将 KnowledgeCreateReq 转换为 KnowledgeModel 实体
            KnowledgeModel knowledgeModel = OrikaUtils.convert(req, KnowledgeModel.class);

            // 保存到数据库
            knowledgeRepository.save(knowledgeModel);

            // 记录保存状态
            recordService.RecordSave(knowledgeModel.getId(), RecordStatusEnum.CREATE, null);

        } catch (IllegalArgumentException e) {
            // 处理参数异常
            log.error("创建知识时参数错误: {}", req, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "创建知识失败"
            );
        } catch (DataAccessException e) {
            // 处理数据库异常
            log.error("创建知识时数据库操作失败: {}", req, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "创建知识失败"
            );
        } catch (Exception e) {
            // 捕获其他所有异常
            log.error("创建知识时发生未知错误: {}", req, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "创建知识失败"
            );
        }
    }
    /**
     * 创建知识条目但不涉及文件的操作
     * 此方法用于处理不需要上传或关联文件的知识创建请求它可能涉及其他类型的知识内容处理，
     * 如纯文本、链接等，确保知识库的完整性与多样性
     *
     * @param req 知识创建请求对象，但不包含文件数据
     */
    @Override
    @Transactional
    public void createKnowledgeNoFile(KnowledgeNoFileCreateReq req) {
        try {
            // 检查知识库 ID 是否存在
            KnowledgeBaseModel kb = knowledgeBaseRepository.findById(req.getKbId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

            String knowledgeName = req.getKnowledgeName();
            // 检查同一个知识库下是否存在相同的知识名称
            boolean exists = knowledgeRepository.existsByKbIdAndKnowledgeName(req.getKbId(), knowledgeName);
            if (exists) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "该知识库下的知识名称已存在");
            }

            long id = snowFlakeConfig.snowFlakeCore().nextId();
            List<TagFileRsp> tagFileRasps = new ArrayList<>();

            // 创建新的知识记录
            KnowledgeModel knowledge = new KnowledgeModel();
            knowledge.setId(id);
            knowledge.setKnowledgeName(knowledgeName);
            knowledge.setKbId(req.getKbId());
            knowledge.setFileId(id);
            req.setFileId(id);
            knowledge.setStatus(KnowledgeStatusEnum.PENDING_APPROVAL);
            knowledgeRepository.save(knowledge);

            KnowledgeBaseResp knowledgeBaseByIdWithTags = knowledgeBaseService.getKnowledgeBaseByIdWithTags(req.getKbId());
            if (!knowledgeBaseByIdWithTags.getTags().isEmpty()) {
                tagFileRasps = OrikaUtils.convertList(knowledgeBaseByIdWithTags.getTags(), TagFileRsp.class);
            }

            // 验证 QA 对
            asyncPythonApiService.updateFaqsOnPythonApi(req, tagFileRasps);

            // 保存记录
            recordService.RecordSave(knowledge.getId(), RecordStatusEnum.CREATE, null);

        } catch (IllegalArgumentException e) {
            // 处理参数异常
            log.error("创建无文件知识时参数错误: {}", req, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "创建知识失败"
            );
        } catch (DataAccessException e) {
            // 处理数据库异常
            log.error("创建无文件知识时数据库操作失败: {}", req, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "创建知识失败"

            );
        } catch (Exception e) {
            // 捕获其他所有异常
            log.error("创建无文件知识时发生未知错误: {}", req, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "创建知识失败"
            );
        }
    }
    @Override
    public List<KnowledgeResp> getKnowledgeByKnowledgeBaseId(Long kbId) {
        try {
            QKnowledgeModel knowledgeModel = QKnowledgeModel.knowledgeModel;
            QUserModel userModel = QUserModel.userModel;

            // 构建查询条件
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(knowledgeModel.kbId.eq(kbId));

            // 动态查询
            List<Tuple> queryResults = queryFactory
                    .select(knowledgeModel, userModel.displayName)
                    .from(knowledgeModel)
                    .leftJoin(userModel).on(knowledgeModel.updaterId.eq(userModel.id))
                    .where(builder)
                    .fetch();

            if (queryResults.isEmpty()) {
                log.warn("知识库ID {} 未查询到相关知识", kbId);
                return Collections.emptyList();
            }

            // 结果转换
            List<KnowledgeResp> knowledgeResps = queryResults.stream().map(tuple -> {
                KnowledgeModel knowledge = tuple.get(knowledgeModel);
                String displayName = tuple.get(userModel.displayName);
                KnowledgeResp resp = OrikaUtils.convert(knowledge, KnowledgeResp.class);
                resp.setOperator(displayName);
                return resp;
            }).collect(Collectors.toList());

            return knowledgeResps;

        } catch (IllegalArgumentException e) {
            // 处理参数异常
            log.error("获取知识时参数错误，知识库ID: {}", kbId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "获取知识失败"
            );
        } catch (DataAccessException e) {
            // 处理数据库异常
            log.error("获取知识时数据库操作失败，知识库ID: {}", kbId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "获取知识失败"
            );
        } catch (Exception e) {
            // 捕获其他所有异常
            log.error("获取知识时发生未知错误，知识库ID: {}", kbId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "获取知识失败"
            );
        }
    }
    /**
     * 根据知识库ID获取知识列表
     *
     * @param knowledgeQueryPageReq@return 返回知识响应对象的分页结果
     */
    @Override
    public PageResult<KnowledgeResp> getKnowledgeByKnowledgeBaseId(KnowledgeQueryPageReq knowledgeQueryPageReq) {
        try {
        QKnowledgeModel knowledgeModel = QKnowledgeModel.knowledgeModel;
        QUserModel userModel = QUserModel.userModel;

        // 获取分页参数
        Integer pageNo = knowledgeQueryPageReq.getPageNo();
        Integer pageSize = knowledgeQueryPageReq.getPageSize();
        Long kbId = knowledgeQueryPageReq.getKbId();

        // 构建查询条件
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(knowledgeModel.kbId.eq(kbId));

        // 创建分页请求
        Pageable pageable = PageRequest.of(pageNo - 1, pageSize);

        // 先获取总数
        long total = queryFactory.select(knowledgeModel.id.countDistinct())
                .from(knowledgeModel)
                .where(builder)
                .fetchOne();

        if (total == 0) {
            log.warn("根据知识库ID {} 查询的知识列表为空", kbId);
            return PageResult.empty();
        }

        // 动态查询知识，并按更新时间倒序排序
        List<Tuple> queryResults = queryFactory
                .select(knowledgeModel, userModel.displayName)
                .from(knowledgeModel)
                .leftJoin(userModel).on(knowledgeModel.updaterId.eq(userModel.id))
                .where(builder)
                .orderBy(knowledgeModel.knowledgeName.asc())
//                .orderBy(knowledgeModel.updateTime.desc())
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();

        // 结果转换
        List<KnowledgeResp> knowledgeResps = queryResults.stream().map(tuple -> {
            KnowledgeModel knowledge = tuple.get(knowledgeModel);
            String displayName = tuple.get(userModel.displayName);

            // 转换为 KnowledgeResp
            KnowledgeResp resp = OrikaUtils.convert(knowledge, KnowledgeResp.class);
            resp.setOperator(displayName);

            // 手动触发枚举转换
            if (knowledge.getStatus() != null) {
                resp.setStatusName(knowledge.getStatus().getDescription());
            }

            return resp;
        }).collect(Collectors.toList());

        // 返回分页结果
        return PageResult.of(knowledgeResps, total);
        } catch (IllegalArgumentException e) {
            // 处理参数异常
            log.error("获取知识时参数错误，知识库ID: {}", knowledgeQueryPageReq.getKbId(), e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "获取知识列表失败"
            );
        } catch (DataAccessException e) {
            // 处理数据库异常
            log.error("获取知识时数据库操作失败，知识库ID: {}", knowledgeQueryPageReq.getKbId(), e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "获取知识列表失败"
            );
        } catch (Exception e) {
            // 捕获其他所有异常
            log.error("获取知识时发生未知错误，知识库ID: {}", knowledgeQueryPageReq.getKbId(), e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "获取知识列表失败"
            );
        }
    }



    /**
     * 更新知识
     *
     * @param req 知识更新请求
     */
    @Override
    @Transactional
    public void updateKnowledge(KnowledgeUpdateReq req) {
        List<TagFileRsp> tagFileRsps = new ArrayList<>();

            // 根据ID查找知识
            KnowledgeModel knowledgeModel = knowledgeRepository.findById(req.getId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(
                            GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"
                    ));

            // 根据数据库查找内容设置 ID
            req.setKbId(knowledgeModel.getKbId());
            req.setFileId(knowledgeModel.getFileId());

            // 检查知识名称是否重复
            boolean exists = knowledgeRepository.existsByKbIdAndKnowledgeNameAndIdNot(
                    req.getKbId(), req.getKnowledgeName(), req.getId()
            );
            if (exists) {
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.DUPLICATED_OBJECT,
                        "该知识名称已经存在~请重新命名"
                );
            }

            // 如果知识 ID 和文件 ID 不相同，获取文件的标签信息
            if (!Objects.equals(req.getId(), req.getFileId())) {
                FileResp fileWithTagsById = getFileWithTagsById(req.getFileId());
                tagFileRsps = fileWithTagsById.getTags();
            }else {
                KnowledgeBaseResp knowledgeBaseByIdWithTags = knowledgeBaseService.getKnowledgeBaseByIdWithTags(req.getKbId());
                if (!knowledgeBaseByIdWithTags.getTags().isEmpty()) {
                    tagFileRsps = OrikaUtils.convertList(knowledgeBaseByIdWithTags.getTags(), TagFileRsp.class);
                }
            }

            // 转换类并调用 Python API 更新 QA 对
            KnowledgeNoFileCreateReq knowledgeReq = OrikaUtils.convert(req, KnowledgeNoFileCreateReq.class);
            try {
                asyncPythonApiService.updateFaqsOnPythonApi(knowledgeReq, tagFileRsps);
            } catch (Exception e) {
                log.error("调用 Python API 更新 QA 对失败, 知识ID: {}, 请求体: {}", req.getId(), knowledgeReq, e);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                        "调用算法更新知识失败"
                );
            }
        try {
            // 更新知识数据
            KnowledgeModel updateKnowledge = OrikaUtils.convert(req, KnowledgeModel.class);
            updateKnowledge.setStatus(KnowledgeStatusEnum.PENDING_APPROVAL);
            KnowledgeModel saveModel = knowledgeRepository.save(updateKnowledge);
            ///调用方法 构建 StatusUpdateMessage 并发送到 `knowledge` 主题
            sendStatusMessage(saveModel);

            // 保存记录
            recordService.RecordSave(req.getId(), RecordStatusEnum.MODIFY, null);

        } catch (ServiceException e) {
            log.error("更新知识失败, 知识ID: {}", req.getId(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,"更新知识失败"); // 直接抛出自定义异常
        }
    }

    public FileResp getFileWithTagsById(Long fileId) {
        // 定义实体对象的 Q 类
        QFileModel fileModel = QFileModel.fileModel;
        QFileTagModel fileTagModel = QFileTagModel.fileTagModel;
        QTagModel tagModel = QTagModel.tagModel;

        try {
            // 构建查询条件，仅按文件ID查询
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(fileModel.id.eq(fileId));

            // 执行查询，获取文件和标签信息
            List<Tuple> queryResults = queryFactory.select(fileModel, tagModel)
                    .from(fileModel)
                    .leftJoin(fileTagModel).on(fileModel.id.eq(fileTagModel.fileId))  // 文件和文件标签的关联
                    .leftJoin(tagModel).on(fileTagModel.tagId.eq(tagModel.id))  // 文件标签和标签的关联
                    .where(builder)
                    .fetch();

            // 用于去重的 Map，Key 为文件 ID，Value 为 FileResp（包括标签集合）
            Map<Long, FileResp> fileMap = new HashMap<>();

            // 遍历查询结果并构建 FileResp 对象
            for (Tuple tuple : queryResults) {
                FileModel fileModelResult = tuple.get(fileModel);
                TagModel tagModelResult = tuple.get(tagModel);

                // 获取或创建 FileResp 对象
                FileResp fileResp = fileMap.computeIfAbsent(fileModelResult.getId(), id -> {
                    FileResp newFileResp = OrikaUtils.convert(fileModelResult, FileResp.class);
                    newFileResp.setTags(new ArrayList<>());  // 初始化标签集合
                    return newFileResp;
                });

                // 如果标签不为空，则添加到标签集合中
                if (tagModelResult != null) {
                    TagFileRsp tagFileRsp = new TagFileRsp();
                    tagFileRsp.setId(tagModelResult.getId());
                    tagFileRsp.setTagName(tagModelResult.getTagName());
                    tagFileRsp.setTagType(tagModelResult.getTagType());
                    fileResp.getTags().add(tagFileRsp);
                }
            }

            // 返回文件信息，如果未找到则抛出异常
            if (fileMap.isEmpty()) {
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,
                        "文件不存在，ID：" + fileId
                );
            }
            return fileMap.values().iterator().next();

        } catch (IllegalArgumentException e) {
            log.error("获取文件信息时参数错误，文件ID：{}", fileId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.DATA_NOT_FOUND_ERROR,
                    e,
                    "获取文件信息失败"
            );
        } catch (DataAccessException e) {
            log.error("数据库访问异常，文件ID：{}", fileId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.DATA_NOT_FOUND_ERROR,
                    e,
                    "获取文件信息失败"
            );
        } catch (Exception e) {
            log.error("获取文件信息时发生未知错误，文件ID：{}", fileId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.DATA_NOT_FOUND_ERROR,
                    e,
                    "获取文件信息失败"
            );
        }
    }


    /**
     * 知识提审
     *
     * @param id 知识ID
     */
    @Override
    @Transactional
    public void submitKnowledge(Long id) {
        // 根据ID查找知识
        KnowledgeModel knowledgeModel = knowledgeRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));
        knowledgeModel.setStatus(KnowledgeStatusEnum.APPROVALING);
        KnowledgeModel saveMadel = knowledgeRepository.save(knowledgeModel);
        ///调用方法 构建 StatusUpdateMessage 并发送到 `knowledge` 主题
        sendStatusMessage(saveMadel);
        recordService.RecordSave(knowledgeModel.getId(),RecordStatusEnum.APPROVAL,null);
    }

    /**
     * 知识启用
     * @param id
     */
    @Override
    public void activeKnowledge(Long id) {
        // 根据ID查找知识
        KnowledgeModel knowledgeModel = knowledgeRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));
        if(knowledgeModel.getStatus().equals(KnowledgeStatusEnum.DISABLED)) {
            knowledgeModel.setStatus(KnowledgeStatusEnum.ENABLED);
            KnowledgeModel saveMadel = knowledgeRepository.save(knowledgeModel);
            ///调用方法 构建 StatusUpdateMessage 并发送到 `knowledge` 主题
            sendStatusMessage(saveMadel);
            recordService.RecordSave(knowledgeModel.getId(),RecordStatusEnum.ENABLED,null);
        }
        else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "知识状态错误");
        }
    }

    /**
     * 知识禁用
     * @param id
     */
    @Override
    public void inactiveKnowledge(Long id) {
        // 根据ID查找知识
        KnowledgeModel knowledgeModel = knowledgeRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));
        if(knowledgeModel.getStatus().equals(KnowledgeStatusEnum.ENABLED)) {
            knowledgeModel.setStatus(KnowledgeStatusEnum.DISABLED);
            KnowledgeModel saveMadel = knowledgeRepository.save(knowledgeModel);
            ///调用方法 构建 StatusUpdateMessage 并发送到 `knowledge` 主题
            sendStatusMessage(saveMadel);
            recordService.RecordSave(knowledgeModel.getId(),RecordStatusEnum.DISABLED,null);
        }
        else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "知识状态错误");
        }
    }



    public void sendStatusMessage(KnowledgeModel knowledgeModel) {
        try {

            //获取操作人名称
            String displayName = userRepository.findById(knowledgeModel.getCreatorId()).get().getDisplayName();
            // 发送 WebSocket 通知

            // 构建 StatusUpdateMessage 并发送到 `knowledge` 主题
            KnowledgeStatusMessage knowledgeStatusMessage = new KnowledgeStatusMessage();
            knowledgeStatusMessage.setId(String.valueOf(knowledgeModel.getId()));
            knowledgeStatusMessage.setKnowledgeName(knowledgeModel.getKnowledgeName());
            knowledgeStatusMessage.setStatus(knowledgeModel.getStatus());
            knowledgeStatusMessage.setStatusName(knowledgeModel.getStatus().getDescription());
            knowledgeStatusMessage.setUpdateTime(knowledgeModel.getUpdateTime());
            knowledgeStatusMessage.setOperator(displayName);

            WebSocketServer.broadcastToTopic(knowledgeStatusMessage, "knowledge");

        } catch (JsonProcessingException e) {
            log.error("发送前端 websocket 消息失败，文件ID: {}, 知识ID: {}", knowledgeModel.getId(), e);
            /*throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DOLPHIN_SCHEDULER_RESPONSE_ERROR, "调度引擎-调度websocket失败。");*/
        }
    }


    /**
     * 删除知识（物理删除）
     *
     * @param id 知识ID
     */
    @Override
    @Transactional
    public void deleteKnowledge(Long id) {
        // 查询知识信息
        KnowledgeModel knowledgeModel = knowledgeRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));

        // 调用 API 删除 FAQ
        asyncPythonApiService.deleteFaqsOnPythonApi(knowledgeModel.getKbId(), Collections.singletonList(knowledgeModel.getFileId().toString()));

        // 物理删除知识
        knowledgeRepository.delete(knowledgeModel);
    }


    /**
     * 获取知识详情
     *
     * @param id 知识ID
     * @return 知识响应数据
     */
    @Override
    public KnowledgeResp getKnowledgeById(Long id) {
        KnowledgeModel knowledgeModel = knowledgeRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"知识不存在"));

        // 将 KnowledgeModel 转换为 KnowledgeResp
        return OrikaUtils.convert(knowledgeModel, KnowledgeResp.class);
    }

    /**
     * 获取审批列表
     * @param req 知识分页请求
     * @return 审批分页响应数据
     */
    @Override
    public PageResult<KnowledgePageView> getKnowledgeList(KnowledgePageReq req) {
        try {
            Long currentTenantId = LoginContextHolder.getLoginUserTenantId();
            Pageable pageable = PageRequest.of(req.getPageNo() - 1
                    , req.getPageSize()
                    , Sort.by(Sort.Direction.DESC,
                            SortProperties.CREATE_TIME));
            String content = req.getContent();
            QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
            QUserModel qUserModel=QUserModel.userModel;
            BooleanBuilder builder = new BooleanBuilder();
            if (StrUtil.isNotEmpty(content)) {
                builder.or(qKnowledgeModel.knowledgeName.contains(content))
                        .or(qUserModel.displayName.contains(content));
            }
            builder.and(qKnowledgeModel.creatorId.eq(qUserModel.id));
            builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.APPROVALING));
            builder.and((qKnowledgeModel.tenantId).eq(currentTenantId));
            JPQLQuery<KnowledgePageView> jpqlQuery = queryFactory.select((Projections.bean(
                            KnowledgePageView.class,
                            qKnowledgeModel.id,
                            qKnowledgeModel.knowledgeName,
                            qKnowledgeModel.createTime,
                            qUserModel.displayName
                    )))
                    .from(qKnowledgeModel, qUserModel)
                    .orderBy(qKnowledgeModel.createTime.desc())
                    .offset(pageable.getOffset())
                    .limit(pageable.getPageSize())
                    .where(builder);
            Long total = jpqlQuery.fetchCount();
            List<KnowledgePageView> list = jpqlQuery.fetch();
            return PageResult.of(list, total);
        } catch (IllegalArgumentException e) {
            log.error("查询参数错误",e );
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR,e,"获取问题列表失败");
        } catch (Exception e) {
            log.error("获取问题列表失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR,e,"获取问题列表失败");
        }
    }

    @Override
    @Transactional
    public void updateKnowledgeAndFileStatus(KnowledgeUpdateStatusReq req) {
        log.info("更新知识和文件状态回调数据：{}", req);
        // 从回调数据中获取必要信息
        Long fileId = req.getId();
        KnowledgeStatusEnum knowledgeStatus = req.getStatus();

        // 验证必要字段
        if (fileId == null || knowledgeStatus == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "缺少必要的参数");
        }

        // 查询知识信息
        KnowledgeModel knowledge = knowledgeRepository.findByFileId(fileId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));

        // 更新知识状态
        if (KnowledgeStatusEnum.contains(knowledgeStatus.getValue())) {
            knowledge.setStatus(knowledgeStatus);
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "无效的状态值");
        }

        // 查询对应文件信息
        FileModel file = fileRepository.findById(fileId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "文件不存在"));
        // 检查传入的状态值是否匹配 CREATE_FAILED 枚举值
        if (KnowledgeStatusEnum.CREATE_FAILED.name().equals(req.getStatus().name())) {
            // 匹配成功的逻辑处理
            file.setStatus(FileStatusEnum.PROCESS_FAILED);
        }else {
            file.setStatus(FileStatusEnum.PROCESSED);
        }
        // 保存更新后的知识和文件状态
        //确保回调接口操作人为相关权限人员
        LoginContextHolder.setLoginUserId(knowledge.getUpdaterId());
        LoginContextHolder.setLoginUserTenantId(knowledge.getTenantId());
        knowledge.setUpdaterId(knowledge.getUpdaterId());
        knowledgeRepository.save(knowledge);

        file.setUpdaterId(knowledge.getUpdaterId());
        fileRepository.save(file);
        // 发送 WebSocket 通知
        try {
            // 1. 构建 FileUpdateMessage 并发送到 `file` 主题
            KnowledgeFileUpdateMessage knowledgeFileUpdateMessage = new KnowledgeFileUpdateMessage();
            knowledgeFileUpdateMessage.setId(String.valueOf(file.getId()));
            knowledgeFileUpdateMessage.setStatusName(file.getStatus().getDescription());
            knowledgeFileUpdateMessage.setStatus(file.getStatus().getValue());
            knowledgeFileUpdateMessage.setUpdateTime(file.getUpdateTime());

            WebSocketServer.broadcastToTopic(knowledgeFileUpdateMessage, "file");

            // 2. 构建 KnowledgeUpdateMessage 并发送到 `knowledge` 主题
            KnowledgeUpdateMessage knowledgeUpdateMessage = new KnowledgeUpdateMessage();
            knowledgeUpdateMessage.setId(String.valueOf(knowledge.getId()));
            knowledgeUpdateMessage.setStatusName(knowledge.getStatus().getDescription());
            knowledgeUpdateMessage.setStatus(knowledge.getStatus().getValue());
            knowledgeUpdateMessage.setUpdateTime(knowledge.getUpdateTime());

            WebSocketServer.broadcastToTopic(knowledgeUpdateMessage, "knowledge");

        } catch (JsonProcessingException e) {
            log.error("发送前端 websocket 消息失败，文件ID: {}, 知识ID: {}", file.getId(), knowledge.getId(), e);
           /* throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DOLPHIN_SCHEDULER_RESPONSE_ERROR, "调度引擎-调度websocket失败。");*/
        }
    }
    @Override
    public KnowledgeWithQaResp getKnowledgeWithQa(Long id) {
        QKnowledgeModel knowledgeModel = QKnowledgeModel.knowledgeModel;
        QUserModel userModel = QUserModel.userModel;
        QFileModel fileModel = QFileModel.fileModel;

        // 构建查询条件
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(knowledgeModel.id.eq(id));

        // 查询知识和操作人信息
        Tuple knowledgeTuple = queryFactory
                .select(knowledgeModel, userModel.displayName)
                .from(knowledgeModel)
                .leftJoin(userModel).on(knowledgeModel.updaterId.eq(userModel.id))
                .where(builder)
                .fetchOne();

        if (knowledgeTuple == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在");
        }

        // 提取查询结果
        KnowledgeModel knowledge = knowledgeTuple.get(knowledgeModel);
        String operatorName = knowledgeTuple.get(userModel.displayName);

        // 构建响应对象
        KnowledgeWithQaResp knowledgeResp = OrikaUtils.convert(knowledge, KnowledgeWithQaResp.class);
        knowledgeResp.setOperator(operatorName);

        // 获取对应文件的 PDF 预览路径
        Long fileId = knowledge.getFileId();
        String pdfPreviewUrl = queryFactory
                .select(fileModel.pdfHdfsPath)
                .from(fileModel)
                .where(fileModel.id.eq(fileId))
                .fetchOne();

        if (pdfPreviewUrl != null && !pdfPreviewUrl.isEmpty()) {
            // 设置 PDF 预览路径到响应对象
            knowledgeResp.setPdfPreviewUrl(fileComponent.previewUrlByPath(pdfPreviewUrl));
        } else {
            log.warn("文件 ID {} 的 PDF 路径为空", fileId);
            // 或者设为空字符串，根据业务需求
            knowledgeResp.setPdfPreviewUrl("");
        }
        // 调用服务层获取 QA 对
        List<QaWithCiteResponse> qaList = asyncPythonApiService.getQaWithCite(fileId.toString());
        knowledgeResp.setQaWithCiteResponses(qaList);

        return knowledgeResp;
    }

}