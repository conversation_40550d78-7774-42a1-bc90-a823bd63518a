package com.center.emergency.biz.chat.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "更新对话内容标题所需参数")
public class UpdateTitleReq {

    @Schema(description = "对话SessionID")
    private Long sessionId;

    @NotBlank(message = "新标题不能为空")
    @Schema(description = "新标题")
    private String title;

}
