package com.center.emergency.biz.apikeys.pojo;

import com.center.emergency.common.enumeration.AnswerModeEnum;
import com.center.emergency.common.enumeration.SearchModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ApiKeyValidateDTO {

    @Schema(description = "知识库Id")
    private Long kbId;

    @Schema(description = "机器人Id")
    private Long robotId;

    @Schema(description = "max_token")
    private Integer maxToken;

    @Schema(description = "VECTOR")
    private SearchModeEnum searchMode;

    @Schema(description = "score_threshold")
    private BigDecimal score_threshold;

    @Schema(description = "api_context_length")
    private  Integer apiContextLength;

    @Schema(description = "温度参数")
    private Float temperature;

    @Schema(description = "Top P 参数")
    private Float topP;

    @Schema(description = "top_k 采样参数", example = "20")
    private Integer topK;

    @Schema(description = "answer_mode")
    private AnswerModeEnum answerMode;

    @Schema(description = "query_decompose")
    private  Integer queryDecompose;

    @Schema(description = "model")
    private  String model;

    @Schema(description = "api_base")
    private  String apiBase;

    @Schema(description = "apiKey")
    private  String api_key;

}
