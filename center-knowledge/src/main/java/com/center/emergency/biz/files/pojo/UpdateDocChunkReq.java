package com.center.emergency.biz.files.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class UpdateDocChunkReq {

    @NotEmpty(message = "文件的ChunkID不能为空！")
    @Schema(description = "文件ChunkID")
    private String chunkId;


    @NotEmpty(message = "文件的Chunk内容不能为空")
    @Schema(description = "新的Chunk内容")
    private String content;

    @Schema(description = "Header内容")
    private String header;
}
