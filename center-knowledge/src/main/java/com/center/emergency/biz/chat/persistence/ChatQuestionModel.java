package com.center.emergency.biz.chat.persistence;

import com.center.framework.common.enumerate.EventTypeEnum;
import com.center.framework.db.core.BaseModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;


@Data
@Entity
@QueryEntity
@Table(name = "center_chat_question")
public class ChatQuestionModel extends BaseModel {

    @Schema(description = "问题内容")
    @Column(name = "content",nullable = false)
    private String content;

    @Schema(description = "会话ID")
    @Column(name = "session_id",nullable = false)
    private Long sessionId;

    @Schema(description = "消息类型")
    @Column(name = "event_type",nullable = false)
    @Enumerated(value = EnumType.STRING)
    private EventTypeEnum eventType;

}
