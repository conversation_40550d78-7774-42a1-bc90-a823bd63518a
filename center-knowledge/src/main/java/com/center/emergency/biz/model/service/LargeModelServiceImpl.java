package com.center.emergency.biz.model.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.model.persistence.LargeModel;
import com.center.emergency.biz.model.persistence.LargeModelRepository;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.model.pojo.*;
import com.center.emergency.biz.model.service.LargeModelService;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.robot.persitence.RobotModel;
import com.center.emergency.biz.robot.persitence.RobotRepository;
import com.center.emergency.biz.robot.pojo.RobotDetailsResp;
import com.center.emergency.biz.robot.pojo.RobotModelResp;
import com.center.emergency.common.enumeration.ModelProviderEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.emergency.common.enumeration.TagTypeEnum;
import com.center.emergency.common.utils.OkHttpUtils;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import com.center.infrastructure.system.biz.role.persistence.RoleModel;
import com.center.infrastructure.system.biz.role.persistence.RoleRepository;
import com.center.infrastructure.system.biz.user.persistence.UserRoleModel;
import com.center.infrastructure.system.biz.user.persistence.UserRoleRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.*;
@Slf4j
@Service
public class LargeModelServiceImpl implements LargeModelService {

    @Resource
    private LargeModelRepository largeModelRepository;

    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private RobotRepository robotRepository;

    @Resource
    Snowflake snowflake;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LargeModelResp createLargeModel(LargeModelCreateReq req) {
        if (StringUtils.isBlank(req.getBaseUrl())) {
            switch (req.getProvider()) {
                case TONGYI:
                    req.setBaseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1/"); break;
                case DEEPSEEK:
                    req.setBaseUrl("https://api.deepseek.com/v1/"); break;
                case OPENAI:
                    req.setBaseUrl("https://api.openai.com/v1/"); break;
                default:
                    throw ServiceExceptionUtil.exception(REQUEST_PARAM_ERROR, "不支持的模型服务商，无法自动设置 BaseUrl");
            }
        }
        getAvailableModels(req);

        //解决参数为空值时的复制问题
        if ("".equals(req.getTemperature())) {
            req.setTemperature(null);
        }if ("".equals(req.getTopP())){
            req.setTopP(null);
        }if ("".equals(req.getMaxTokens())){
            req.setMaxTokens(null);
        }if ("".equals(req.getInputContextLength())){
            req.setInputContextLength(null);
        }
        LargeModel model = OrikaUtils.convert(req, LargeModel.class);
        model.setId(snowflake.nextId());
        model.setStatus(CommonStatusEnum.ACTIVE);
        model.setApiKey(req.getApiKey());
        model.setSourceType(SourceTypeEnum.CUSTOM);
        LargeModel largeModel = largeModelRepository.save(model);

        return OrikaUtils.convert(largeModel, LargeModelResp.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LargeModelResp updateLargeModel(LargeModelUpdateReq req) {
        LargeModel model = largeModelRepository.findById(req.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_FOUND, "模型不存在"));

        //增加对系统内置模型更新的限制
        if (SourceTypeEnum.SYSTEM.equals(model.getSourceType())){
            if (!isSuperAdmin(LoginContextHolder.getLoginUserId())){
               throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "系统内置模型不允许修改");
            }
        }

        if (StringUtils.isBlank(req.getBaseUrl())) {
            switch (req.getProvider()) {
                case TONGYI:
                    req.setBaseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1"); break;
                case DEEPSEEK:
                    req.setBaseUrl("https://api.deepseek.com/v1"); break;
                case OPENAI:
                    req.setBaseUrl("https://api.openai.com/v1"); break;
                default:
                    throw ServiceExceptionUtil.exception(REQUEST_PARAM_ERROR, "不支持的模型服务商，无法自动设置 BaseUrl");
            }
        }
        // 转换为 create 请求体以测试模型
        LargeModelCreateReq testReq = OrikaUtils.convert(req, LargeModelCreateReq.class);
        getAvailableModels(testReq);

        //解决参数为空值时的复制问题
        if ("".equals(req.getTemperature())) {
            req.setTemperature(null);
        }if ("".equals(req.getTopP())){
            req.setTopP(null);
        }if ("".equals(req.getMaxTokens())){
            req.setMaxTokens(null);
        }if ("".equals(req.getInputContextLength())){
            req.setInputContextLength(null);
        }

        OrikaUtils.copy(req, model);
        model.setApiKey(req.getApiKey());
        model.setStatus(CommonStatusEnum.ACTIVE);
        return OrikaUtils.convert(largeModelRepository.save(model), LargeModelResp.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLargeModel(Long id) {
        //增加对系统内置模型删除的限制
        LargeModel largeModel = largeModelRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_FOUND, "模型不存在"));
        if (SourceTypeEnum.SYSTEM.equals(largeModel.getSourceType())){
            if (!isSuperAdmin(LoginContextHolder.getLoginUserId())){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "系统内置模型不允许删除");
            }
        }

        // 检查模型使用情况（查询模型组ID）
        long usageCount = robotRepository.countByModelGroupId(id);
        if (usageCount > 0) {
            throw ServiceExceptionUtil.exception(REQUEST_PARAM_ERROR, "该模型已被 " + usageCount + " 个机器人引用，无法删除");
        }

        largeModelRepository.deleteById(id);
    }

    @Override
    public PageResult<PageLargeModelWithRobotsResp> pageLargeModels(LargeModelPageReq req) {
        QLargeModel q = QLargeModel.largeModel;
        QRobotModel qr = QRobotModel.robotModel;

        BooleanBuilder builder = new BooleanBuilder();
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        // 查询条件：租户模型 或 系统模型
        builder.and(q.tenantId.eq(tenantId).or(q.sourceType.eq(SourceTypeEnum.SYSTEM)));

        // 新增：关键词模糊匹配 modelName 或 modelDesc
        if (StringUtils.isNotBlank(req.getKeyword())) {
            builder.and(q.modelDisplayName.containsIgnoreCase(req.getKeyword())
                    .or(q.modelDesc.containsIgnoreCase(req.getKeyword())));
        }
        List<LargeModel> models = queryFactory.selectFrom(q)
                .where(builder)
                .offset((long) (req.getPageNo() - 1) * req.getPageSize())
                .limit(req.getPageSize())
                .orderBy(
                        new CaseBuilder()
                                .when(q.sourceType.eq(SourceTypeEnum.SYSTEM)).then(1)
                                .otherwise(2).asc(),
                        q.createTime.desc()
                )
                .fetch();

        long total = queryFactory.select(q.count()).from(q).where(builder).fetchOne();

        List<Long> modelIds = models.stream().map(LargeModel::getId).collect(Collectors.toList());
        // 查询使用这些模型的机器人（按模型组ID查询）
        List<RobotModel> robots = robotRepository.findByModelGroupIdIn(modelIds);

        Map<Long, List<RobotModel>> robotMap = robots.stream()
                .collect(Collectors.groupingBy(RobotModel::getModelGroupId));

        List<PageLargeModelWithRobotsResp> result = models.stream().map(model -> {
            PageLargeModelWithRobotsResp resp = OrikaUtils.convert(model, PageLargeModelWithRobotsResp.class);
            List<RobotModelResp> robotDetails = Optional.ofNullable(robotMap.get(model.getId()))
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(r -> OrikaUtils.convert(r, RobotModelResp.class))
                    .collect(Collectors.toList());
            resp.setRobots(robotDetails);
            return resp;
        }).collect(Collectors.toList());

        return PageResult.of(result, total);
    }


    @Override
    public List<SelectListLargeModelResp> listAllModelsForSelect() {
        QLargeModel q = QLargeModel.largeModel;
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        
        // 查询条件：租户模型 或 系统模型
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(q.tenantId.eq(tenantId).or(q.sourceType.eq(SourceTypeEnum.SYSTEM)));
        
        List<LargeModel> models = queryFactory.selectFrom(q)
                .where(builder)
                .orderBy(
                        new CaseBuilder()
                                .when(q.sourceType.eq(SourceTypeEnum.SYSTEM)).then(1)
                                .otherwise(2).asc(),
                        q.createTime.desc()
                )
                .fetch();
        
        return OrikaUtils.convertList(models, SelectListLargeModelResp.class);
    }

    private Map<String, Object> buildBasicChatBody(LargeModelCreateReq req) {
        Map<String, Object> body = new HashMap<>();
        body.put("model", req.getModelName());

        List<Map<String, String>> messages = new ArrayList<>();
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", "你好");
        messages.add(message);
        body.put("messages", messages);

        // ✅ String -> Float 解析，并保留默认值
        try {
            body.put("temperature", StringUtils.isNotBlank(req.getTemperature())
                    ? Float.parseFloat(req.getTemperature())
                    : 0.7f);
        } catch (NumberFormatException e) {
            throw ServiceExceptionUtil.exception(REQUEST_PARAM_ERROR, "温度参数格式错误，请输入0到1之间的小数");
        }

        try {
            body.put("top_p", StringUtils.isNotBlank(req.getTopP())
                    ? Float.parseFloat(req.getTopP())
                    : 1.0f);
        } catch (NumberFormatException e) {
            throw ServiceExceptionUtil.exception(REQUEST_PARAM_ERROR, "Top P参数格式错误，请输入的参数大于0且小于等于1");
        }

        try {
            body.put("max_tokens", StringUtils.isNotBlank(req.getMaxTokens())
                    ? Integer.parseInt(req.getMaxTokens())
                    : 4096);
        } catch (NumberFormatException e) {
            throw ServiceExceptionUtil.exception(REQUEST_PARAM_ERROR, "max_tokens 参数格式错误，请输入大于等于512的整数");
        }

        body.put("stream", false);
        return body;
    }


    private String doOpenAICompatibleChatTest(String url, String apiKey, Map<String, Object> body, Map<String, String> extraHeaders) {

        try {
            OkHttpUtils builder = OkHttpUtils.builder()
                    .url(url)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("Content-Type", "application/json");

            if (extraHeaders != null) {
                extraHeaders.forEach(builder::addHeader);
            }

            String result = builder.jsonPost(body).sync();

            if (result != null && result.contains("choices")) {
                log.info("兼容模型返回：" + result);
                return "模型测试通过";
            } else {
                throw new RuntimeException("模型响应异常: " + result);
            }
        } catch (Exception e) {
            // 不在这里包 ServiceException，让上层统一处理
            throw new RuntimeException("模型请求异常: " + extractRootMessage(e), e);
        }
    }
    private String extractRootMessage(Throwable e) {
        while (e.getCause() != null) {
            e = e.getCause();
        }
        return e.getMessage() != null ? e.getMessage() : "未知错误";
    }
    @Override
    public LargeModelUsageResp getModelUsageInfo(Long modelId) {
        // 查询使用该模型的机器人（按模型组ID查询）
        List<RobotModel> robots = robotRepository.findByModelGroupId(modelId);
        List<RobotModelResp> details = OrikaUtils.convertList(robots, RobotModelResp.class);
        LargeModelUsageResp resp = new LargeModelUsageResp();
        resp.setModelId(modelId);
        resp.setUsageCount((long) details.size());
        resp.setRobots(details);
        return resp;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LargeModelWithRobotsResp toggleModelStatus(Long modelId) {
        LargeModel model = largeModelRepository.findById(modelId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(NOT_FOUND, "模型不存在"));

        //判断是否是内置模型
        if (SourceTypeEnum.SYSTEM.equals(model.getSourceType())){
            throw ServiceExceptionUtil.exception(FORBIDDEN, "内置模型不可修改状态");
        }

        // 切换状态
        CommonStatusEnum newStatus = model.getStatus() == CommonStatusEnum.ACTIVE
                ? CommonStatusEnum.INACTIVE
                : CommonStatusEnum.ACTIVE;

        model.setStatus(newStatus);
        largeModelRepository.save(model);

        // 查询引用该模型的机器人（按模型组ID查询）
        List<RobotModel> robots = robotRepository.findByModelGroupId(modelId);
        List<RobotModelResp> robotDetails = OrikaUtils.convertList(robots, RobotModelResp.class);

        // 组装返回
        LargeModelWithRobotsResp resp = OrikaUtils.convert(model, LargeModelWithRobotsResp.class);
        resp.setRobots(robotDetails);
        return resp;
    }
    @Override
    public LargeModelWithRobotsResp getModelDetail(Long modelId) {
        LargeModel model = largeModelRepository.findById(modelId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(NOT_FOUND, "模型不存在"));

        // 查询使用该模型的机器人（按模型组ID查询）
        List<RobotModel> robots = robotRepository.findByModelGroupId(modelId);
        List<RobotModelResp> robotDetails = OrikaUtils.convertList(robots, RobotModelResp.class);

        //如果是非超管查看系统内置模型的话，加密！
        if (SourceTypeEnum.SYSTEM.equals(model.getSourceType())){
            model.setApiKey("******");
            model.setBaseUrl("******");
            model.setModelName("******");
        }

        LargeModelWithRobotsResp resp = OrikaUtils.convert(model, LargeModelWithRobotsResp.class);
        resp.setRobots(robotDetails);
        return resp;
    }

    @Override
    public List<AvailableModelResp> getAvailableModels(LargeModelCreateReq req) {
        String baseUrl = req.getBaseUrl().replaceAll("/+$", "");
        String listUrl = baseUrl + "/models";
        String result;

        // 1️⃣ 请求阶段：先尝试发请求，专门处理网络相关问题
        try {
            result = OkHttpUtils.builder()
                    .url(listUrl)
                    .addHeader("Authorization", "Bearer " + req.getApiKey())
                    .addHeader("Content-Type", "application/json")
                    .get()
                    .sync();
        } catch (IOException e) {
            log.error("网络连接异常：{}", extractRootMessage(e));
            throw ServiceExceptionUtil.exception(
                    OUTER_SERVER_EXCEPTION,
                    "请检查网络连接或API Base地址是否正确"
            );
        }

        // 2️⃣ 响应阶段：先检查是否是 JSON 格式
        if (!result.trim().startsWith("{")) {
            throw ServiceExceptionUtil.exception(
                    OUTER_SERVER_EXCEPTION,
                    "请检查API Key是否正确，或是否已过期"
            );
        }

        JSONObject json;
        try {
            json = JSONObject.parseObject(result);
        } catch (Exception e) {
            log.error("解析响应失败：{}", result, e);
            throw ServiceExceptionUtil.exception(
                    OUTER_SERVER_EXCEPTION,
                    "响应格式错误，可能服务返回了非 JSON 内容，请检查模型服务是否符合接口规范"
            );
        }

        // 3️⃣ 判断是否为 error 响应
        if (json.containsKey("error")) {
            JSONObject error = json.getJSONObject("error");
            String message = error.getString("message");
            String code = error.getString("code");

            if (containsApiKeyError(message, code)) {
                throw ServiceExceptionUtil.exception(
                        OUTER_SERVER_EXCEPTION,
                        "API Key 无效，请检查是否正确或是否拥有权限"
                );
            } else {
                throw ServiceExceptionUtil.exception(
                        OUTER_SERVER_EXCEPTION,
                        "请检查配置是否正确"
                );
            }
        }

        // 4️⃣ 成功响应，但模型列表为空
        JSONArray data = json.getJSONArray("data");
        if (data == null || data.isEmpty()) {
            throw ServiceExceptionUtil.exception(
                    OUTER_SERVER_EXCEPTION,
                    "未获取到可用模型，请检查配置是否正确"
            );
        }

        return data.toJavaList(AvailableModelResp.class);
    }





    private boolean containsApiKeyError(String message, String code) {
        String m = message == null ? "" : message.toLowerCase();
        String c = code == null ? "" : code.toLowerCase();

        return m.contains("incorrect api key")
                || m.contains("authentication fails")
                || (m.contains("api key") && m.contains("invalid"))
                || c.contains("invalid_api_key")
                || c.contains("authentication_error");
    }

    public Boolean isSuperAdmin(Long userId) {
        UserRoleModel userRoleModel = userRoleRepository.findByUserId(userId);
        RoleModel roleModel = roleRepository.findById(userRoleModel.getRoleId()).orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "角色不存在"));
        return RoleEnum.SUPERADMIN.equals(roleModel.getCode());
    }

}
