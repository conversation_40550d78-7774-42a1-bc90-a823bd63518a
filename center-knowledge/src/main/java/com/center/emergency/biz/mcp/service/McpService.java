package com.center.emergency.biz.mcp.service;

import com.center.emergency.biz.mcp.pojo.McpCreateReq;
import com.center.emergency.biz.mcp.pojo.McpPageReq;
import com.center.emergency.biz.mcp.pojo.McpResp;
import com.center.emergency.biz.mcp.pojo.McpUpdateReq;
import com.center.framework.web.pojo.PageResult;

import java.util.List;

/**
 * MCP（Microservice Control Platform）服务接入与管理平台接口：定义MCP的业务逻辑
 */
public interface McpService {

    Long createMcp(McpCreateReq req);

    void deleteMcp(Long id);

    void updateMcp(McpUpdateReq req);

    McpResp getMcp(Long id);

    List<McpResp> listMcp(String keyword);

    PageResult<McpResp> pageMcp(McpPageReq req);
}