package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.robot.pojo.*;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.pojo.DepartAndKbListResp;
import java.util.List;


public interface RobotService {

    /**
     * 创建智能助手
     * @param req 创建智能助手所需所有的信息
     */
    Long createRobots(AssistantCreateReq req);

    /**
     * 分页查询所有机器人方法
     * @param pageReq 机器人分页查询所需信息
     * @return RobotPageResp的分页展示结果
     */
    PageResult<RobotPageResp> getRobotsByPage(RobotPageReq pageReq);


    /**
     * 切换机器人状态，启用<-->停用
     * @param statusReq，包含需要切换到的机器人状态
     */
    void switchStatus(RobotStatusReq statusReq);

    /**
     * 保存更新后的机器人信息
     * @param req 机器人基本信息，包含机器人id、机器人关联的知识库Id列表等
     * @return 机器人id
     */
    void saveRobot(RobotSaveReq req);


    List<DepartAndKbListResp> listDepartAndKb(Long departId, String path);

    /**
     * 根据机器人ID获取机器人详细信息
     *
     * @param id 机器人的唯一标识符
     * @return 返回机器人的详细信息对象
     * @Description:用于在机器人列表查询机器人详情，返回的知识库信息中包括了相关部门信息
     */
    RobotDetailsResp getRobotDetails(Long id);

    /**
     * 更新智能助手相关信息
     *
     * @param req 更新智能助手所需所有的信息
     */
    void updateRobot(AssistantUpdateReq req);

    /**
     * 根据机器人ID获取其关联的MCP信息列表。
     *
     * @param id 智能助手（机器人）的唯一标识
     * @return MCP 信息列表，每个包含 MCP ID、名称和 logo 预览地址（如有）
     */
    List<MCPResp> getMCPInfoByRobotId(Long id);
}
