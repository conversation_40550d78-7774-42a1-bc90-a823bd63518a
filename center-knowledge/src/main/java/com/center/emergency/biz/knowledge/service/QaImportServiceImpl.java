package com.center.emergency.biz.knowledge.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.approval.service.RecordService;
import com.center.emergency.biz.knowledge.pojo.QaConfirmImportReq;
import com.center.emergency.biz.knowledge.pojo.*;
import com.center.emergency.biz.knowledge.pojo.QaPreviewResp;
import com.center.emergency.biz.knowledge.converter.QaImportResponseConverter;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.emergency.biz.knowledgebase.pojo.KnowledgeBaseResp;
import com.center.emergency.biz.knowledgebase.service.KnowledgeBaseService;
import com.center.emergency.biz.knowledge.persistence.KnowledgeModel;
import com.center.emergency.biz.knowledge.persistence.KnowledgeRepository;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.emergency.common.enumeration.RecordStatusEnum;
import org.springframework.transaction.annotation.Transactional;
import com.center.emergency.biz.tag.pojo.TagFileRsp;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.knowledge.KnowledgeApiTool;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.db.config.SnowFlakeConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * QA导入服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class QaImportServiceImpl implements QaImportService {
    
    @Value("${qa.updateUrl}")
    private String qaUpdateUrl;

    @Value("${qa.checkDuplicatesUrl}")
    private String qaCheckDuplicatesUrl;
    
    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;
    
    @Resource
    private KnowledgeBaseService knowledgeBaseService;
    
    @Resource
    private SnowFlakeConfig snowFlakeConfig;

    @Resource
    private KnowledgeRepository knowledgeRepository;

    @Resource
    private RecordService recordService;
    
    private static final int MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB



    @Override
    public QaPreviewResp previewFromExcel(MultipartFile file, Long kbId) {
        try {
            // 1. 文件校验
            validateFile(file);

            // 2. 获取知识库信息
            KnowledgeBaseModel knowledgeBase = knowledgeBaseRepository.findById(kbId)
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

            // 3. 解析Excel文件
            List<QaImportData> importDataList = parseExcelFile(file);

            if (importDataList.isEmpty()) {
                QaPreviewResp errorResult = buildOptimizedPreviewErrorResult("Excel文件中没有有效数据");
                errorResult.setKbId(knowledgeBase.getId());
                errorResult.setKbName(knowledgeBase.getKbName());
                return errorResult;
            }

            // 4. 基本数据校验（只做空值和长度检查）
            List<QAPair> qaPairs = new ArrayList<>();
            for (QaImportData data : importDataList) {
                // 基本校验：空值检查
                if (StrUtil.isEmpty(data.getQuestion()) || StrUtil.isEmpty(data.getAnswer())) {
                    continue; // 跳过空值，让算法处理
                }

                QAPair qaPair = new QAPair();
                qaPair.setQuestion(data.getQuestion().trim());
                qaPair.setAnswer(data.getAnswer().trim());
                qaPairs.add(qaPair);
            }

            if (qaPairs.isEmpty()) {
                QaPreviewResp errorResult = buildOptimizedPreviewErrorResult("没有有效的QA对数据");
                errorResult.setKbId(knowledgeBase.getId());
                errorResult.setKbName(knowledgeBase.getKbName());
                return errorResult;
            }

            // 5. 构建预检查请求
            Map<String, Object> requestBody = buildCheckDuplicatesRequest(knowledgeBase, qaPairs);

            // 6. 调用算法预检查接口
            JSONObject response = KnowledgeApiTool.post(qaCheckDuplicatesUrl, requestBody);

            // 7. 使用优化的转换器解析响应
            QaPreviewResp result = QaImportResponseConverter.convertToPreviewResp(response);

            // 8. 设置知识库信息
            result.setKbId(knowledgeBase.getId());
            result.setKbName(knowledgeBase.getKbName());

            return result;

        } catch (Exception e) {
            log.error("QA预检查失败: {}", e.getMessage(), e);
            return buildOptimizedPreviewErrorResult(e.getMessage());
        }
    }



    @Override
    @Transactional
    public QaImportResultResp confirmImport(QaConfirmImportReq req) {
        try {
            // 1. 获取知识库信息
            KnowledgeBaseModel knowledgeBase = knowledgeBaseRepository.findById(req.getKbId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));

            // 2. 生成唯一ID（知识ID和文件ID使用同一个，参考createKnowledgeNoFile逻辑）
            Long id = snowFlakeConfig.snowFlakeCore().nextId();

            // 3. 确定知识名称
            String knowledgeName = StrUtil.isNotEmpty(req.getQaName()) ? req.getQaName() : "QA导入_" + System.currentTimeMillis();

            // 4. 检查知识名称是否重复
            boolean exists = knowledgeRepository.existsByKbIdAndKnowledgeName(req.getKbId(), knowledgeName);
            if (exists) {
                knowledgeName = knowledgeName + "_" + System.currentTimeMillis();
            }

            // 5. 创建知识对象并保存到数据库（参考createKnowledgeNoFile的逻辑）
            KnowledgeModel knowledge = new KnowledgeModel();
            knowledge.setId(id);
            knowledge.setKnowledgeName(knowledgeName);
            knowledge.setKbId(req.getKbId());
            knowledge.setFileId(id);  // 文件ID与知识ID相同
            knowledge.setStatus(KnowledgeStatusEnum.PENDING_APPROVAL);
            knowledgeRepository.save(knowledge);

            // 6. 构建导入请求
            QaImportReq importReq = new QaImportReq();
            importReq.setKbId(req.getKbId());
            importReq.setFileId(id);  // 使用同一个ID
            importReq.setFaqs(req.getFaqs());

            // 7. 获取标签信息
            List<TagFileRsp> tags = getKnowledgeBaseTags(req.getKbId());

            // 8. 构建算法接口请求
            Map<String, Object> requestBody = buildAlgorithmRequest(knowledgeBase, importReq, tags);

            // 9. 调用算法接口
            JSONObject response = KnowledgeApiTool.post(qaUpdateUrl, requestBody);

            // 10. 使用优化的转换器解析响应
            QaImportResultResp result = QaImportResponseConverter.convertToImportResultResp(response);

            // 11. 设置知识库信息
            result.setKbId(knowledgeBase.getId());
            result.setKbName(knowledgeBase.getKbName());

            // 12. 如果算法调用成功，记录操作日志并设置知识信息
            if (result.getSuccess()) {
                recordService.RecordSave(id, RecordStatusEnum.CREATE, null);
                result.setKnowledgeId(id);
                result.setKnowledgeName(knowledgeName);
                log.info("QA导入成功，知识ID: {}, 知识名称: {}", id, knowledgeName);
            }

            return result;

        } catch (Exception e) {
            log.error("QA确认导入失败: {}", e.getMessage(), e);
            return buildOptimizedImportErrorResult(e.getMessage());
        }
    }

    @Override
    public QaImportResultResp importFromExcel(MultipartFile file, Long kbId) {
        try {
            // 1. 文件校验
            validateFile(file);
            
            // 2. 获取知识库信息
            KnowledgeBaseModel knowledgeBase = knowledgeBaseRepository.findById(kbId)
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在"));
            
            // 3. 解析Excel文件
            List<QaImportData> importDataList = parseExcelFile(file);
            
            if (importDataList.isEmpty()) {
                QaImportResultResp result = new QaImportResultResp();
                result.setSuccess(false);
                result.setMessage("Excel文件中没有有效数据");
                result.setKbId(knowledgeBase.getId());
                result.setKbName(knowledgeBase.getKbName());
                return result;
            }
            
            // 4. 基本数据校验（只做空值和长度检查，其他交给算法处理）
            List<QAPair> qaPairs = new ArrayList<>();
            for (QaImportData data : importDataList) {
                // 基本校验：空值检查
                if (StrUtil.isEmpty(data.getQuestion()) || StrUtil.isEmpty(data.getAnswer())) {
                    continue; // 跳过空值，让算法处理
                }
                
                QAPair qaPair = new QAPair();
                qaPair.setQuestion(data.getQuestion().trim());
                qaPair.setAnswer(data.getAnswer().trim());
                qaPairs.add(qaPair);
            }
            
            if (qaPairs.isEmpty()) {
                QaImportResultResp result = new QaImportResultResp();
                result.setSuccess(false);
                result.setMessage("没有有效的QA对数据");
                result.setKbId(knowledgeBase.getId());
                result.setKbName(knowledgeBase.getKbName());
                return result;
            }
            
            // 5. 构建导入请求
            QaImportReq importReq = new QaImportReq();
            importReq.setKbId(kbId);
            importReq.setFileId(snowFlakeConfig.snowFlakeCore().nextId());
            importReq.setFaqs(qaPairs);
            
            // 6. 获取标签信息
            List<TagFileRsp> tags = getKnowledgeBaseTags(kbId);
            
            // 7. 构建算法接口请求
            Map<String, Object> requestBody = buildAlgorithmRequest(knowledgeBase, importReq, tags);
            
            // 8. 调用算法接口
            JSONObject response = KnowledgeApiTool.post(qaUpdateUrl, requestBody);

            // 9. 使用优化的转换器解析响应
            QaImportResultResp result = QaImportResponseConverter.convertToImportResultResp(response);

            // 10. 设置知识库信息
            result.setKbId(knowledgeBase.getId());
            result.setKbName(knowledgeBase.getKbName());

            return result;
            
        } catch (Exception e) {
            log.error("QA导入失败: {}", e.getMessage(), e);
            return buildOptimizedImportErrorResult(e.getMessage());
        }
    }
    
    @Override
    public byte[] downloadTemplate() {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("QA导入模板");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("问题");
            headerRow.createCell(1).setCellValue("答案");
            
            // 创建示例行
            Row exampleRow = sheet.createRow(1);
            exampleRow.createCell(0).setCellValue("什么是人工智能？");
            exampleRow.createCell(1).setCellValue("人工智能是计算机科学的一个分支...");
            
            // 设置列宽
            sheet.setColumnWidth(0, 8000);
            sheet.setColumnWidth(1, 15000);
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            log.error("生成模板文件失败: {}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "生成模板文件失败");
        }
    }
    
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "文件不能为空");
        }
        
        if (file.getSize() > MAX_FILE_SIZE) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "文件大小不能超过10MB");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || (!filename.endsWith(".xlsx") && !filename.endsWith(".xls"))) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "文件格式不正确，请上传Excel文件");
        }
    }
    
    private List<QaImportData> parseExcelFile(MultipartFile file) {
        List<QaImportData> dataList = new ArrayList<>();

        try {
            EasyExcel.read(file.getInputStream(), QaImportData.class, new AnalysisEventListener<QaImportData>() {
                @Override
                public void invoke(QaImportData data, AnalysisContext context) {
                    // 跳过标题行（第0行）
                    if (context.readRowHolder().getRowIndex() == 0) {
                        return;
                    }

                    data.setRowIndex(context.readRowHolder().getRowIndex() + 1);

                    // 过滤空行
                    if (StrUtil.isNotEmpty(data.getQuestion()) || StrUtil.isNotEmpty(data.getAnswer())) {
                        dataList.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel解析完成，共{}条数据", dataList.size());
                }
            }).sheet().headRowNumber(1).doRead(); // 设置标题行数为1

        } catch (IOException e) {
            log.error("Excel文件解析失败: {}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "Excel文件解析失败");
        } catch (Exception e) {
            log.error("Excel文件解析异常: {}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "Excel文件格式错误或内容异常");
        }

        return dataList;
    }
    
    private List<TagFileRsp> getKnowledgeBaseTags(Long kbId) {
        try {
            KnowledgeBaseResp knowledgeBase = knowledgeBaseService.getKnowledgeBaseByIdWithTags(kbId);
            return knowledgeBase != null && knowledgeBase.getTags() != null ? 
                OrikaUtils.convertList(knowledgeBase.getTags(), TagFileRsp.class) :
                new ArrayList<>();
        } catch (Exception e) {
            log.warn("获取知识库标签失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
    
    private Map<String, Object> buildAlgorithmRequest(KnowledgeBaseModel knowledgeBase, QaImportReq importReq, List<TagFileRsp> tags) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", knowledgeBase.getAiFaqbId());
        requestBody.put("file_id", importReq.getFileId().toString());
        
        // 转换QA对
        List<Map<String, String>> faqs = importReq.getFaqs().stream()
                .map(qa -> {
                    Map<String, String> faqMap = new HashMap<>();
                    faqMap.put("question", qa.getQuestion());
                    faqMap.put("answer", qa.getAnswer());
                    return faqMap;
                })
                .collect(Collectors.toList());
        requestBody.put("faqs", faqs);
        
        // 转换标签
        List<Map<String, String>> tagList = tags.stream()
                .map(tag -> Collections.singletonMap(tag.getId().toString(), tag.getTagName()))
                .collect(Collectors.toList());
        requestBody.put("tags", tagList);
        
        return requestBody;
    }
    


    /**
     * 构建预检查请求
     */
    private Map<String, Object> buildCheckDuplicatesRequest(KnowledgeBaseModel knowledgeBase, List<QAPair> qaPairs) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");
        requestBody.put("kb_id", knowledgeBase.getAiFaqbId());

        // 转换QA对
        List<Map<String, String>> faqs = qaPairs.stream()
                .map(qa -> {
                    Map<String, String> faqMap = new HashMap<>();
                    faqMap.put("question", qa.getQuestion());
                    faqMap.put("answer", qa.getAnswer());
                    return faqMap;
                })
                .collect(Collectors.toList());
        requestBody.put("faqs", faqs);

        return requestBody;
    }



    /**
     * 构建预检查错误结果
     */
    private QaPreviewResp buildOptimizedPreviewErrorResult(String errorMessage) {
        QaPreviewResp result = new QaPreviewResp();
        result.setSuccess(false);
        result.setMessage("预检查失败: " + errorMessage);
        result.setCanImport(false);
        return result;
    }

    /**
     * 构建导入错误结果
     */
    private QaImportResultResp buildOptimizedImportErrorResult(String errorMessage) {
        QaImportResultResp result = new QaImportResultResp();
        result.setSuccess(false);
        result.setMessage("导入失败: " + errorMessage);
        result.setStatus("FAILED");
        return result;
    }

    @Data
    public static class QaImportData {
        private Integer rowIndex;

        @ExcelProperty(index = 0)
        private String question;

        @ExcelProperty(index = 1)
        private String answer;
    }
}