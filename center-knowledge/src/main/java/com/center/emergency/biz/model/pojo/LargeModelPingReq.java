package com.center.emergency.biz.model.pojo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class LargeModelPingReq {

    @Schema(description = "基础URL")
    @NotBlank(message = "Api Base不能为空")
    @Pattern(
            regexp = "^(http|https)://.+$",
            message = "Api Base不符合规范，请以http或https开头"
    )
    private String baseUrl;


    @Schema(description = "API密钥")
    @NotBlank(message = "API密钥不能为空")
    private String apiKey;

} 