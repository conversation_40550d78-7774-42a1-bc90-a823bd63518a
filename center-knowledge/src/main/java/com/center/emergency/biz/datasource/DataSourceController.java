package com.center.emergency.biz.datasource;

import com.center.emergency.biz.datasource.pojo.*;
import com.center.emergency.biz.datasource.service.DataSourceService;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@Tag(name = "数据库模块")
@RequestMapping("/datasource")
@Validated
@Slf4j
public class DataSourceController {

    @Resource
    private DataSourceService dataSourceService;


    @GetMapping("/select")
    @Operation(summary = "根据关键字查询(分页查询数据)")
    public CommonResult<PageResult<DataSourceSelectResp>> selectDataSource(DataSourcePageReq pageReq){
        return CommonResult.success(dataSourceService.selectDataSource(pageReq));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除数据库")
    public CommonResult<String> deleteDataSource(@RequestParam Long id){
        dataSourceService.deleteDataSource(id);
        return CommonResult.success();
    }

    @GetMapping("/get_single_data_source")
    @Operation(summary = "根据ID获取单数据库详情")
    public CommonResult<SingleDataSourceResp> getSingleDataSourceById(@RequestParam Long id){
        return CommonResult.success(dataSourceService.getSingleDataSourceById(id));
    }

    @GetMapping("get_multiple_data_source")
    @Operation(summary = "根据ID获取多数据详情")
    public CommonResult<MultipleDataSourceResp> getMultipleDataSourceById(@RequestParam Long id){
        return CommonResult.success(dataSourceService.getMultipleDataSourceById(id));
    }


    @GetMapping("/list_all")
    @Operation(summary = "列出所有的数据库")
    public CommonResult<List<DataSourceSelectResp>> listAllDataSource(){
        return CommonResult.success(dataSourceService.listDataSource());
    }

    @PostMapping(value = "/create_single_datasource")
    @Operation(summary = "创建单数据库")
    public CommonResult<String> createSingleDataSource(@RequestBody @Valid SingleDataSourceCreateReq singleDataSourceCreateReq){
        dataSourceService.createSingleDataSource(singleDataSourceCreateReq);
        return CommonResult.success();
    }

    @PostMapping(value = "/update_single_datasource")
    @Operation(summary = "修改单数据库")
    public CommonResult<String> updateSingleDataSource(@RequestBody @Valid SingleDataSourceUpdateReq updateReq){
        dataSourceService.updateSingleDataSource(updateReq);
        return CommonResult.success();
    }

    @PostMapping(value = "/create_multiple_datasource")
    @Operation(summary = "创建多数据库")
    public CommonResult<String> createMultipleDataSource(@RequestBody @Valid MultipleDataSourceCreateReq multipleDataSourceCreateReq){
        dataSourceService.createMultipleDataSource(multipleDataSourceCreateReq);
        return CommonResult.success();
    }

    @PostMapping(value = "/update_multiple_datasource")
    @Operation(summary = "修改多数据库")
    public CommonResult<String> updateMultipleDataSource(@RequestBody @Valid MultipleDataSourceUpdateReq updateReq){
        dataSourceService.updateMultipleDataSource(updateReq);
        return CommonResult.success();
    }

    @GetMapping("/list_all_tables")
    @Operation(summary = "查询数据库中所有表")
    public CommonResult<List<DataSourceExtensionBase>> listAllTables(DataBaseInfo dataBaseInfo){
        return CommonResult.success(dataSourceService.listAllTables(dataBaseInfo));
    }

    @GetMapping("/check_connection")
    @Operation(summary = "测试数据库连接")
    public CommonResult<String> checkConnection(DataBaseInfo dataBaseInfo){
        dataSourceService.checkConnection(dataBaseInfo);
        return CommonResult.success();
    }

    @GetMapping("/list_table_data")
    @Operation(summary = "查看数据库中表的数据（前100条）")
    public CommonResult<TableDataResp> listTableData(DataSourceExtensionBase dataSourceExtensionBase){
        return CommonResult.success(dataSourceService.selectData(dataSourceExtensionBase));
    }

    @GetMapping("/get_table_ddl")
    @Operation(summary = "获取表结构说明")
    public CommonResult<String> getTableDDL(DataSourceExtensionBase dataSourceExtensionBase){
        return CommonResult.success(dataSourceService.getTableDDL(dataSourceExtensionBase));
    }
}
