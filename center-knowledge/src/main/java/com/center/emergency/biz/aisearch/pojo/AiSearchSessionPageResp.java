package com.center.emergency.biz.aisearch.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI搜索会话分页查询响应
 */
@Data
@Schema(description = "AI搜索会话分页查询响应")
public class AiSearchSessionPageResp {
    
    @Schema(description = "会话ID")
    private Long id;
    
    @Schema(description = "会话标题")
    private String title;
    
    @Schema(description = "搜索模式代码：1-全网搜索，2-企业内部搜索")
    private Integer searchMode;
    
    @Schema(description = "搜索模式描述")
    private String searchModeDesc;
    
    @Schema(description = "搜索引擎类型")
    private String searchEngine;
    
    @Schema(description = "消息数量")
    private Long messageCount;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建者名称")
    private String creatorName;
} 