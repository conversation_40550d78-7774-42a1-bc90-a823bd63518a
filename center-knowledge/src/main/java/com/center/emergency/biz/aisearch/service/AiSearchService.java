package com.center.emergency.biz.aisearch.service;

import com.center.emergency.biz.aisearch.pojo.AiSearchSessionPageReq;
import com.center.emergency.biz.aisearch.pojo.AiSearchSessionPageResp;
import com.center.emergency.biz.aisearch.pojo.SearchRequest;
import com.center.framework.web.pojo.PageResult;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.List;
import java.util.Map;

/**
 * AI搜索服务接口
 */
public interface AiSearchService {
    
    /**
     * 执行AI搜索，返回SSE流式响应
     * 
     * @param request 搜索请求参数
     * @return SSE发射器
     */
    SseEmitter search(SearchRequest request);
    
    /**
     * 获取会话历史记录
     * 
     * @param sessionId 会话ID
     * @return 历史记录列表
     */
    List<Map<String, Object>> getSessionHistory(Long sessionId);
    
    /**
     * 删除会话及其所有相关数据
     * 
     * @param sessionId 会话ID
     */
    void deleteSession(Long sessionId);
    
    /**
     * 分页查询用户会话列表（统一方法，支持多条件筛选）
     * 
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    PageResult<AiSearchSessionPageResp> pageUserSessions(AiSearchSessionPageReq request);
    
    /**
     * 获取当前用户的所有会话列表
     * 
     * @return 会话列表
     * @deprecated 建议使用 pageUserSessions 方法进行分页查询
     */
    @Deprecated
    List<Map<String, Object>> getUserSessions();
    
    /**
     * 根据搜索模式获取用户的会话列表
     * 
     * @param searchMode 搜索模式
     * @return 会话列表
     * @deprecated 建议使用 pageUserSessions 方法进行分页查询
     */
    @Deprecated
    List<Map<String, Object>> getUserSessionsByMode(Integer searchMode);
    
    /**
     * 获取消息详情，包括关联的事件
     * 
     * @param messageId 消息ID
     * @return 消息详情
     */
    Map<String, Object> getMessageDetail(Long messageId);
    
    /**
     * 更新消息的点赞状态
     * 
     * @param messageId 消息ID
     * @param thumbsUp 点赞状态：0-无，1-赞，-1-踩
     */
    void updateMessageThumbsUp(Long messageId, Integer thumbsUp);
    
    /**
     * 获取会话统计信息
     * 
     * @param sessionId 会话ID
     * @return 统计信息
     */
    Map<String, Object> getSessionStats(Long sessionId);
    
    /**
     * 更新会话标题
     * 
     * @param sessionId 会话ID
     * @param title 新标题
     */
    void updateSessionTitle(Long sessionId, String title);
} 