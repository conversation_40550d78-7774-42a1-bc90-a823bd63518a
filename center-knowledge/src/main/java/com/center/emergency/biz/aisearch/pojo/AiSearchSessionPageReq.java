package com.center.emergency.biz.aisearch.pojo;

import com.center.framework.web.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * AI搜索会话分页查询请求
 */
@Data
@Schema(description = "AI搜索会话分页查询请求")
public class AiSearchSessionPageReq extends PageParam {
    
    @Schema(description = "会话标题关键词（模糊搜索）")
    @Length(max = 100, message = "搜索关键词长度不能超过100个字符")
    private String keyword;
    
    @Schema(description = "搜索模式：1-全网搜索，2-企业内部搜索（可选，不传则查询所有）")
    @Min(value = 1, message = "搜索模式必须为1或2")
    @Max(value = 2, message = "搜索模式必须为1或2")
    private Integer searchMode;
    
    @Schema(description = "搜索引擎类型（可选）")
    @Length(max = 50, message = "搜索引擎类型长度不能超过50个字符")
    private String searchEngine;
} 