package com.center.emergency.biz.database.connector;

import cn.hutool.core.util.StrUtil;
import com.center.emergency.biz.database.pojo.Column;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractDatabaseConnector implements DatabaseConnector{

    @Override
    public List getTableNameList(ResultSet tableSet, String databaseName) {
        List tableNameList = new ArrayList<>();
        //遍历所有表
        while (true) {
            try {
                if (!tableSet.next()) break;            // 获取表名
                String tableName = tableSet.getString("TABLE_NAME");
                String schema = tableSet.getString("TABLE_SCHEM");
                Map tableMap = new LinkedHashMap();
                if(schema != null) {
                    tableMap.put("fileName", schema + "." + tableName);
                    tableMap.put("filePath", schema + "." + tableName);
                }else {
                    tableMap.put("fileName", tableName);
                    tableMap.put("filePath", tableName);
                }
                tableNameList.add(tableMap);
            } catch (SQLException e) {
                log.error("获取表名失败!", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR);
            }
        }
        return tableNameList;
    }

    @Override
    public List getColumnList(DatabaseConnector connector,ResultSet columnResultSet,ResultSet primaryKeyResultSet){
        List<Column> columnList = new ArrayList<>();
        //获取主键集合
        Set<String> primaryKeyColumnSet = new HashSet<>();
        try {
            while (primaryKeyResultSet.next()) {
                primaryKeyColumnSet.add(primaryKeyResultSet.getString("COLUMN_NAME"));
            }
            //遍历列集合
            if (columnResultSet != null) {
                while (columnResultSet.next()) {
                    Column column = new Column();
                    column.setName(columnResultSet.getString("COLUMN_NAME"));
                    column.setSchema(columnResultSet.getString("TABLE_SCHEM"));
                    column.setCategory(connector.convertDataType(columnResultSet.getInt("DATA_TYPE")));
                    column.setIsNullable(columnResultSet.getString("IS_NULLABLE"));
                    column.setKey(primaryKeyColumnSet.contains(columnResultSet.getString("COLUMN_NAME")) ? "PRIMARY" : "NOT PRIMARY");
                    column.setComment(columnResultSet.getString("Remarks"));
                    column.setDefaultVal(columnResultSet.getString("COLUMN_DEF") != null ? columnResultSet.getString("COLUMN_DEF") : "none");
                    columnList.add(column);
                }
            }
        }catch (SQLException e) {
            log.error("获取列集合出错!",e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR,"获取列集合出错!");
        }finally {
            try {
                if (connector != null) {
                    connector.close();
                }
            } catch (Exception e) {
                log.error("关闭数据库连接出错!",e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CLOSE_DATABASE_ERROR);
            }
        }
        return columnList;
    }

    @Override
    public String convertDataType(int dataType){
        return null;
    }

  @Override
  public String getTableDDL(DatabaseConnector connector, String tableName) {
    String result = StrUtil.EMPTY;
    PreparedStatement pstmt = null;
    try {
      String sql = "SHOW CREATE TABLE " + tableName;
      pstmt = connector.getConnection().prepareStatement(sql);
      ResultSet rs = pstmt.executeQuery();
      if (rs.next()) {
          result = rs.getString("Create Table");
      }
    } catch (SQLException e) {
        log.error("获取表的DDL出错!"+connector.toString()+",tableName="+tableName, e);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,"获取表的DDL出错");
    } finally {
      try {
        if (null != pstmt) {
          pstmt.close();
        }
        if (null != connector) {
          connector.close();
        }
      } catch (SQLException e) {
        throw new RuntimeException(e);
      }
    }
    return result;
  }
}
