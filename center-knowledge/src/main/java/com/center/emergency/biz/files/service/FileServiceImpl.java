package com.center.emergency.biz.files.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.approval.service.RecordService;
import com.center.emergency.biz.chat.persistence.ChatQuestionRepository;
import com.center.emergency.biz.files.dto.FileNameDTO;
import com.center.emergency.biz.files.persistence.*;
import com.center.emergency.biz.files.pojo.*;
import com.center.emergency.biz.files.pojo.filechunk.Chunk;
import com.center.emergency.biz.files.pojo.filechunk.ChunkResponse;
import com.center.emergency.biz.knowledge.persistence.KnowledgeModel;
import com.center.emergency.biz.knowledge.persistence.KnowledgeRepository;
import com.center.emergency.biz.knowledge.pojo.KnowledgeCreateReq;
import com.center.emergency.biz.knowledge.service.KnowledgeService;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseModel;
import com.center.emergency.biz.knowledgebase.persistence.KnowledgeBaseRepository;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.biz.tag.persistence.TagModel;
import com.center.emergency.biz.tag.persistence.TagRepository;
import com.center.emergency.biz.tag.pojo.TagFileRsp;
import com.center.emergency.biz.tag.pojo.TagUpdateReq;
import com.center.emergency.biz.tag.service.TagService;
import com.center.emergency.biz.webskt.WebSocketServer;
import com.center.emergency.common.enumeration.FileStatusEnum;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.emergency.common.enumeration.PdfStatusEnum;
import com.center.emergency.common.enumeration.RecordStatusEnum;
import com.center.emergency.common.utils.FileTypeValidator;
import com.center.emergency.common.utils.OkHttpUtils;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.knowledge.KnowledgeApiTool;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.db.config.SnowFlakeConfig;
import com.center.framework.pdf.component.PDFConvert;
import com.center.framework.storage.factory.FileStorageServiceFactory;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.storage.interfaces.template.FilePathBuilder;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文件模块业务实现类
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private FileRepository fileRepository;

    @Autowired
    private KnowledgeRepository knowledgeRepository;

    @Autowired
    private AsyncPythonApiService asyncPythonApiService;

    @Autowired
    KnowledgeService knowledgeService;

    @Autowired
    private FileTagRepository fileTagRepository;

    @Autowired
    private PDFConvert pdfConvert;

    @Resource
    private PdfMessageRepository pdfMessageRepository;
    @Resource
    private PdfJobRepository pdfJobRepository;

    @Value("${hadoop.fs.defaultFS}")
    private String defaultFs;
    @Value("${python.api-url-upload}")
    private String apiUrlUpload;

    @Value("${python.api-base-url}")
    private String modelUrl;

    @Autowired
    private SnowFlakeConfig snowFlakeConfig;
    @Resource
    private JPAQueryFactory queryFactory;

    @Autowired
    private KnowledgeBaseRepository knowledgeBaseRepository;
    @Autowired
    private TagService tagService;
    @Autowired
    private TagRepository tagRepository;
    @Resource
    private RecordService recordService;

    @Value("${node.dir}")
    private String dir;

    @Autowired
    private FileStorageServiceFactory fileStorageServiceFactory;

    @Resource
    private FileComponent fileComponent;

    /**
     * 创建文件
     *
     * @param req 文件创建请求
     */
    @Override
    public Long createFile(FileUploadReq req) {
        // 转换请求对象到持久化模型对象
        FileModel fileModel = OrikaUtils.convert(req, FileModel.class);
        // 保存到数据库
        fileRepository.save(fileModel);
        return fileModel.getId();
    }

    /**
     * 更新文件
     *
     * @param req 文件更新请求
     */
    @Override
    @Transactional
    public void updateFile(FileUpdateReq req) {
        // 1. 通过 fileId 查询文件实体
        FileModel file = fileRepository.findById(req.getId())
                .orElseThrow(() ->
                        ServiceExceptionUtil.exception(
                                GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,
                                "文件不存在",
                                new Exception("文件ID：" + req.getId())
                        )
                );

        // 2. 更新文件信息（如文件名、文件描述）
        if (req.getFileName() != null && !req.getFileName().equals(file.getFileName())) {
            // 文件名称的唯一性校验（同一知识库下不允许重名）
            boolean isDuplicateFileName = fileRepository.existsByFileNameAndKbIdAndIdNot(
                    req.getFileName(),
                    file.getKbId(),
                    req.getId()
            );
            if (isDuplicateFileName) {
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.FILENAME_EXIST_ERROR,
                        req.getFileName(),
                        new Exception("文件名：" + req.getFileName())
                );
            }
            // 更新文件名
            file.setFileName(req.getFileName());
        }

        // 3. 如果 tags 不为空，根据 tag ID 查询标签信息并填充到 tagList
        if (req.getTags() != null) {
            // 当 `tags` 不为空时，根据标签ID从数据库查询对应的标签对象
            List<TagModel> tagModels = tagRepository.findAllById(req.getTags());
            if (tagModels.size() != req.getTags().size()) {
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,
                        "部分标签不存在"
                );
            }

            // 将 TagModel 转换为 TagUpdateReq
            List<TagUpdateReq> tagUpdateReqs = tagModels.stream()
                    .map(tagModel -> {
                        TagUpdateReq tagUpdateReq = new TagUpdateReq();
                        tagUpdateReq.setId(tagModel.getId());
                        tagUpdateReq.setTagName(tagModel.getTagName());
                        return tagUpdateReq;
                    })
                    .collect(Collectors.toList());

            // 设置转换后的 tagList
            req.setTagList(tagUpdateReqs);
        }

        // 4. 更新标签信息
        try {
            // 异步调用算法接口更新标签
            asyncPythonApiService.updateTagsOnPythonApi(file.getId(), req.getTagList());

            // 清除当前文件的所有标签关联
            fileTagRepository.deleteByFileId(req.getId());

            // 如果 `tagList` 非空，则添加新的标签关联
            if (req.getTagList() != null && !req.getTagList().isEmpty()) {
                List<FileTagModel> newFileTags = req.getTagList().stream()
                        .map(tag -> {
                            FileTagModel fileTagModel = new FileTagModel();
                            fileTagModel.setFileId(req.getId());
                            fileTagModel.setTagId(tag.getId());
                            fileTagModel.setKbId(req.getKbId());
                            return fileTagModel;
                        })
                        .collect(Collectors.toList());

                fileTagRepository.saveAll(newFileTags);
            }
        } catch (Exception e) {
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                    e,
                    "更新标签失败"
            );
        }

        // 5. 保存更新后的文件实体
        fileRepository.save(file);
    }


    /**
     * 删除文件（物理删除）
     *
     * @param id 文件ID
     */
    @Override
    @Transactional
    public void deleteFile(Long id) {
        // 1. 检查文件是否存在
        FileModel fileModel = fileRepository.findById(id)
                .orElseThrow(() ->
                        ServiceExceptionUtil.exception(
                                GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,
                                "文件不存在",
                                new Exception("文件ID：" + id)
                        )
                );
        // 2. 删除与文件相关的标签关联
        try {
            fileTagRepository.deleteByFileId(id);
        } catch (Exception e) {
            log.error("删除文件标签关联失败，文件ID：{}", id, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.DELETE_OBJECT_ERROR,
                    e,
                    "文件删除失败"
            );
        }
        // 3. 删除与文件相关的知识关联
        try {
            knowledgeRepository.deleteByFileId(id);
        } catch (Exception e) {
            log.error("删除文件知识关联失败，文件ID：{}", id, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.DELETE_OBJECT_ERROR,
                    e,
                    "文件删除失败"
            );
        }
        // 4. 删除文件
        String path = fileModel.getHdfsPath();
        if (path != null && !path.isEmpty()) {
            // 获取当前存储服务
            FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
            try {
                List<String> paths = Collections.singletonList(path);
                fileStorageService.deleteParentDirectoryOfFilePath(paths);
            } catch (Exception e) {
                log.error("删除文件失败，路径：{}", path, e);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.DELETE_OBJECT_ERROR,
                        e,
                        "文件删除失败"
                );
            }
        } else {
            log.warn("文件 ID {} 没有路径，跳过删除步骤", id);
        }
        // 5. 调用算法接口删除文件
        try {
            asyncPythonApiService.deleteFileOnPythonApi(fileModel);
        } catch (Exception e) {
            log.error("调用算法删除文件 API 失败，文件ID：{}", id, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                    e,
                    "文件删除失败"
            );
        }
        // 6. 删除文件记录（物理删除）
        try {
            fileRepository.delete(fileModel);
        } catch (Exception e) {
            log.error("删除文件记录失败，文件ID：{}", id, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    e,
                    "删除文件记录失败"
            );
        }
    }


    /**
     * 查询文件详情
     *
     * @param id 文件ID
     * @return 文件模型
     */
    @Override
    public FileResp getFileById(Long id) {
        // 查找文件信息
        FileModel fileModel = fileRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "文件不存在"));

        // 将 FileModel 转换为 FileResp
        FileResp fileResp = OrikaUtils.convert(fileModel, FileResp.class);

        // 拼接 pdfPreviewPath 字段
        if (fileModel.getPdfHdfsPath() != null) {
            String pdfPreviewPath = fileComponent.previewUrlByPath(fileModel.getPdfHdfsPath());
            fileResp.setPdfPreviewPath(pdfPreviewPath);
        }
        return fileResp;
    }

    /**
     * 根据知识库ID查询文件列表
     *
     * @param kbId 知识库ID
     * @return 文件列表
     */
    @Override
    public List<FileResp> getFilesByKnowledgeBaseId(Long kbId) {
        try {
            // 初始化查询实体对象
            QFileModel fileModel = QFileModel.fileModel;
            QFileTagModel fileTagModel = QFileTagModel.fileTagModel;
            QTagModel tagModel = QTagModel.tagModel;
            QUserModel userModel = QUserModel.userModel;

            // 构建查询条件：根据知识库ID
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(fileModel.kbId.eq(kbId));

            // 执行查询并按更新时间倒序排序
            List<Tuple> queryResults = queryFactory.select(fileModel, tagModel, userModel.displayName)
                    .from(fileModel)
                    .leftJoin(fileTagModel).on(fileModel.id.eq(fileTagModel.fileId))
                    .leftJoin(tagModel).on(fileTagModel.tagId.eq(tagModel.id))
                    .leftJoin(userModel).on(fileModel.updaterId.eq(userModel.id))
                    .where(builder)
//                    .orderBy(fileModel.updateTime.desc())
                    .orderBy(fileModel.fileName.asc())
                    .fetch();

            if (queryResults.isEmpty()) {
                log.warn("根据知识库ID {} 查询的文件列表为空", kbId);
                return Collections.emptyList();
            }

            // 使用 LinkedHashMap 保持插入顺序
            Map<Long, FileResp> fileMap = new LinkedHashMap<>();

            for (Tuple tuple : queryResults) {
                FileModel fileModelResult = tuple.get(fileModel);
                TagModel tagModelResult = tuple.get(tagModel);
                String operatorName = tuple.get(userModel.displayName);

                // 获取或创建 FileResp
                FileResp fileResp = fileMap.computeIfAbsent(fileModelResult.getId(), id -> {
                    FileResp newFileResp = OrikaUtils.convert(fileModelResult, FileResp.class);
                    newFileResp.setTags(new ArrayList<>());
                    newFileResp.setOperator(operatorName);

                    // 构建并设置 pdfPreviewPath
                    if (fileModelResult.getPdfHdfsPath() != null) {
                        try {
                            newFileResp.setPdfPreviewPath(fileComponent.previewUrlByPath(fileModelResult.getPdfHdfsPath()));
                        } catch (Exception e) {
                            log.error("构建 PDF 预览路径失败，文件ID：{}", fileModelResult.getId(), e);
                            throw ServiceExceptionUtil.exception(
                                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                                    e,
                                    "查询文件列表失败"
                            );
                        }
                    }
                    return newFileResp;
                });

                // 添加标签到文件
                if (tagModelResult != null) {
                    TagFileRsp tagFileRsp = new TagFileRsp();
                    tagFileRsp.setId(tagModelResult.getId());
                    tagFileRsp.setTagName(tagModelResult.getTagName());
                    tagFileRsp.setTagType(tagModelResult.getTagType());
                    fileResp.getTags().add(tagFileRsp);
                }
            }

            // 将有序的 Map 转换为 List
            return new ArrayList<>(fileMap.values());

        } catch (Exception e) {
            log.error("根据知识库ID查询文件列表时出现异常，知识库ID：{}", kbId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "查询文件列表失败"
            );
        }
    }


    /**
     * 首先，单独统计文件的总数，避免标签的干扰。
     * 分页时，仅对文件进行分页，不对标签进行分页。
     * 在获取文件信息后，再查询标签并进行组合。
     */
    @Override
    public PageResult<FileResp> getFilesByKnowledgeBaseId(@Valid FilePageQueryReq filePageQueryReq) {
        try {
            // 获取分页参数
            Integer pageNo = filePageQueryReq.getPageNo();
            Integer pageSize = filePageQueryReq.getPageSize();
            Long kbId = filePageQueryReq.getKbId();

            // 定义实体对象的 Q 类
            QFileModel fileModel = QFileModel.fileModel;
            QUserModel userModel = QUserModel.userModel;

            // 构建查询条件：知识库ID
            BooleanBuilder builder = new BooleanBuilder();
            if (kbId != null) {
                builder.and(fileModel.kbId.eq(kbId));
            }

            // 创建分页请求
            Pageable pageable = PageRequest.of(pageNo - 1, pageSize);

            // 查询总数，仅统计文件数量
            long total = queryFactory.select(fileModel.id.countDistinct())
                    .from(fileModel)
                    .where(builder)
                    .fetchOne();

            if (total == 0) {
                log.warn("根据知识库ID {} 查询的文件列表为空", kbId);
                return PageResult.empty();
            }

            // 查询文件和操作人信息，并按更新时间倒序排序（仅对文件进行分页）
            List<Tuple> fileResults = queryFactory.select(fileModel, userModel.displayName)
                    .from(fileModel)
                    .leftJoin(userModel).on(fileModel.updaterId.eq(userModel.id))
                    .where(builder)
//                    .orderBy(fileModel.updateTime.desc())
                    .orderBy(fileModel.fileName.asc())
                    .offset(pageable.getOffset())
                    .limit(pageable.getPageSize())
                    .fetch();

            // 使用 LinkedHashMap 来保持插入顺序并避免重复文件
            Map<Long, FileResp> fileMap = new LinkedHashMap<>();

            // 提取文件ID列表，用于后续查询标签
            List<Long> fileIds = new ArrayList<>();

            for (Tuple tuple : fileResults) {
                FileModel fileModelResult = tuple.get(fileModel);
                String operatorName = tuple.get(userModel.displayName);

                // 获取或创建 FileResp
                FileResp fileResp = OrikaUtils.convert(fileModelResult, FileResp.class);
                fileResp.setTags(new ArrayList<>());
                fileResp.setOperator(operatorName);

                // 构建并设置 pdfPreviewPath
                if (fileModelResult.getPdfHdfsPath() != null) {
                    try {
                        fileResp.setPdfPreviewPath(fileComponent.previewUrlByPath(fileModelResult.getPdfHdfsPath()));
                    } catch (Exception e) {
                        log.error("构建 PDF 预览路径失败，文件ID：{}", fileModelResult.getId(), e);
                        throw ServiceExceptionUtil.exception(
                                GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                                e,
                                "查询文件列表失败"
                        );
                    }
                }

                // 添加到 map 中并记录文件ID
                fileMap.put(fileModelResult.getId(), fileResp);
                fileIds.add(fileModelResult.getId());
            }

            // 查询标签信息（不分页，针对文件ID列表进行批量查询）
            if (!fileIds.isEmpty()) {
                QFileTagModel fileTagModel = QFileTagModel.fileTagModel;
                QTagModel tagModel = QTagModel.tagModel;

                List<Tuple> tagResults = queryFactory.select(fileTagModel.fileId, tagModel)
                        .from(fileTagModel)
                        .leftJoin(tagModel).on(fileTagModel.tagId.eq(tagModel.id))
                        .where(fileTagModel.fileId.in(fileIds))
                        .fetch();

                // 将标签信息加入到对应的文件响应对象中
                for (Tuple tagTuple : tagResults) {
                    Long fileId = tagTuple.get(fileTagModel.fileId);
                    TagModel tagModelResult = tagTuple.get(tagModel);

                    FileResp fileResp = fileMap.get(fileId);
                    if (fileResp != null && tagModelResult != null) {
                        TagFileRsp tagFileRsp = new TagFileRsp();
                        tagFileRsp.setId(tagModelResult.getId());
                        tagFileRsp.setTagName(tagModelResult.getTagName());
                        tagFileRsp.setTagType(tagModelResult.getTagType());
                        fileResp.getTags().add(tagFileRsp);
                    }
                }
            }

            // 返回分页结果，包含 total 和文件列表
            return PageResult.of(new ArrayList<>(fileMap.values()), total);

        } catch (Exception e) {
            log.error("分页获取文件列表时出现异常，知识库ID：{}", filePageQueryReq.getKbId(), e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.GET_OBJECT_ERROR,
                    e,
                    "分页获取文件列表失败"
            );
        }
    }


    /**
     * 上传文件
     *
     * @param kbId,originalFilename,filename 包括知识库ID、文件名等
     * @param file                           要上传的文件
     * @param rename
     * @return 上传文件的路径
     */
    @Override
    public FileUploadReq uploadFileFromLocal(MultipartFile file, Long kbId, String fileName, String originalFilename, Integer rename) {
        // 文件类型校验
        if (!FileTypeValidator.isSupportedFileType(file.getOriginalFilename())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILETYPE_ERROR, "暂不支持该类型文件");
        }
        FileUploadReq fileUploadReq = new FileUploadReq();
        List<FileUploadReq> fileUploadReqList = new ArrayList<>();
        long fileId = snowFlakeConfig.snowFlakeCore().nextId();

        try {
            // 获取当前登录用户的租户ID
            Long tenantId = Optional.ofNullable(LoginContextHolder.getLoginUserTenantId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "无法获取租户信息"));

            // 校验文件名是否重复
            boolean isDuplicateFileName = fileRepository.existsByFileNameAndKbId(fileName, kbId);
            fileUploadReq.setFileName(fileName);
            fileUploadReq.setFileNum(0);
            boolean kbExists = knowledgeBaseRepository.existsById(kbId);

            if (!kbExists) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识库不存在");
            }

            if (isDuplicateFileName) {
                // 文件名重复处理
                if (rename == 0) {
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "该知识库路径下存在文件名重复，请重命名");
                } else if (rename == 1) {
                    FileNameDTO fileNameDTO = determineUniqueFileName(kbId, originalFilename, fileUploadReqList);
                    fileUploadReq.setFileName(fileNameDTO.getFileName());
                    fileUploadReq.setFileNum(fileNameDTO.getFileNum());
                }
            }

            // 构建路径
            FilePathBuilder filePathBuilder = new FilePathBuilder();
            String dfsPath = filePathBuilder.buildFilePath(tenantId.toString(), kbId.toString(), String.valueOf(fileId), originalFilename);
            // 获取当前存储服务
            FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
            // 上传文件
            try (InputStream inputStream = file.getInputStream()) {
                fileStorageService.uploadFile(dfsPath, inputStream,true);
            } catch (IOException e) {
                log.error("文件上传失败，路径: {}", dfsPath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件上传失败");
            }

            // 更新文件上传对象
            fileUploadReq.setHdfsPath(dfsPath);
            fileUploadReq.setKbId(kbId);
            fileUploadReq.setId(fileId);
            fileUploadReq.setAiFilebFileId(fileId);
            fileUploadReq.setOriginalName(originalFilename);
            fileUploadReq.setStatus(FileStatusEnum.LOADING);

            log.info("文件成功上传 ，路径: {}", dfsPath);
            return fileUploadReq;

        } catch (ServiceException e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("未知错误，文件上传失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
        }
    }

    @Override
    @Transactional
    public Long uploadFileFromLocalForDailogue(MultipartFile file) {
//        Step one:上传文件
            // 文件类型校验
            if (!FileTypeValidator.isSupportedFileType(file.getOriginalFilename())) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILETYPE_ERROR, "暂不支持该类型文件");
            }
            long fileId = snowFlakeConfig.snowFlakeCore().nextId();

            try {
                // 获取当前登录用户的租户ID
                Long tenantId = Optional.ofNullable(LoginContextHolder.getLoginUserTenantId())
                        .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "无法获取租户信息"));

                // 构建路径
                FilePathBuilder filePathBuilder = new FilePathBuilder();
                String dfsPath = filePathBuilder.buildFilePath(tenantId.toString(), String.valueOf(fileId), file.getOriginalFilename());
                // 获取当前存储服务
                FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
                // 上传文件
                try (InputStream inputStream = file.getInputStream()) {
                    fileStorageService.uploadFile(dfsPath, inputStream,true);
                } catch (IOException e) {
                    log.error("文件上传失败，路径: {}", dfsPath, e);
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件上传失败");
                }
                log.info("文件成功上传 ，路径: {}", dfsPath);

                FileUploadReq fileUploadReq = new FileUploadReq();
                fileUploadReq.setHdfsPath(dfsPath);
                fileUploadReq.setId(fileId);
                fileUploadReq.setAiFilebFileId(fileId);
                fileUploadReq.setOriginalName(file.getOriginalFilename());
                fileUploadReq.setFileName(file.getOriginalFilename());
//              kbid不能为空，目前先用fileId来做kbid，后续把center_file表进行重构，不应该在这个表中加kbid这个字段，而是要创建一个知识库和文件的关系表。
                fileUploadReq.setKbId(fileId);

//                Step two:调用将文件转换为pdf
                fileUploadReq.setPdfHdfsPath(convertPdf(dfsPath));
                log.info("PDF文件转换和成功上传 ，路径: {}", fileUploadReq.getPdfHdfsPath());

//                Step three:调用python接口上传文件
                asyncPythonApiService.uploadFilesForDailogue(fileUploadReq);
//                Step four:将文件信息存储到数据库
                fileUploadReq.setStatus(FileStatusEnum.PROCESSED);

                FileUploadReq createReq = OrikaUtils.convert(fileUploadReq, FileUploadReq.class);
                return createFile(createReq);
            } catch (ServiceException e) {
                log.error("上传文件失败: {}", e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("未知错误，文件上传失败", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
        }
    }

    @Override
    public List<FileUploadReq> uploadFilesFromCloudToCloud(List<String> filePaths, Long kbId, List<String> dirPaths) {
        List<FileUploadReq> fileUploadReqList = new ArrayList<>();
        FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
        // 获取当前租户ID，如果未找到则抛出异常
        Long tenantId = Optional.ofNullable(LoginContextHolder.getLoginUserTenantId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "无法获取租户信息"));

        // 将目录中的文件路径加入 filePaths 列表
        if (!dirPaths.isEmpty()) {
            List<String> result = new ArrayList<>();
            for (String dirPath : dirPaths) {
                result.addAll(fileStorageService.getFileList(dirPath));
            }
            filePaths.addAll(result);
        }

        // 遍历所有文件路径并将文件复制到新的目标路径
        for (String filePath : filePaths) {
            String originalFilename = filePath.substring(filePath.lastIndexOf("/") + 1).replaceAll("[^a-zA-Z0-9\\-_\\.\\u4e00-\\u9fa5]", "_");
            // 使用 FileTypeValidator 检查文件类型是否受支持
            if (!FileTypeValidator.isSupportedFileType(originalFilename)) {
                log.warn("文件类型不支持，跳过上传：{}", originalFilename);
                continue;  // 跳过不支持的文件类型
            }
            Long fileId = snowFlakeConfig.snowFlakeCore().nextId();

            try {
                // 确定唯一的文件名称，避免重名
                FileNameDTO fileNameDTO = determineUniqueFileName(kbId, originalFilename, fileUploadReqList);
                // 构建路径
                FilePathBuilder filePathBuilder = new FilePathBuilder();
                String dfsPath = filePathBuilder.buildFilePath(tenantId.toString(), kbId.toString(), String.valueOf(fileId), originalFilename);
                // 使用 FileStorageService 上传文件
                fileStorageService.copyFromCloudToCloud(filePath, dfsPath);
                // 构建 FileUploadReq 并添加到列表中
                FileUploadReq fileUploadReq = new FileUploadReq();
                fileUploadReq.setId(fileId);
                fileUploadReq.setAiFilebFileId(fileId);
                fileUploadReq.setHdfsPath(dfsPath);
                // 设置唯一文件名
                fileUploadReq.setFileName(fileNameDTO.getFileName());
                fileUploadReq.setOriginalName(originalFilename);
                fileUploadReq.setKbId(kbId);
                // 设置文件的 file_num
                fileUploadReq.setFileNum(fileNameDTO.getFileNum());
                fileUploadReq.setStatus(FileStatusEnum.LOADING);
                fileUploadReqList.add(fileUploadReq);

            } catch (ServiceException e) {
                log.error("上传文件失败: {}", e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("未知错误，文件上传失败，路径: {}", filePath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
            }
        }

        return fileUploadReqList;
    }


    /**
     * 确定文件名称在同一知识库中是否唯一，如有重复则添加 file_num 来保证唯一性
     *
     * @param kbId              知识库 ID
     * @param originalName      原始文件名
     * @param fileUploadReqList 当前已经处理的文件列表，用于判断已存在的文件名
     * @return 返回带有 file_num 的唯一文件名称和 file_num
     */
    private FileNameDTO determineUniqueFileName(Long kbId, String originalName, List<FileUploadReq> fileUploadReqList) {
        // 获取所有已存在的文件名相同的文件及其 file_num
        Set<Integer> existingFileNums = new HashSet<>();

        // 从数据库中查找相同文件名的文件及其 file_num
        List<FileModel> existingFiles = fileRepository.findByOriginalNameAndKbId(originalName, kbId);
        for (FileModel existingFile : existingFiles) {
            existingFileNums.add(existingFile.getFileNum());
        }

        // 从当前批次的文件列表中查找相同文件名的文件及其 file_num
        for (FileUploadReq req : fileUploadReqList) {
            if (originalName.equals(req.getOriginalName())) {
                existingFileNums.add(req.getFileNum());
            }
        }

        // 找出最小的未使用的 file_num
        int fileNum = 0;
        while (existingFileNums.contains(fileNum)) {
            fileNum++;
        }
        if (fileNum == 0) {
            // 返回新的文件名和 file_num
            return new FileNameDTO(originalName, fileNum);
        } else {
            // 返回新的文件名和 file_num
            return new FileNameDTO(originalName + "_" + fileNum, fileNum);
        }

    }

    @Override
    public String convertPdf(String originPath) {
        FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
        String pdfName = originPath.substring(originPath.lastIndexOf("/") + 1);
        File pdfFile = null;
        File crcfile = null;
        if (!pdfName.endsWith(".pdf")) {
            int lastDotIndex = pdfName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                pdfName = pdfName.substring(0, lastDotIndex);
            }
            pdfName = pdfName + ".pdf";
            File localTempFile = null;
            try {
                localTempFile = File.createTempFile("temp", ".odt");
                String tempFilePath = localTempFile.getAbsolutePath();
                // 读取文件并写入到本地临时文件
                fileStorageService.copyToLocal(originPath, tempFilePath);
                // 转换为 PDF
                String pdfPath = pdfConvert.toPdf(tempFilePath, tempFilePath.substring(0, tempFilePath.lastIndexOf(File.separator)));
                // 上传文件
                fileStorageService.uploadFile(pdfPath, originPath.substring(0, originPath.lastIndexOf("/")) + "/" + pdfName);
                pdfFile = new File(pdfPath);
                String crcPath = localTempFile.getParent() + "\\\\." + localTempFile.getName() + ".crc";
                crcfile = new File(crcPath);
                return originPath.substring(0, originPath.lastIndexOf("/"))+ "/" + pdfName;
            } catch (IOException e) {
                log.error("文件上传异常", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
            } finally {
                // 删除临时文件
                if (localTempFile != null && localTempFile.exists()) {
                    localTempFile.delete();
                }
                if (pdfFile != null && pdfFile.exists()) {
                    pdfFile.delete();
                }
                if (crcfile != null && crcfile.exists()) {
                    crcfile.delete();
                }
            }
        }
        return originPath;
    }

    @Override
    public void updatePdfHdfsPath(Long fileId, String pdfHdfsPath) {
        FileModel fileModel = fileRepository.findById(fileId).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "文件不存在")
        );
        fileModel.setPdfHdfsPath(pdfHdfsPath);
        fileRepository.save(fileModel);
    }

    @Async
    @Override
    public void asyncCallPythonUploadAndConvertPdf(FileUploadReq fileUploadReq) {
        try {
            // Step 1: 调用 Python API 上传文件
            try {
                asyncPythonApiService.asyncCallPythonUpload(fileUploadReq);
                log.info("Python API 上传文件成功，文件 ID: {}", fileUploadReq.getId());
            } catch (ServiceException e) {
                log.error("调用 Python API 上传文件失败，文件 ID: {}", fileUploadReq.getId(), e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "调用 Python API 上传文件失败");
            }

            // Step 2: 检查文件是否需要转换为 PDF，并执行 PDF 转换
            String pdfUploadPath;
            pdfUploadPath = convertPdf(defaultFs + fileUploadReq.getHdfsPath());
            log.info("异步转换pdf成功！路径为{}", pdfUploadPath);

            // Step 3: 更新数据库中的 pdfHdfsPath 字段
            try {
                String pdfPath = pdfUploadPath.replace(defaultFs, "");
                updatePdfHdfsPath(fileUploadReq.getId(), pdfPath);
                log.info("数据库更新pdf路径成功！");
            } catch (Exception e) {
                log.error("更新数据库 PDF 路径失败，文件 ID: {}", fileUploadReq.getId(), e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DATABASE_UNKNOW_ERROR, e, "更新数据库 PDF 路径失败");
            }

        } catch (ServiceException e) {
            // 捕获 ServiceException，统一记录错误日志并处理
            log.error("异步调用处理文件转换出现 ServiceException，文件 ID: {}", fileUploadReq.getId(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "文件处理失败");
        } catch (Exception e) {
            // 捕获所有其他异常，记录日志并抛出自定义异常
            log.error("异步调用处理文件转换出现未知异常，文件 ID: {}", fileUploadReq.getId(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "文件处理失败");
        }
    }


    /**
     * 异步调用 Python API 批量处理多个文件上传
     *
     * @param fileUploadReqList 文件上传请求列表
     */
    @Async
    @Override
    public void asyncCallPythonUploadBatch(List<FileUploadReq> fileUploadReqList) {
        // 遍历每个文件上传请求，依次异步调用
        for (FileUploadReq fileUploadReq : fileUploadReqList) {
            asyncCallPythonUploadAndConvertPdf(fileUploadReq);
        }
    }

    @Transactional
    @Override
    public void updateFileStatusAndTagsByPython(FileUploadPythonReq req) {
        log.info("开始更新文件状态和标签，请求参数：{}", req);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            // 1. 根据文件ID查询文件
            FileModel fileModel = fileRepository.findById(req.getId())
                    .orElseThrow(() -> {
                        log.error("文件不存在，ID: {}", req.getId());
                        return ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "文件不存在");
                    });
            LoginContextHolder.setLoginUserId(fileModel.getUpdaterId());
            LoginContextHolder.setLoginUserTenantId(fileModel.getTenantId());
            // 2. 更新文件标签
            if (req.getTags() != null && !req.getTags().isEmpty()) {
                try {
                    // 清除当前文件的所有标签关联
                    fileTagRepository.deleteByFileId(req.getId());
                    // 校验是否有重复的标签ID
                    Set<Long> tagIdSet = new HashSet<>();
                    for (TagUpdateReq tag : req.getTags()) {
                        if (!tagIdSet.add(tag.getId())) {
                            log.error("重复的标签ID: {}", tag.getId());
                            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "重复的标签ID：" + tag.getId());
                        }
                    }

                    // 添加新的标签关联
                    List<FileTagModel> newFileTags = req.getTags().stream()
                            .map(tag -> {
                                FileTagModel fileTagModel = new FileTagModel();
                                fileTagModel.setFileId(req.getId());
                                fileTagModel.setTagId(tag.getId());
                                fileTagModel.setKbId(fileModel.getKbId());
                                return fileTagModel;
                            })
                            .collect(Collectors.toList());
                    fileTagRepository.saveAll(newFileTags);
                } catch (Exception e) {
                    log.error("更新文件标签失败，文件ID: {}", req.getId(), e);
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DATABASE_UNKNOW_ERROR, e, "更新文件标签失败");
                }
            }

            // 3. 更新文件状态
            try {
                if (FileStatusEnum.contains(req.getStatus().getValue())) {
                    fileModel.setStatus(req.getStatus());
                } else {
                    log.error("无效的文件状态: {}", req.getStatus());
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "无效的文件状态：" + req.getStatus());
                }

                // 保存更新后的文件实体
                fileRepository.save(fileModel);
            } catch (Exception e) {
                log.error("更新文件状态失败，文件ID: {}", req.getId(), e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DATABASE_UNKNOW_ERROR, e, "更新文件状态失败");
            }
            try {
                // 创建 FileUpdateMessage
                FileResp fileByQuery = getFileByQuery(req.getId());
                FileUpdateMessage fileUpdateMessage = new FileUpdateMessage();
                fileUpdateMessage.setId(String.valueOf(fileByQuery.getId()));
                fileUpdateMessage.setStatusName(fileByQuery.getStatus().getDescription());
                fileUpdateMessage.setStatus(fileByQuery.getStatus().getValue());
                fileUpdateMessage.setTags(fileByQuery.getTags());
                fileUpdateMessage.setPdfPreviewPath(fileByQuery.getPdfPreviewPath());
                fileUpdateMessage.setUpdateTime(fileByQuery.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                // 调用 WebSocket 发送更新
                String topic = "file";
                WebSocketServer.broadcastToTopic(fileUpdateMessage, topic);
            } catch (Exception e) {
                log.error("发送前端websocket消息失败，文件ID: {}", req.getId(), e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DOLPHIN_SCHEDULER_RESPONSE_ERROR, e, "调度websocket失败");
            }

        } catch (ServiceException e) {
            // 捕获 ServiceException，记录日志并重新抛出
            log.error("更新文件状态和标签失败，文件ID: {}", req.getId(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, e, "更新文件状态和标签失败");
        } catch (Exception e) {
            // 捕获其他异常，记录日志并抛出自定义异常
            log.error("更新文件状态和标签出现未知异常，文件ID: {}", req.getId(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, e, "更新文件状态和标签失败");
        }
    }


    @Override
    public List<FileListResp> getAllFiles(String path) {
        List<FileListResp> lists = new ArrayList<>();
        FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
        return fileStorageService.getAllFiles(path);
    }

    @Override
    public FileResp getFileByQuery(Long fileId) {
        try {
            // 定义 Q 类
            QFileModel fileModel = QFileModel.fileModel;
            QFileTagModel fileTagModel = QFileTagModel.fileTagModel;
            QTagModel tagModel = QTagModel.tagModel;
            QUserModel userModel = QUserModel.userModel;

            // 构建查询条件
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(fileModel.id.eq(fileId));

            // 执行查询，获取文件、标签和操作人信息
            List<Tuple> queryResults = queryFactory.select(fileModel, tagModel, userModel.displayName)
                    .from(fileModel)
                    .leftJoin(fileTagModel).on(fileModel.id.eq(fileTagModel.fileId))
                    .leftJoin(tagModel).on(fileTagModel.tagId.eq(tagModel.id))
                    .leftJoin(userModel).on(fileModel.updaterId.eq(userModel.id))
                    .where(builder)
                    .fetch();

            if (queryResults.isEmpty()) {
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "文件不存在"
                );
            }
            // 初始化 FileResp 对象
            FileResp fileResp = null;
            for (Tuple tuple : queryResults) {
                FileModel fileModelResult = tuple.get(fileModel);
                TagModel tagModelResult = tuple.get(tagModel);
                String operatorName = tuple.get(userModel.displayName);
                // 如果是首次创建 FileResp
                if (fileResp == null) {
                    fileResp = OrikaUtils.convert(fileModelResult, FileResp.class);
                    fileResp.setTags(new ArrayList<>()); // 初始化标签集合
                    fileResp.setOperator(operatorName); // 设置操作人名称
                    // 设置 pdfPreviewPath
                    if (fileModelResult.getPdfHdfsPath() != null) {
                        fileResp.setPdfPreviewPath(fileComponent.previewUrlByPath(fileModelResult.getPdfHdfsPath()));
                    }
                }
                // 如果标签不为空，则添加到标签集合中
                if (tagModelResult != null) {
                    TagFileRsp tagFileRsp = new TagFileRsp();
                    tagFileRsp.setId(tagModelResult.getId());
                    tagFileRsp.setTagName(tagModelResult.getTagName());
                    tagFileRsp.setTagType(tagModelResult.getTagType());
                    fileResp.getTags().add(tagFileRsp);
                }
            }
            // 返回单个文件对象
            return fileResp;
        } catch (Exception e) {
            log.error("查询文件详细信息失败，文件ID: {}", fileId, e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.DATA_NOT_FOUND_ERROR, e, "查询文件详情失败"
            );
        }
    }


    @Override
    public void generateKnowledgeForFile(FileGenerateKnowledgeReq req) {
        try {
            // 获取文件信息
            FileModel file = fileRepository.findById(req.getId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(
                            GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "文件不存在"
                    ));

            //更新文件状态为“知识生成中”
            file.setStatus(FileStatusEnum.PROCESSING);
            fileRepository.save(file); // 保存文件状态

            // 获取文件的关联标签信息
            List<TagFileRsp> tagFileRsps = tagService.getFileTagsByFileId(req.getId());
            List<TagUpdateReq> tagUpdateReqs = OrikaUtils.convertList(tagFileRsps, TagUpdateReq.class);
            req.setTags(tagUpdateReqs); // 设置标签信息
            req.setKbId(file.getKbId());
            //获取pdf文件完整路径
            String pdfUrl = fileComponent.previewUrlByPath(file.getPdfHdfsPath());
            //调用前端服务获取pdfTXT
            req.setPdfText(getBase64(pdfUrl));
            // 调用 Python API 生成知识
            try {
                asyncPythonApiService.generateKnowledgeOnPythonApi(req);
            } catch (Exception e) {
                log.error("调用 Python API 生成知识失败, 文件ID: {}", req.getId(), e);
                throw ServiceExceptionUtil.exception(
                        GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                        "调用 Python API 失败"
                );
            }

            // 检查知识库中是否已有该文件对应的知识
            Optional<KnowledgeModel> existingKnowledge = knowledgeRepository.findByFileId(req.getId());

            if (existingKnowledge.isPresent()) {
                // 如果存在对应的知识，则更新其状态
                KnowledgeModel knowledge = existingKnowledge.get();
                knowledge.setStatus(KnowledgeStatusEnum.CREATING);
                knowledgeRepository.save(knowledge);
                //知识重新修改
                recordService.RecordSave(knowledge.getId(), RecordStatusEnum.MODIFY, null);
            } else {
                //如果不存在对应的知识，创建新的知识记录
                KnowledgeCreateReq knowledgeCreateReq = new KnowledgeCreateReq();
                knowledgeCreateReq.setKnowledgeName(file.getFileName() + "_知识");
                knowledgeCreateReq.setFileId(file.getId());
                knowledgeCreateReq.setStatus(KnowledgeStatusEnum.CREATING);
                knowledgeCreateReq.setKbId(file.getKbId());

                try {
                    knowledgeService.createKnowledge(knowledgeCreateReq);
                } catch (Exception e) {
                    log.error("创建新知识记录失败, 文件ID: {}", req.getId(), e);
                    throw ServiceExceptionUtil.exception(
                            GlobalErrorCodeConstants.DATABASE_UNKNOW_ERROR,
                            "创建新知识记录失败"
                    );
                }
            }

        } catch (Exception e) {
            log.error("生成文件知识过程中发生异常, 文件ID: {}", req.getId(), e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,
                    "生成文件知识失败"
            );
        }
    }


    @Override
    @Transactional
    public UploadFileReps uploadFile(MultipartFile file) {
        FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
        String pdfName = file.getOriginalFilename();
        UploadFileReps reps = new UploadFileReps();
        File pdfFile = null;
        File crcfile = null;
        if (!pdfName.endsWith(".pdf")) {
            int lastDotIndex = pdfName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                pdfName = pdfName.substring(0, lastDotIndex);
            }
            pdfName = pdfName + ".pdf";
            File localTempFile = null;
            // 读取文件并写入到本地临时文件
            try {
                localTempFile = File.createTempFile("temp", ".odt");
                // 将 MultipartFile 写入临时文件
                file.transferTo(localTempFile);
                // 读取临时文件路径
                String tempFilePath = localTempFile.getAbsolutePath();
                // 转换为 PDF
                String pdfPath = pdfConvert.toPdf(tempFilePath, tempFilePath.substring(0, tempFilePath.lastIndexOf(File.separator)));
                String dfsPath = "/knowledge/file/" + UUID.randomUUID().toString();
                String crcPath = localTempFile.getParent() + dfsPath + ".crc";
                dfsPath = dfsPath + ".pdf";
                //上传
                fileStorageService.uploadFile(pdfPath, dfsPath);
                pdfFile = new File(pdfPath);
                crcfile = new File(crcPath);
                // 调用 Python API
                try {
                    JSONObject fileReps = KnowledgeApiTool.post(apiUrlUpload, pdfFile);
                    reps.setFileId(fileReps.get("file_id").toString());
                    reps.setOutputFilePath(fileReps.get("output_file_path").toString());
                } catch (Exception e) {
                    log.error("调用 Python API 失败: {}", e.getMessage(), e);
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "调用 Python API 失败: " + e.getCause());
                }
                reps.setFilePath(dfsPath);
                return reps;
            } catch (IOException e) {
                log.error("文件上传异常", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
            } finally {
                // 删除临时文件
                if (localTempFile != null && localTempFile.exists()) {
                    localTempFile.delete();
                }
                if (pdfFile != null && pdfFile.exists()) {
                    pdfFile.delete();
                }
                if (crcfile != null && crcfile.exists()) {
                    crcfile.delete();
                }
            }
        } else {
            File localTempFile = null;
            try {
                localTempFile = File.createTempFile("temp", ".pdf");
                // 将 MultipartFile 写入临时文件
                file.transferTo(localTempFile);
                // 读取临时文件路径
                String tempFilePath = localTempFile.getAbsolutePath();
                //获取临时路径名
                String dfsPath = "/knowledge/file/" + UUID.randomUUID().toString();
                String crcPath = localTempFile.getParent() + dfsPath + ".crc";
                dfsPath = dfsPath + ".pdf";
                crcfile = new File(crcPath);
                //上传pdf
                fileStorageService.uploadFile(tempFilePath, dfsPath);
                // 调用 Python API
                try {
                    JSONObject fileReps = KnowledgeApiTool.post(apiUrlUpload, new File(tempFilePath));
                    reps.setFileId(fileReps.get("file_id").toString());
                    reps.setOutputFilePath(fileReps.get("output_file_path").toString());
                } catch (Exception e) {
                    log.error("调用 Python API 失败: {}", e.getMessage(), e);
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "调用 Python API 失败: " + e.getCause());
                }
                reps.setFilePath(dfsPath);
                return reps;
            } catch (IOException e) {
                log.error("文件上传异常", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
            } finally {
                // 删除临时文件
                if (localTempFile != null && localTempFile.exists()) {
                    localTempFile.delete();
                }
                if (pdfFile != null && pdfFile.exists()) {
                    pdfFile.delete();
                }
                if (crcfile != null && crcfile.exists()) {
                    crcfile.delete();
                }
            }
        }
    }

    @Override
    public Long getJobId(PdfReq pdfReq) {
        String url=pdfReq.getUrl();
        String inputs=pdfReq.getInputList();
        String fileId=pdfReq.getFileId();
        //存入数据库
        PdfJobModel pdfJob = new PdfJobModel();
        pdfJob.setPdfUrl(url);
        pdfJob.setStatus(PdfStatusEnum.PROCESSING);
        pdfJobRepository.save(pdfJob);
        //构造请求体数据
        String path = url.split("filePath=")[1];
        try {
            // 获取当前存储服务
            FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
            ResponseEntity<ByteArrayResource> response = fileStorageService.previewFile(path);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 从响应中获取字节资源
            ByteArrayResource byteArrayResource = response.getBody();
            if (byteArrayResource != null) {
                // 读取字节数据
                InputStream inputStream = byteArrayResource.getInputStream();
                byte[] readBuffer = new byte[1024];
                int readBytes;
                while ((readBytes = inputStream.read(readBuffer)) != -1) {
                    byteArrayOutputStream.write(readBuffer, 0, readBytes);
                }
                // 编码为 Base64 字符串
                String base64 = getBase64(url);
                // 创建结果映射
                Map<String, Object> resultMap = new HashMap<>();
                Map<String, Object> inputsMap = new HashMap<>();
                Map<String, Object> fileMap = new HashMap<>();
                fileMap.put("type", "document");
                fileMap.put("transfer_method", "local_file");
                fileMap.put("url", "");
                fileMap.put("upload_file_id", fileId);

                inputsMap.put("FILE",fileMap);
                inputsMap.put("QUERY",inputs);
                inputsMap.put("PDF_JS_BASE64",base64);
                inputsMap.put("JOB_ID",pdfJob.getId().toString());

                resultMap.put("inputs",inputsMap);
                resultMap.put("response_mode","blocking");
                resultMap.put("user","wzzhou");

                // 异步调用 Python API
                asyncPythonApiService.asyncPostJob(resultMap);
            }
        } catch (Exception e) {
            log.info("提取文件信息失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, e, "提取文件信息失败");
        }
        return pdfJob.getId();
    }

    @Override
    @Transactional
    public void savePdfMessage(PdfMessageReq pdfMessageReq) {
        List<PdfDataReq> pdfData = pdfMessageReq.getFormatted_attributes();
        Long jobId = Long.valueOf(pdfMessageReq.getJob_id());
        PdfJobModel pdfJobModel = pdfJobRepository.findById(jobId).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "pdf任务不存在"));
        try {
            if (pdfJobModel.getStatus().equals(PdfStatusEnum.PROCESSING) && pdfMessageReq.getStatus().equals("SUCCESS")) {
                for (PdfDataReq data : pdfData) {
                    for (PdfValueReq valueReq : data.getValue()) {
                        PdfMessageModel pdfMessageModel = new PdfMessageModel();
                        pdfMessageModel.setJobId(jobId);
                        pdfMessageModel.setKey(data.getKey());
                        pdfMessageModel.setValue(valueReq.getValue());
                        pdfMessageModel.setStartLocation(valueReq.getStart_id());
                        pdfMessageModel.setEndLocation(valueReq.getEnd_id());
                        pdfMessageModel.setType(valueReq.getType());
                        log.info("保存数据:{}", pdfMessageModel);
                        pdfMessageRepository.save(pdfMessageModel);
                    }
                }
                pdfJobModel.setStatus(PdfStatusEnum.PROCESSED);
                pdfJobRepository.save(pdfJobModel);
            } else if (pdfMessageReq.getStatus().equals("FAILED")) {
                pdfJobModel.setStatus(PdfStatusEnum.FAILED);
                pdfJobRepository.save(pdfJobModel);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "调用 Python API 失败");
            } else
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "pdf任务状态错误");
        } catch (Exception e) {
            log.error("pdf数据保存失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR, e, "pdf数据保存失败");
        }
    }

    @Override
    public HashMap pdfMessage(Long jobId) {
        HashMap result = new HashMap();
        List<HashMap> pdfMessageList = new ArrayList<>();
        PdfJobModel pdfJobModel = pdfJobRepository.findById(jobId).orElseThrow(() ->
                ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "PDF任务不存在"));
        if (pdfJobModel.getStatus().equals(PdfStatusEnum.PROCESSED)) {
            List<PdfMessageModel> messageList = pdfMessageRepository.findByJobId(jobId);
            HashMap<String, List<HashMap<String, Object>>> aggregatedMap = new HashMap<>();
            for (PdfMessageModel pdfMessageModel : messageList) {
                String key = pdfMessageModel.getKey();
                if (!aggregatedMap.containsKey(key)) {
                    aggregatedMap.put(key, new ArrayList<>());
                }
                HashMap<String, Object> valueMap = new HashMap<>();
                valueMap.put("value", pdfMessageModel.getValue());
                valueMap.put("startLocation", pdfMessageModel.getStartLocation());
                valueMap.put("endLocation", pdfMessageModel.getEndLocation());
                valueMap.put("type", pdfMessageModel.getType());
                aggregatedMap.get(key).add(valueMap);
            }
            for (Map.Entry<String, List<HashMap<String, Object>>> entry : aggregatedMap.entrySet()) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("key", entry.getKey());
                map.put("value", entry.getValue());
                pdfMessageList.add(map);
            }
        }
        result.put("result", pdfMessageList);
        result.put("status", pdfJobModel.getStatus());
        return result;
    }

    /**
     * 从本地上传文件，并将文件信息存储到数据库
     * 该方法使用了事务注解，确保文件上传和信息存储要么全部完成，要么全部回滚
     *
     * @param file             上传的文件对象
     * @param kbId             文件库ID，用于指定文件存储的位置
     * @param fileName         文件名，用于在系统中标识文件
     * @param originalFilename 原始文件名，保留源文件的名称信息
     * @param rename           文件重命名标志，决定是否重命名文件
     * @return 返回上传文件的信息对象
     */
    @Transactional
    @Override
    public FileUploadReq uploadFileFromLocalWithTransaction(MultipartFile file, Long kbId, String fileName, String originalFilename, Integer rename) {
        // Step 1: 上传文件
        FileUploadReq uploadReq = uploadFileFromLocal(file, kbId, fileName, originalFilename.replaceAll("[^a-zA-Z0-9\\-_\\.\\u4e00-\\u9fa5]", "_"), rename);

        // Step 2: 将文件信息存储到数据库
        // 使用Orika进行对象转换，将上传的文件信息转换为数据库存储所需的对象
        FileUploadReq createReq = OrikaUtils.convert(uploadReq, FileUploadReq.class);
        // 调用createFile方法将文件信息保存到数据库
        createFile(createReq);

        // 异步调用 Python 接口
        asyncCallPythonUploadAndConvertPdf(uploadReq);

        return uploadReq;
    }

    /**
     * 从云存储中上传文件并保存到数据库
     *
     * @param filePaths 文件路径列表，代表需要上传的文件
     * @param kbId      知识库ID，用于确定文件存储的位置或分类
     * @param dirPaths  目录路径列表，用于指定文件上传后的存放目录
     * @return 返回上传并保存的文件信息列表
     * <p>
     * 此方法首先从云存储上传文件，然后将上传的文件信息转换为数据库模型并保存到数据库中
     * 使用了@Transactional注解，确保文件上传和数据库保存操作的事务性，即这两个操作要么都成功，要么都失败
     */
    @Transactional
    @Override
    public List<FileUploadReq> uploadAndSaveFilesFromCloud(List<String> filePaths, Long kbId, List<String> dirPaths) {
        // 上传文件并获取上传请求列表
        List<FileUploadReq> fileUploadReqs = uploadFilesFromCloudToCloud(filePaths, kbId, dirPaths);

        // 将 FileUploadReq 转换为 FileModel 并批量存储到数据库
        List<FileModel> fileModels = OrikaUtils.convertList(fileUploadReqs, FileModel.class);
        fileRepository.saveAll(fileModels);

        return fileUploadReqs;
    }

    @Override
    public Long upload(MultipartFile file) {
        FileUploadReq fileUploadReq = new FileUploadReq();
        long fileId = snowFlakeConfig.snowFlakeCore().nextId();

        try {
            // 获取当前登录用户的租户ID
            Long tenantId = Optional.ofNullable(LoginContextHolder.getLoginUserTenantId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "无法获取租户信息"));

            fileUploadReq.setFileName(file.getOriginalFilename());
            fileUploadReq.setFileNum(0);

            // 构建路径
            FilePathBuilder filePathBuilder = new FilePathBuilder();
            String dfsPath = filePathBuilder.buildFilePath(tenantId.toString(), String.valueOf(fileId), file.getOriginalFilename());
            // 获取当前存储服务
            FileStorageService fileStorageService = fileStorageServiceFactory.getFileStorageService();
            // 上传文件
            try (InputStream inputStream = file.getInputStream()) {
                fileStorageService.uploadFile(dfsPath, inputStream,true);
            } catch (IOException e) {
                log.error("文件上传失败，路径: {}", dfsPath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件上传失败");
            }

            // 更新文件上传对象
            fileUploadReq.setHdfsPath(dfsPath);
            fileUploadReq.setKbId(0L);
            fileUploadReq.setId(fileId);
            fileUploadReq.setOriginalName(file.getOriginalFilename());
            fileUploadReq.setStatus(FileStatusEnum.LOADED);

            log.info("文件成功上传 ，路径: {}", dfsPath);

            // 转换请求对象到持久化模型对象
            FileModel fileModel = OrikaUtils.convert(fileUploadReq, FileModel.class);
            // 保存到数据库
            Long id = fileRepository.save(fileModel).getId();
            return id;
        } catch (Exception e) {
            log.error("未知错误，文件上传失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
        }
    }

    @Override
    public PageResult<Chunk> getDocChunk(DocChunkPageReq docChunkPageReq) {
        Optional<FileModel> optional = fileRepository.findById(docChunkPageReq.getFileId());
        if(!optional.isPresent()){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "该文件不存在！");

        }
        FileModel fileModel = optional.get();
        Optional<KnowledgeBaseModel> opt = knowledgeBaseRepository.findById(fileModel.getKbId());
        if(!opt.isPresent()){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "该文件没有生成Chunk！");
        }
        KnowledgeBaseModel knowledgeBaseModel = opt.get();
        OkHttpClient client = OkHttpUtils.getOkHttpClient();
        Map<String,Object> param = new HashMap<>();
        param.put("user_id","xxy");
        param.put("kb_id",knowledgeBaseModel.getAiFilebId());
        param.put("file_id",String.valueOf(docChunkPageReq.getFileId()));
        param.put("page_id",docChunkPageReq.getPageNo());
        param.put("page_limit",docChunkPageReq.getPageSize());
        param.put("keyword",docChunkPageReq.getKeyword());
        Request request = OkHttpUtils.getJsonPostRequest(modelUrl+"/api/local_doc_qa/get_doc_completed", param);

        List<Chunk> chunks = new ArrayList();
        Long total = 0L;
        try {
            Response response = client.newCall(request).execute();
            String jsonString = response.body().string();
            ChunkResponse chunkResponse = JSONUtil.toBean(jsonString, ChunkResponse.class);
            chunks = chunkResponse.getChunks();
            total = Long.valueOf(chunkResponse.getTotal_count());
        } catch (IOException e) {
            log.error("调用算法接口读取文件Chunk失败！",e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "调用算法接口读取文件Chunk失败！");
        }
        PageResult<Chunk> result = PageResult.of(chunks,total);
        return result;
    }

    @Override
    public void updateDocChunk(UpdateDocChunkReq updateDocChunkReq) {
        OkHttpClient client = OkHttpUtils.getOkHttpClient();
        Map<String,Object> param = new HashMap<>();
        param.put("user_id","xxy");
        param.put("doc_id",updateDocChunkReq.getChunkId());
        param.put("update_content",updateDocChunkReq.getHeader()+updateDocChunkReq.getContent());
        Request request = OkHttpUtils.getJsonPostRequest(modelUrl+"/api/local_doc_qa/update_chunks", param);
        Response response = null;
        try {
            response = client.newCall(request).execute();
            if(response.code()!=200){
                log.error("调用算法接口读取文件Chunk失败！"+response.body().string());
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "调用算法接口文件文件Chunk出错！");
            }
            String str = response.body().string();
            log.info("修改文件Chunk，算法接口返回内容："+str);
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(str);
            cn.hutool.json.JSONArray failedUpdates = jsonObject.getJSONObject("data").getJSONArray("failed_updates");
            for (int i = 0; i < failedUpdates.size(); i++) {
                cn.hutool.json.JSONObject update = failedUpdates.getJSONObject(i);
                if (update.getStr("doc_id").equals(updateDocChunkReq.getChunkId())) {
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "调用算法接口修改文件Chunk失败！"+update.getStr("reason"));
                }
            }
        } catch (IOException e) {
            log.error("调用算法接口修改文件Chunk失败！",e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "调用算法接口修改文件Chunk失败！");
        }
    }

    @Override
    public ResponseEntity<ByteArrayResource> previewById(Long fileId) {
        FileModel file = fileRepository.findById(fileId).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "附件不存在")
        );
        try {
            return fileStorageServiceFactory.getFileStorageService().previewFile(file.getHdfsPath());
        } catch (IOException e) {
            log.error("预览文件失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, "获取文件预览信息失败");
        }
    }



    private String getBase64(String url) {
        StringBuilder output = new StringBuilder();
        Process exec = null;
        try {
            String command = "node " + dir + " --url=" + url;
            log.info("执行命令");
            exec = Runtime.getRuntime().exec(command);
            // 读取命令执行后的输出
            try (InputStream inputStream = exec.getInputStream();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append(System.lineSeparator());  // 拼接所有行
                }
            }
            if (output.length() > 0) {
                if(isBase64(output.toString().trim())){
                    log.info("是base64");
                    return output.toString().trim();
                }else {
                    throw ServiceExceptionUtil.exception(
                            GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,
                            "调用服务获取pdfTXT失败");
                }
            } else {
                // 读取命令执行后的错误报告
                try (InputStream errorStream = exec.getErrorStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(errorStream))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append(System.lineSeparator());  // 拼接所有行
                    }
                    if (output.length() > 0) {
                        throw ServiceExceptionUtil.exception(
                                GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, output,
                                "调用前端服务获取pdfTXT失败");
                    }
                } catch (IOException e) {
                    log.error("调用前端服务获取pdfTXT失败", e);
                    throw ServiceExceptionUtil.exception(
                            GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e,
                            "调用前端服务获取pdfTXT失败");
                }
            }
        } catch (Exception e) {
            log.error("调用前端服务获取pdfTXT失败", e);
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e,
                    "提取文件信息失败");
        } finally {
            if (exec != null) {
                exec.destroy();
            }
        }
        return null;
    }

    public static boolean isBase64(String str) {
        // 1. 检查字符串是否符合 Base64 字符集
        if (!Pattern.compile("^[A-Za-z0-9+/=]+$").matcher(str).matches()) {
            return false;
        }
        // 2. 检查长度是否是 4 的倍数
        if (str.length() % 4 != 0) {
            return false;
        }
        // 3. 尝试解码 Base64 字符串
        try {
            // 尝试解码字符串，如果解码时没有异常，则认为它是有效的 Base64 编码
            byte[] decoded = Base64.getDecoder().decode(str);
            // 如果解码过程没有异常，说明是有效的 Base64 编码
            return true;
        } catch (IllegalArgumentException e) {
            // 解码失败，说明不是有效的 Base64 编码
            return false;
        }
    }
}