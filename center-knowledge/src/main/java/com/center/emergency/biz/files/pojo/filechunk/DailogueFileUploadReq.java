package com.center.emergency.biz.files.pojo.filechunk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;


@Data
public class DailogueFileUploadReq {

    @Schema(description = "上传的文件")
    private MultipartFile file;
    @Schema(description = "会话ID")
    private Long sessionId;
    @Schema(description = "文件名")
    private String fileName;
}
