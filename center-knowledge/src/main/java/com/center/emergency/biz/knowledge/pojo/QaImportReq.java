package com.center.emergency.biz.knowledge.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * QA导入请求类
 * <AUTHOR>
 */
@Data
public class QaImportReq {
    
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID")
    private Long kbId;
    
    @Schema(description = "文件ID，用于标识本次导入")
    private Long fileId;
    
    @Schema(description = "QA对列表")
    private List<QAPair> faqs;
    
    @Schema(description = "标签列表")
    private List<TagImportInfo> tags;
    
    @Data
    public static class TagImportInfo {
        private String id;
        private String name;
    }
} 