package com.center.emergency.biz.aisearch.common.persistence;

import com.center.emergency.biz.aisearch.common.enumeration.AiSearchEventType;
import lombok.Data;

/**
 * AI搜索解析后的事件对象
 */
@Data
public class AiSearchParsedEvent {
    // MESSAGE/SEARCH_RESULTS/COMPLETED/ERROR等
    private AiSearchEventType eventType;
    // 原始SSE事件名
    private String originalEventType;
    // 解析出的文本内容
    private String content;
    // 是否结束
    private boolean end;
    // 原始数据(可选, 用于排查问题)
    private String rawData;
    // 可选,返回给前端
    private String conversationId;
    // 会话ID
    private Long sessionId;
} 