package com.center.emergency.biz.aisearch.service;

import com.center.emergency.biz.chat.persistence.QChatAnswerEventModel;
import com.center.emergency.biz.chat.persistence.QChatAnswerModel;
import com.center.emergency.biz.chat.persistence.QChatQuestionModel;
import com.center.framework.common.enumerate.EventTypeEnum;
import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * AI搜索聊天工具类
 * 从 chat 模块复制必要的方法，确保 aisearch 模块的独立性
 */
@Service
@Slf4j
public class AiSearchChatUtils {
    
    /**
     * 从知识库构建知识库和文件ID（知识库对话场景使用）
     * 复制自 ChatResourceService.buildFromKnowledgeBase
     * 
     * @param knowledgeBaseId 知识库ID
     * @param kbIds 知识库ID集合（输出参数）
     * @param fileIds 文件ID列表（输出参数）
     * @param queryFactory JPA查询工厂
     */
    public void buildFromKnowledgeBase(Long knowledgeBaseId, Set<String> kbIds, List<String> fileIds, JPAQueryFactory queryFactory) {
        log.debug("从知识库构建资源范围: knowledgeBaseId={}", knowledgeBaseId);
        
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeBaseModel.id.eq(knowledgeBaseId));
        builder.and(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId));

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qKnowledgeBaseModel)
                .leftJoin(qKnowledgeModel).on(qKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .where(qKnowledgeBaseModel.id.eq(knowledgeBaseId))
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }
        
        log.debug("知识库资源范围构建完成: kbIds.size={}, fileIds.size={}", kbIds.size(), fileIds.size());
    }
    
    /**
     * 从知识库获取标签信息（知识库对话场景使用）
     * 复制自 ChatTagService.getTagsFromKnowledgeBase
     * 
     * @param knowledgeBaseId 知识库ID
     * @param queryFactory JPA查询工厂
     * @return 标签信息列表，包含标签ID和名称的映射
     */
    public List<Map<String, String>> getTagsFromKnowledgeBase(Long knowledgeBaseId, JPAQueryFactory queryFactory) {
        log.debug("从知识库获取标签信息: knowledgeBaseId={}", knowledgeBaseId);
        
        Map<String, String> map = new HashMap<>();
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeModel.kbId.eq(knowledgeBaseId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qKnowledgeModel.fileId.eq(qFileModel.id));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
                
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            map.put(String.valueOf(tuple.get(qTagModel.id)), tuple.get(qTagModel.tagName));
        }
        
        List<Map<String, String>> result = new ArrayList<>();
        result.add(map);
        
        log.debug("知识库标签信息获取完成: 共{}个标签", map.size());
        return result;
    }
    
    /**
     * 获取聊天历史记录（新格式）
     *
     * @param sessionId 会话ID
     * @param curQuestionId 当前问题ID（排除此问题）
     * @param isSimulate 是否模拟模式
     *                  - TRUE: 模拟模式，返回空历史（不影响数据库）
     *                  - FALSE: 正常模式，返回实际历史记录
     * @param queryFactory JPA查询工厂
     * @return 历史对话记录列表，格式：[{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
     */
    public List<Map<String, Object>> getHistory(Long sessionId, Long curQuestionId, Boolean isSimulate, JPAQueryFactory queryFactory) {
        log.debug("获取聊天历史记录: sessionId={}, curQuestionId={}, isSimulate={}", sessionId, curQuestionId, isSimulate);
        
        List<Map<String, Object>> result = new ArrayList<>();

        // 模拟模式下直接返回空历史
        if (isSimulate) {
            log.debug("模拟模式，返回空历史记录");
            return result;
        }
        
        // 查询问题和回答记录，按时间正序排列（从早到晚）
        QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatQuestionModel.sessionId.eq(sessionId));
        builder.and(qChatQuestionModel.id.ne(curQuestionId));
        builder.and(qChatAnswerModel.questionId.eq(qChatQuestionModel.id));
        builder.and(qChatAnswerModel.content.isNotEmpty());

        // 联合查询问题和回答，按时间正序排列
        List<Tuple> qaList = queryFactory.select(
                        qChatQuestionModel.id,
                        qChatQuestionModel.content,
                        qChatAnswerModel.id.as("answerId"),
                        qChatQuestionModel.createTime)
                .from(qChatQuestionModel)
                .join(qChatAnswerModel).on(qChatAnswerModel.questionId.eq(qChatQuestionModel.id))
                .where(builder)
                .orderBy(qChatQuestionModel.createTime.asc())  // 改为正序：从早到晚
                .fetch();

        // 构建新格式的历史记录
        for (Tuple tuple : qaList) {
            Long questionId = tuple.get(qChatQuestionModel.id);
            String questionContent = tuple.get(qChatQuestionModel.content);
            Long answerId = tuple.get(qChatAnswerModel.id.as("answerId"));

            // 添加用户消息
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", questionContent);
            result.add(userMessage);

            // 从事件表获取MESSAGE类型事件并拼接
            String assistantContent = getAssistantContentFromEvents(answerId, queryFactory);

            // 添加助手消息
            Map<String, Object> assistantMessage = new HashMap<>();
            assistantMessage.put("role", "assistant");
            assistantMessage.put("content", assistantContent);
            result.add(assistantMessage);
        }
        
        log.debug("聊天历史记录获取完成: 共{}条记录", result.size());
        return result;
    }

    /**
     * 从事件表获取助手回复内容
     * 只获取MESSAGE类型的事件，用两个回车拼接
     *
     * @param answerId 回答ID
     * @param queryFactory JPA查询工厂
     * @return 拼接后的助手回复内容
     */
    private String getAssistantContentFromEvents(Long answerId, JPAQueryFactory queryFactory) {
        QChatAnswerEventModel qEventModel = QChatAnswerEventModel.chatAnswerEventModel;

        // 查询MESSAGE类型的事件，按顺序排列
        // 使用EventTypeEnum.MESSAGE.getValue()确保类型匹配
        List<Tuple> events = queryFactory.select(qEventModel.eventContent, qEventModel.sequenceOrder)
                .from(qEventModel)
                .where(qEventModel.answerId.eq(answerId)
                        .and(qEventModel.eventType.eq(com.center.framework.common.enumerate.EventTypeEnum.MESSAGE.getValue()))
                        .and(qEventModel.includeInContext.isTrue()))
                .orderBy(qEventModel.sequenceOrder.asc())
                .fetch();

        // 用两个回车拼接MESSAGE事件内容
        StringBuilder contentBuilder = new StringBuilder();
        for (int i = 0; i < events.size(); i++) {
            Tuple event = events.get(i);
            String eventContent = event.get(qEventModel.eventContent);

            if (eventContent != null && !eventContent.trim().isEmpty()) {
                if (i > 0) {
                    contentBuilder.append("\n\n");  // 两个回车分隔
                }
                // 过滤思考标签
                contentBuilder.append(removeThinkTags(eventContent));
            }
        }

        return contentBuilder.toString();
    }

    /**
     * 移除思考标签 <think>...</think> 中的内容
     * 复制自 ChatHistoryService.removeThinkTags
     *
     * @param content 原始内容
     * @return 移除思考标签后的内容
     */
    private String removeThinkTags(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 使用正则表达式移除 <think>...</think> 标签及其内容
        // (?s) 表示 DOTALL 模式，让 . 匹配换行符
        // .*? 表示非贪婪匹配
        String result = content.replaceAll("(?s)<think>.*?</think>", "");

        // 清理可能产生的多余空行
        result = result.replaceAll("\n\n+", "\n\n");

        return result;
    }
}
