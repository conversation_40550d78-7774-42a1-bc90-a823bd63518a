package com.center.emergency.biz.knowledgebase.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class KnowledgeBaseDetail extends KnowledgeBaseBase{

    @Schema(description = "知识库ID")
    private Long id;

    @Schema(description = "部门名称")
    private String departName;

    @Schema(description = "返回的路径")
    private String pathName;

    @JsonIgnore
    private Long tenantId;

}

