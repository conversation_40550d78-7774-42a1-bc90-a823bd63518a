package com.center.emergency.biz.knowledge.pojo;

import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotNull;

/**
 * 知识更新请求类，继承 KnowledgeBase，增加 id 字段
 * <AUTHOR>
 */
@Data
public class KnowledgeUpdateStatusReq {

    @NotNull(message = "知识ID不能为空")
    @Schema(description = "知识ID")
    private Long id;

    @Schema(description = "知识状态")
    @Enumerated(EnumType.STRING)
    private KnowledgeStatusEnum status;
}