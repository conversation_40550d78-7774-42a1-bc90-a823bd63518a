package com.center.emergency.biz.approval;

import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.PageResult;
import com.center.emergency.biz.approval.pojo.RecordView;
import com.center.emergency.biz.approval.pojo.RefuseReq;
import com.center.emergency.biz.approval.service.RecordService;
import com.center.emergency.biz.knowledge.pojo.KnowledgePageReq;
import com.center.emergency.biz.knowledge.pojo.KnowledgePageView;
import com.center.emergency.biz.knowledge.service.KnowledgeService;
import com.center.framework.web.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "知识审批")
@RequestMapping("/approval")
@Validated
@RestController
public class ApprovalController {
    @Resource
    private RecordService recordService;
    @Resource
    private KnowledgeService knowledgeService;

    @GetMapping("/select_records")
    @Operation(summary = "查询流程信息")
    @Parameter(description = "问题事件id")
    @EnumConvertPoint
    public CommonResult<List<RecordView>> selectRecords(@RequestParam @Valid Long knowledgeId) {
        return CommonResult.success(recordService.getRecordList(knowledgeId));
    }

    @GetMapping("/select_by_content")
    @Operation(summary = "条件查询审批列表")
    @Parameter(description = "分页查询信息")
    public CommonResult<PageResult<KnowledgePageView>> selectByContent(@Valid KnowledgePageReq knowledgeReq) {
        return CommonResult.success(knowledgeService.getKnowledgeList(knowledgeReq));
    }

    @PostMapping("/pass/{id}")
    @Operation(summary = "审批通过")
    @Parameter(description = "知识id")
    public CommonResult<String> knowledgePass(@PathVariable @Valid Long id) {
        recordService.pass(id);
        return CommonResult.successWithMessageOnly("审批通过成功");
    }

    @PostMapping("/refuse")
    @Operation(summary = "审批拒绝")
    @Parameter(description = "知识id和拒绝理由")
    public CommonResult<String> knowledgePass(@RequestBody @Valid RefuseReq refuseReq) {
        recordService.refuse(refuseReq.getId(), refuseReq.getRefuseReason());
        return CommonResult.successWithMessageOnly("审批拒绝成功");
    }
}
