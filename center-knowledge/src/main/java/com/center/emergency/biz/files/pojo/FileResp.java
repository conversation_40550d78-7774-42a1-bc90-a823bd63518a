package com.center.emergency.biz.files.pojo;


import com.center.emergency.biz.tag.pojo.TagFileRsp;
import com.center.emergency.common.enumeration.FileStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件响应类
 * <AUTHOR>
 */
@Data
public class FileResp {

    /**
     * 文件ID
     */
    private Long id;

    /**
     * 知识库ID
     */
    private Long kbId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 原始文件名称
     */
    private String originalName;

    /**
     * HDFS路径
     */
    private String hdfsPath;
    /**
     * PDF路径
     */
    private String pdfHdfsPath;

    private String pdfPreviewPath;

    /**
     * 文件状态
     */
    private FileStatusEnum status;

    @EnumConvert(value = FileStatusEnum.class,srcFieldName = "status")
    private String statusName;

    /**
     * 模型文件库的文件ID
     */
    private Long aiFilebFileId;

    // 操作人名称
    private String operator;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private List<TagFileRsp> tags;
}