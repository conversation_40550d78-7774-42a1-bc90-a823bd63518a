package com.center.emergency.biz.approval.persistence;

import com.center.emergency.biz.approval.pojo.RecordView;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecordRepository extends JpaRepository<RecordModel, Long>,
        QuerydslPredicateExecutor<RecordModel> {
    List<RecordView> findByKnowledgeId(Long knowledgeId);

    void deleteByKnowledgeIdIn(List<Long> knowledgeIds);
}
