package com.center.emergency.biz.modelgroup.persistence;

import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "model_group")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ModelGroup extends BaseTenantModel {

    @Column(name = "group_name", nullable = false, length = 50)
    private String groupName;

    @Column(name = "source_type")
    @Enumerated(value =EnumType.STRING)
    private SourceTypeEnum sourceType;
}