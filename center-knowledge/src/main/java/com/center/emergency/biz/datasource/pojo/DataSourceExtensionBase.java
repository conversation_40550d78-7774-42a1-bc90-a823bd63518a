package com.center.emergency.biz.datasource.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "数据库扩展信息")
public class DataSourceExtensionBase extends DataBaseInfo{

    @Schema(description = "数据库基础表ID")
    private Long datasourceId;

    @Schema(description = "表名称")
    @Size(message = "表名称长度不能超过50。",max = 50)
    private String tableName;

    @Schema(description = "表结构说明")
    @Size(message = "表结构说明长度不能超过1000。",max = 1000)
    private String tableDescription;
}
