package com.center.emergency.biz.tag.service;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.files.persistence.FileTagRepository;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.biz.tag.persistence.TagModel;
import com.center.emergency.biz.tag.persistence.TagRepository;
import com.center.emergency.biz.tag.pojo.*;
import com.center.emergency.common.enumeration.TagTypeEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.knowledge.KnowledgeApiTool;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: 标签服务层实现类
 * @date 2024/10/16 18:09
 */
@Slf4j
@Service
public class TagServiceImpl implements TagService {
    @Resource
    private TagRepository tagRepository;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private FileTagRepository fileTagRepository;

    @Value("${center.superadmin-id}")
    private Long superAdminId;

    @Value("${center.tenant-id}")
    private Long superTenantId;

    @Value("${python.api-base-url}")
    private String pythonApiBaseUrl;
    @Autowired
    private JPAQueryFactory queryFactory;

    @Override
    @Transactional
    public void createTag(String tagName) {
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        TagModel tagModel = new TagModel();

        // 1.检查标签名称是否重复
        // 1.1 如果是系统管理员，则标签与所有标签判重
        if (userId.equals(superAdminId)) {
            if (tagRepository.existsByTagName(tagName)) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "标签名称重复");
            }
            tagModel.setTagType(TagTypeEnum.SYSTEM);
        } else {  // 1.2 不是系统管理员，则标签与企业标签和系统判重(与系统标签判重只能使用标签类型)
            List<TagModel> tagList = tagRepository.findByTagName(tagName);
            for(TagModel model : tagList) {
                if (model.getTagType().equals(TagTypeEnum.SYSTEM) || model.getTenantId().equals(tenantId)) {
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "标签名称重复");
                }
            }
            tagModel.setTagType(TagTypeEnum.CUSTOM);
        }

        // 2.设置标签参数
        tagModel.setTagName(tagName);

        // 3. 保存Tag实体
        tagRepository.save(tagModel);
    }


    @Override
    public TagGetResp getTag(Long id) {
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        TagGetResp tagGetResp = new TagGetResp();
        // 1. 先判断标签是否存在
        Optional<TagModel> tag = tagRepository.findByIdAndTenantId(id, tenantId);
        if (tag.isPresent()) {
            tagGetResp.setTagName(tag.get().getTagName());
            tagGetResp.setId(tag.get().getId());
            return tagGetResp;
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "标签不存在或无权限");
        }
    }

    @Override
    @Transactional
    public void saveTag(TagSaveReq req) {
        // 1. 先根据标签Id判断该标签是否存在
        Long tagId = req.getId();
        tagRepository.findById(tagId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "标签不存在"));

        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        Optional<TagModel> tag = tagRepository.findByIdAndTenantId(tagId, tenantId);
        if (!tag.isPresent()) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "无更新标签权限");
        }

        // 2.标签与tenantId匹配，则判断是系统标签还是企业标签
        String tagName = tag.get().getTagName();

        // 3.判断名称是否发生变化，如果没未改动则不判重
        if (!tagName.equals(req.getTagName())) {
            if (userId.equals(superAdminId)) {  //3.1 发生变化且是系统标签
                if (tagRepository.existsByTagName(req.getTagName())) {
                    throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "标签名称重复");
                }
            }else {    // 3.1 发生变化且是企业标签
                List<TagModel> tagList = tagRepository.findByTagName(req.getTagName());
                for(TagModel tagModel : tagList) {
                    if (tagModel.getTagType().equals(TagTypeEnum.SYSTEM) || tagModel.getTenantId().equals(tenantId)) {
                        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "标签名称重复");
                    }
                }
            }

            // 3.2 更新数据库标签数据
            TagModel tagModel = OrikaUtils.convert(req, TagModel.class);

            //4. 判断是否需要调用算法接口进行标签更新，存在关联关系才调用算法接口更新
            if (fileTagRepository.existsByTagId(tagId)){

                // 4.1 更新算法数据库标签数据
                List<Map<String, String>> newTagList = new ArrayList<>();
                Map<String, String> newTagMap = new HashMap<>();
                newTagMap.put(Long.toString(tagId),req.getTagName());
                newTagList.add(newTagMap);

                updateTag(tagId,tagName,newTagList);
            }
            // 4.2 保存标签更新后的数据
            tagRepository.save(tagModel);
        }

    }

    @Override
    @Transactional
    public void deleteTag(Long id) {
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        String tagName = tagRepository.findByIdAndTenantId(id, tenantId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "标签不存在或无权限")).getTagName();

        // 1. 删除数据库数据
        tagRepository.deleteById(id);

        //2. 判断是否需要调用算法接口进行标签删除，存在关联关系才调用算法接口删除
        if (fileTagRepository.existsByTagId(id)){
            // 2.1 删除算法数据库标签数据
            List<Map<String,String>> newTagList = new ArrayList<>();
            updateTag(id,tagName,newTagList);
            fileTagRepository.deleteByTagId(id);
        }
    }

    @Async
    public void updateTag(Long id, String oldTagName,List<Map<String, String>> newTagList) {

        // 1. 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("user_id", "zyx");

        // 2. 构建原标签列表
        List<Map<String, String>> oldTagList = new ArrayList<>();
        Map<String, String> oldTagMap = new HashMap<>();
        oldTagMap.put(Long.toString(id),oldTagName);
        oldTagList.add(oldTagMap);

        requestBody.put("tag", oldTagList);

        // 2.1 构建新标签列表
        requestBody.put("new_tag", newTagList);

        try {
            // 3. 调用 Python API
            JSONObject responseObj = KnowledgeApiTool.post(pythonApiBaseUrl+"/api/local_doc_qa/update_tags_by_tag", requestBody);
            log.info("调用 Python API 更新标签返回结果: {}", responseObj);

            // 4. 检查返回状态
            if (!"200".equals(responseObj.getString("code"))) {
                log.error("Python API 更新标签失败，错误信息: {}", responseObj.getString("msg"));
            }
        } catch (Exception e) {
            log.error("调用 Python API 更新标签失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取当前租户的自定义标签和所有系统标签
     *
     * @param tenantId 租户ID，用于查询该租户下的自定义标签
     * @return 返回一个包含自定义标签和系统标签的列表
     */
    @Override
    public List<TagResp> getTenantAndSystemTags(Long tenantId) {
        // 1. 查询当前租户的自定义标签
        List<TagModel> tenantTags = tagRepository.findByTenantId(tenantId);

        // 2. 查询所有系统标签
        List<TagModel> systemTags = tagRepository.findByTagType(TagTypeEnum.SYSTEM);

        // 3. 合并自定义标签和系统标签，并使用去重
        List<TagModel> allTags = Stream.concat(tenantTags.stream(), systemTags.stream())
                .collect(Collectors.toMap(TagModel::getId, tag -> tag, (tag1, tag2) -> tag1))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 4. 将实体转换为响应对象
        return allTags.stream()
                .map(tag -> OrikaUtils.convert(tag, TagResp.class))
                .collect(Collectors.toList());
    }


    @Override
    public PageResult<TagPageResp> getPageTags(TagPageReq pageReq) {
        Pageable pageable = PageRequest.of(pageReq.getPageNo() - 1, pageReq.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();

        QUserModel qUserModel = QUserModel.userModel;
        QTagModel qTagModel = QTagModel.tagModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;

        // 1. 构建查询条件
        BooleanBuilder whereBuilder = new BooleanBuilder();
        if (userId.equals(superAdminId)) {
            whereBuilder.and(qTagModel.tagType.eq(TagTypeEnum.SYSTEM)); // 超管仅查询系统标签
        } else {
            whereBuilder.and(
                    qTagModel.tagType.eq(TagTypeEnum.SYSTEM)
                            .or(qTagModel.tenantId.eq(tenantId)) // 系统标签和当前租户标签
            );
        }

        if (StrUtil.isNotEmpty(pageReq.getTagName())) {
            whereBuilder.and(qTagModel.tagName.contains(pageReq.getTagName())); // 模糊查询
        }

        // 2. 构建 JPQL 查询
        JPQLQuery<TagPageResp> jpqlQuery = jpaQueryFactory.select(Projections.bean(
                        TagPageResp.class,
                        qTagModel.id,
                        qTagModel.tagName,
                        qFileTagModel.tagId.count().as("fileTagCount"), // 统计文件标签数量
                        qTagModel.createTime,
                        qTagModel.tagType,
                        qUserModel.displayName))
                .from(qTagModel)
                .leftJoin(qFileTagModel).on(
                        qTagModel.id.eq(qFileTagModel.tagId)
                                .and(qFileTagModel.tenantId.eq(tenantId)) // 仅统计当前租户的文件标签
                )
                .join(qUserModel).on(qTagModel.updaterId.eq(qUserModel.id))
                .where(whereBuilder)
                .groupBy(qTagModel.id)
                .orderBy(
                        new CaseBuilder()
                                .when(qTagModel.tagType.eq(TagTypeEnum.CUSTOM)).then(1)
                                .otherwise(2).asc(),
                        qTagModel.createTime.desc()
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());

        // 3. 执行查询并返回结果
        Long total = jpqlQuery.fetchCount();
        List<TagPageResp> resultList = jpqlQuery.fetch();

        return PageResult.of(resultList, total);
    }

    public List<TagFileRsp> getFileTagsByFileId(Long fileId) {
        QFileTagModel fileTagModel = QFileTagModel.fileTagModel;
        QTagModel tagModel = QTagModel.tagModel;

        // 查询文件的标签信息
        List<TagModel> tagResults = queryFactory.select(tagModel)
                .from(fileTagModel)
                .leftJoin(tagModel).on(fileTagModel.tagId.eq(tagModel.id))
                .where(fileTagModel.fileId.eq(fileId))
                .fetch();

        // 转换为 TagFileRsp 列表
        return tagResults.stream()
                .map(tag -> OrikaUtils.convert(tag, TagFileRsp.class))
                .collect(Collectors.toList());
    }
}
