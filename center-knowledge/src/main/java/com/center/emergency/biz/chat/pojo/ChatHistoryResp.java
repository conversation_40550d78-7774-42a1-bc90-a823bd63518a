package com.center.emergency.biz.chat.pojo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ChatHistoryResp {

    @Schema(name = "问题对象")
    private ChatQuestionBase chatQuestionBase;

    @Schema(name = "回答对象")
    private ChatAnswerBase chatAnswerBase;

    @Schema(name = "回答事件列表", description = "按sequence_order排序的完整事件流程")
    private List<ChatAnswerEventResp> answerEvents;
}
