package com.center.emergency.biz.chat.pojo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ChatHistoryResp {

    @Schema(description = "问题对象")
    private ChatQuestionBase chatQuestionBase;

    @Schema(description = "回答对象")
    private ChatAnswerBase chatAnswerBase;

    @Schema(description = "回答事件列表(按sequence_order排序的完整事件流程)")
    private List<ChatAnswerEventResp> answerEvents;
}
