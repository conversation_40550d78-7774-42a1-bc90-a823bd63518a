package com.center.emergency.biz.files.service;

import com.center.emergency.biz.files.persistence.FileModel;
import com.center.emergency.biz.files.persistence.FileRepository;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class FileComponent {

    @Value("${server.preview_by_url}")
    private String url;

    @Resource
    private FileRepository fileRepository;


    /**
     * 生成根据文件id预览文件的全链接
     * @param id
     * @return
     */
    public String previewUrlById(Long id) {
        FileModel file = fileRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "附件不存在")
        );
        String pdfPath = file.getPdfHdfsPath();
        if (pdfPath == null || pdfPath.trim().isEmpty()) {
            return "";
        }
        return previewUrlByPath(pdfPath);
    }

    /**
     * 生成根据文件id生成预览文件的全链接
     * Logo的图片并没有转成PDF，所以直接使用hdfsPath获取预览路径
     * @param id
     * @return
     */
    public String previewUrlByIdForLogo(Long id) {
        FileModel file = fileRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "附件不存在")
        );
        String pdfPath = file.getHdfsPath();
        if (pdfPath == null || pdfPath.trim().isEmpty()) {
            return "";
        }
        return previewUrlByPath(pdfPath);
    }

    /**
     * 生成根据文件路径预览文件的链接
     * @param path
     * @return
     */
    public String previewUrlByPath(String path) {
        StringBuilder urlBuilder = new StringBuilder(url);
        urlBuilder.append("?filePath=");
        urlBuilder.append(path);
        return urlBuilder.toString();
    }
}
