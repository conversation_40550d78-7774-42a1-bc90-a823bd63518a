package com.center.emergency.biz.chat.service;

import com.center.emergency.biz.chat.persistence.QChatAnswerEventModel;
import com.center.emergency.biz.chat.persistence.QChatAnswerModel;
import com.center.emergency.biz.chat.persistence.QChatQuestionModel;
import com.center.framework.common.enumerate.EventTypeEnum;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 聊天历史服务
 * 迁移自AgentChatUtils中的历史记录处理方法
 */
@Service
@Slf4j
public class ChatHistoryService {
    
    /**
     * 获取聊天历史记录（新格式）
     *
     * @param sessionId 会话ID
     * @param curQuestionId 当前问题ID（排除此问题）
     * @param isSimulate 是否模拟模式
     *                  - TRUE: 模拟模式，返回空历史（不影响数据库）
     *                  - FALSE: 正常模式，返回实际历史记录
     * @param queryFactory JPA查询工厂
     * @return 历史对话记录列表，格式：[{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
     */
    public List<Map<String, Object>> getHistory(Long sessionId, Long curQuestionId, Boolean isSimulate, JPAQueryFactory queryFactory) {
        log.debug("获取聊天历史记录: sessionId={}, curQuestionId={}, isSimulate={}", sessionId, curQuestionId, isSimulate);

        List<Map<String, Object>> result = new ArrayList<>();

        // 模拟模式下直接返回空历史
        if (isSimulate) {
            log.debug("模拟模式，返回空历史记录");
            return result;
        }

        // 查询问题和回答记录，按时间正序排列（从早到晚）
        QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatQuestionModel.sessionId.eq(sessionId));
        builder.and(qChatQuestionModel.id.ne(curQuestionId));
        builder.and(qChatAnswerModel.questionId.eq(qChatQuestionModel.id));
        builder.and(qChatAnswerModel.content.isNotEmpty());

        // 联合查询问题和回答，按时间正序排列
        List<Tuple> qaList = queryFactory.select(
                        qChatQuestionModel.id,
                        qChatQuestionModel.content,
                        qChatAnswerModel.id.as("answerId"),
                        qChatQuestionModel.createTime)
                .from(qChatQuestionModel)
                .join(qChatAnswerModel).on(qChatAnswerModel.questionId.eq(qChatQuestionModel.id))
                .where(builder)
                .orderBy(qChatQuestionModel.createTime.asc())  // 改为正序：从早到晚
                .fetch();

        // 构建新格式的历史记录
        for (Tuple tuple : qaList) {
            Long questionId = tuple.get(qChatQuestionModel.id);
            String questionContent = tuple.get(qChatQuestionModel.content);
            Long answerId = tuple.get(qChatAnswerModel.id.as("answerId"));

            // 添加用户消息
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", questionContent);
            result.add(userMessage);

            // 从事件表获取MESSAGE类型事件并拼接
            String assistantContent = getAssistantContentFromEvents(answerId, queryFactory);

            // 添加助手消息
            Map<String, Object> assistantMessage = new HashMap<>();
            assistantMessage.put("role", "assistant");
            assistantMessage.put("content", assistantContent);
            result.add(assistantMessage);
        }

        log.debug("聊天历史记录获取完成: 共{}条记录", result.size());
        return result;
    }
    
    /**
     * 从事件表获取助手回复内容
     * 只获取MESSAGE类型的事件，用两个回车拼接
     *
     * @param answerId 回答ID
     * @param queryFactory JPA查询工厂
     * @return 拼接后的助手回复内容
     */
    private String getAssistantContentFromEvents(Long answerId, JPAQueryFactory queryFactory) {
        QChatAnswerEventModel qEventModel = QChatAnswerEventModel.chatAnswerEventModel;

        // 查询MESSAGE类型的事件，按顺序排列
        // 使用EventTypeEnum.MESSAGE.getValue()确保类型匹配
        List<Tuple> events = queryFactory.select(qEventModel.eventContent, qEventModel.sequenceOrder)
                .from(qEventModel)
                .where(qEventModel.answerId.eq(answerId)
                        .and(qEventModel.eventType.eq(com.center.framework.common.enumerate.EventTypeEnum.MESSAGE.getValue()))
                        .and(qEventModel.includeInContext.isTrue()))
                .orderBy(qEventModel.sequenceOrder.asc())
                .fetch();

        // 用两个回车拼接MESSAGE事件内容
        StringBuilder contentBuilder = new StringBuilder();
        for (int i = 0; i < events.size(); i++) {
            Tuple event = events.get(i);
            String eventContent = event.get(qEventModel.eventContent);

            if (eventContent != null && !eventContent.trim().isEmpty()) {
                if (i > 0) {
                    contentBuilder.append("\n\n");  // 两个回车分隔
                }
                // 过滤思考标签
                contentBuilder.append(removeThinkTags(eventContent));
            }
        }

        return contentBuilder.toString();
    }

    /**
     * 移除思考标签 <think>...</think> 中的内容
     * 迁移自AgentChatUtils.removeThinkTags
     *
     * @param content 原始内容
     * @return 移除思考标签后的内容
     */
    public String removeThinkTags(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 使用正则表达式移除 <think>...</think> 标签及其内容
        // (?s) 表示 DOTALL 模式，让 . 匹配换行符
        // .*? 表示非贪婪匹配
        String result = content.replaceAll("(?s)<think>.*?</think>", "");

        // 清理可能产生的多余空行
        result = result.replaceAll("\n\n+", "\n\n");

        return result;
    }
}
