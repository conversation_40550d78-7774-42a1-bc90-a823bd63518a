package com.center.emergency.biz.knowledge.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class KnowledgePageView {

    @Schema(description = "知识ID")
    private Long id;

    @Schema(description = "知识名称")
    private String knowledgeName;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "申请人")
    private String displayName;
}
