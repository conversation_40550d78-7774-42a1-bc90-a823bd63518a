package com.center.emergency.biz.files.pojo;


import com.center.emergency.biz.tag.pojo.TagUpdateReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文件更新请求类，继承 FileBase，增加 id 字段
 * <AUTHOR>
 */
@Data
public class FileUpdateReq extends FileBase {

    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不能为空")
    private Long id;

    private List<TagUpdateReq> tagList;
    @NotNull
    private List<Long> tags;
}