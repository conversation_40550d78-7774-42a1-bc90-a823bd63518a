package com.center.emergency.biz.knowledge.converter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.knowledge.pojo.QaImportResultResp;
import com.center.emergency.biz.knowledge.pojo.QaPreviewResp;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * QA导入响应转换器
 * 将算法接口的复杂响应转换为简洁的前端友好响应
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class QaImportConverter {

    /**
     * 转换算法check_faqs_duplicates响应为预览响应
     */
    public QaPreviewResp convertToPreviewResp(JSONObject algorithmResp) {
        QaPreviewResp resp = new QaPreviewResp();
        
        String code = algorithmResp.getString("code");
        boolean isSuccess = "200".equals(code);
        resp.setSuccess(isSuccess);
        resp.setMessage(algorithmResp.getString("msg"));
        
        if (!isSuccess) {
            resp.setCanImport(false);
            return resp;
        }
        
        // 转换统计信息
        JSONObject stats = algorithmResp.getJSONObject("stats");
        if (stats != null) {
            QaPreviewResp.PreviewStats previewStats = new QaPreviewResp.PreviewStats();
            previewStats.setNewCount(stats.getInteger("new_count"));
            previewStats.setDuplicateCount(stats.getInteger("duplicate_count"));
            previewStats.setErrorCount(stats.getInteger("error_count"));
            previewStats.setTotalCount(stats.getInteger("total_count"));
            resp.setStats(previewStats);
            
            // 设置是否可导入（无错误即可导入）
            resp.setCanImport(previewStats.getErrorCount() == 0);
        }
        
        // 转换详情为统一的预览项目列表（关键优化点）
        JSONObject details = algorithmResp.getJSONObject("details");
        if (details != null) {
            List<QaPreviewResp.QaPreviewItem> allItems = new ArrayList<>();
            
            // 处理新增FAQ
            if (details.containsKey("new_faqs")) {
                JSONArray newFaqs = details.getJSONArray("new_faqs");
                List<QaPreviewResp.QaPreviewItem> newItems = newFaqs.stream()
                    .map(item -> convertToNewPreviewItem((JSONObject) item))
                    .collect(Collectors.toList());
                allItems.addAll(newItems);
            }

            // 处理重复FAQ
            if (details.containsKey("duplicate_faqs")) {
                JSONArray duplicateFaqs = details.getJSONArray("duplicate_faqs");
                List<QaPreviewResp.QaPreviewItem> duplicateItems = duplicateFaqs.stream()
                    .map(item -> convertToDuplicatePreviewItem((JSONObject) item))
                    .collect(Collectors.toList());
                allItems.addAll(duplicateItems);
            }

            // 处理错误FAQ
            if (details.containsKey("error_faqs")) {
                JSONArray errorFaqs = details.getJSONArray("error_faqs");
                List<QaPreviewResp.QaPreviewItem> errorItems = errorFaqs.stream()
                    .map(item -> convertToErrorPreviewItem((JSONObject) item))
                    .collect(Collectors.toList());
                allItems.addAll(errorItems);
            }
            
            // 按行号排序
            allItems.sort((a, b) -> Integer.compare(a.getRowNumber(), b.getRowNumber()));
            resp.setItems(allItems);
        }
        
        return resp;
    }

    /**
     * 转换算法upload_faqs响应为导入结果响应
     */
    public QaImportResultResp convertToImportResp(JSONObject algorithmResp) {
        QaImportResultResp resp = new QaImportResultResp();
        
        String code = algorithmResp.getString("code");
        boolean isSuccess = "200".equals(code);
        resp.setSuccess(isSuccess);
        resp.setMessage(algorithmResp.getString("msg"));
        resp.setStatus(isSuccess ? "SUCCESS" : "FAILED");
        
        // 转换统计信息
        JSONObject stats = algorithmResp.getJSONObject("stats");
        if (stats != null) {
            QaImportResultResp.ImportStats importStats = new QaImportResultResp.ImportStats();
            importStats.setNewCount(stats.getInteger("new_count"));
            importStats.setDuplicateCount(stats.getInteger("duplicate_count"));
            importStats.setErrorCount(stats.getInteger("error_count"));
            importStats.setTotalCount(stats.getInteger("total_count"));
            resp.setStats(importStats);
        }
        
        // 转换错误详情
        if (algorithmResp.containsKey("error_details")) {
            JSONArray errorDetails = algorithmResp.getJSONArray("error_details");
            if (errorDetails != null && !errorDetails.isEmpty()) {
                List<QaImportResultResp.ErrorDetail> errors = errorDetails.stream()
                    .map(item -> convertToErrorDetail((JSONObject) item))
                    .collect(Collectors.toList());
                resp.setErrorDetails(errors);
            }
        }
        
        return resp;
    }

    /**
     * 转换为新增预览项目
     */
    private QaPreviewResp.QaPreviewItem convertToNewPreviewItem(JSONObject item) {
        QaPreviewResp.QaPreviewItem previewItem = new QaPreviewResp.QaPreviewItem();
        previewItem.setRowNumber(item.getInteger("index"));
        previewItem.setQuestion(item.getString("question"));
        previewItem.setAnswer(item.getString("answer"));
        previewItem.setStatus("NEW");

        // 使用算法返回的reason，如果没有则使用默认描述
        String reason = item.getString("reason");
        previewItem.setStatusDesc(reason != null ? reason : "新FAQ，可以导入");

        return previewItem;
    }

    /**
     * 转换为重复预览项目
     */
    private QaPreviewResp.QaPreviewItem convertToDuplicatePreviewItem(JSONObject item) {
        QaPreviewResp.QaPreviewItem previewItem = new QaPreviewResp.QaPreviewItem();
        previewItem.setRowNumber(item.getInteger("index"));
        previewItem.setQuestion(item.getString("question"));
        previewItem.setAnswer(item.getString("answer"));
        previewItem.setStatus("DUPLICATE");

        // 使用算法返回的reason，如果没有则使用默认描述
        String reason = item.getString("reason");
        previewItem.setStatusDesc(reason != null ? reason : "与现有FAQ重复，将跳过");

        // 处理原始重复FAQ信息
        if (item.containsKey("original_faq") && item.get("original_faq") != null) {
            JSONObject originalFaq = item.getJSONObject("original_faq");
            if (originalFaq != null) {
                previewItem.setOriginalQuestion(originalFaq.getString("question"));
                previewItem.setOriginalAnswer(originalFaq.getString("answer"));
            }
        }
        
        // 如果算法API没有返回原始FAQ信息，但是是完全重复的情况
        // 可以将当前问题和答案作为原始信息（因为完全重复）
        if (previewItem.getOriginalQuestion() == null && previewItem.getOriginalAnswer() == null) {
            previewItem.setOriginalQuestion(previewItem.getQuestion());
            previewItem.setOriginalAnswer(previewItem.getAnswer());
        }

        return previewItem;
    }

    /**
     * 转换为错误预览项目
     */
    private QaPreviewResp.QaPreviewItem convertToErrorPreviewItem(JSONObject item) {
        QaPreviewResp.QaPreviewItem previewItem = new QaPreviewResp.QaPreviewItem();
        previewItem.setRowNumber(item.getInteger("index"));
        previewItem.setQuestion(item.getString("question"));
        previewItem.setAnswer(item.getString("answer"));
        previewItem.setStatus("ERROR");

        // 设置错误信息
        String error = item.getString("error");
        previewItem.setErrorMsg(error);
        previewItem.setStatusDesc("数据错误，无法导入：" + (error != null ? error : "未知错误"));

        return previewItem;
    }

    /**
     * 转换为错误详情
     */
    private QaImportResultResp.ErrorDetail convertToErrorDetail(JSONObject item) {
        QaImportResultResp.ErrorDetail errorDetail = new QaImportResultResp.ErrorDetail();
        errorDetail.setRowNumber(item.getInteger("index"));
        errorDetail.setQuestion(item.getString("question"));
        errorDetail.setErrorMessage(item.getString("error"));
        return errorDetail;
    }
}
