package com.center.emergency.biz.model.pojo;

import com.center.emergency.common.enumeration.ModelProviderEnum;
import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.*;

@Data
public class LargeModelCreateReq {
    @Schema(description = "服务商")
    private ModelProviderEnum provider;

    @Schema(description = "模型显示名称")
    @NotBlank(message = "模型显示名称不能为空")
    @Length(min = 1, max = 50, message = "模型名称长度必须在1-50字符之间")
    private String modelDisplayName;

    @Schema(description = "模型名称")
    @NotBlank(message = "模型名称不能为空")
    @Length(min = 1, max = 50, message = "模型名称长度必须在1-50字符之间")
    private String modelName;

    @Schema(description = "基础URL")
    @NotBlank(message = "Api Base不能为空")
    @Pattern(
            regexp = "^(http|https)://.+$",
            message = "Api Base不符合规范，请以http或https开头"
    )
    private String baseUrl;

    @Schema(description = "模型描述")
    @Length(max = 200, message = "模型描述最多200字符")
    private String modelDesc;

    @Schema(description = "API密钥")
    @NotBlank(message = "API密钥不能为空")
    private String apiKey;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "温度参数（生成随机性），取值范围：[0, 2]。值越大越随机，越小越稳定。建议范围 0.0 ~ 1.0")
    @Pattern(
            regexp = "^(0(\\.\\d{1,5})?|1(\\.\\d{1,5})?|2(\\.0{1,5})?)$",
            message = "当前温度参数值超出有效范围 [0.0 - 2.0)，控制输出的随机性，值越大创造性越强，值越小越稳定。请确保值在 0.0 - 2.0 之间"
    )
    private String temperature;

    @Schema(description = "Top P 参数（核采样），取值范围：(0, 1]。控制模型输出的多样性，值越接近 1 表示选择范围越大")
    @Pattern(
            regexp = "^$|^(0\\.(0*[1-9]\\d{0,4}|[1-9]\\d{0,4})|1(\\.0{1,5})?)$",
            message = "Top P 参数必须为大于 0 小于等于 1 的小数，最多保留 5 位小数"
    )
    private String topP;

    @Schema(description = "最大 tokens 数（模型最大回复长度），必须 ≥ 512。建议值为 4096。", example = "4096")
    @Pattern(
            regexp = "^$|^(512|51[3-9]|5[2-9]\\d|[6-9]\\d{2}|[1-9]\\d{3}|[12]\\d{4}|3[01]\\d{3}|32000)$",
            message = "控制模型输出的 Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。"
    )
    private String maxTokens;

    @Schema(description = "最大输入Input Context Length：默认16000，范围4096～128000 ",example = "16000")
    @Pattern(
            regexp = "^$|^(4096|409[7-9]|4[1-9]\\d{2}|[5-9]\\d{3}|[1-9]\\d{4}|1[01]\\d{4}|12[0-6]\\d{3}|127[0-9]{2}|128000)$",
            message = "控制模型输入的 Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。"
    )
    private String inputContextLength;


} 