package com.center.emergency.biz.model.persistence;

import com.center.emergency.common.enumeration.ModelProviderEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "large_model")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class LargeModel extends BaseTenantModel {



    @Column(name = "provider",nullable = false, length = 100)
    private ModelProviderEnum provider;

    @Column(name = "model_display_name", nullable = false, length = 100)
    private String modelDisplayName;

    @Column(name = "model_name", nullable = false, length = 100)
    private String modelName;

    @Column(name = "base_url", length = 255)
    private String baseUrl;

    @Column(name = "model_desc", length = 255)
    private String modelDesc;

    @Column(name = "api_key", nullable = false, length = 255)
    private String apiKey;

    @Column(name = "temperature")
    private Float temperature;

    @Column(name = "top_p")
    private Float topP;

    @Column(name = "status")
    @Enumerated(value = EnumType.STRING)
    private CommonStatusEnum status;

    @Column(name = "max_tokens")
    private Integer maxTokens;

    @Column(name = "input_context_length")
    private Integer inputContextLength;

    @Column(name = "source_type")
    @Enumerated(value =EnumType.STRING)
    private SourceTypeEnum sourceType;
}