package com.center.emergency.biz.files.persistence;


import com.center.framework.db.core.BaseTenantModel;
import lombok.Data;
import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 文件标签关联实体类
 */
@Data
@Entity
@Table(name = "center_file_tags")
public class FileTagModel extends BaseTenantModel {

    /**
     * 知识库ID
     */
    @Column(name = "kb_id", nullable = false)
    private Long kbId;

    /**
     * 文件ID
     */
    @Column(name = "file_id", nullable = false)
    private Long fileId;

    /**
     * 标签ID
     */
    @Column(name = "tag_id", nullable = false)
    private Long tagId;

}