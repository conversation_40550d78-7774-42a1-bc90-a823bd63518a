package com.center.emergency.biz.files.pojo;

import com.center.emergency.biz.tag.pojo.TagUpdateReq;
import com.center.emergency.common.enumeration.FileStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class FileUploadPythonReq {
    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不能为空")
    private Long id;

    private List<TagUpdateReq> tags;

    /**
     * 文件状态
     */
    @NotNull(message = "文件状态不能为空")
    @Schema(description = "文件状态")
    private FileStatusEnum status;
}
