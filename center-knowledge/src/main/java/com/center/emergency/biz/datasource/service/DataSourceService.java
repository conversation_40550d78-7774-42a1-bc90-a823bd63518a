package com.center.emergency.biz.datasource.service;

import com.center.emergency.biz.datasource.pojo.*;
import com.center.framework.web.pojo.PageResult;

import java.util.List;

public interface DataSourceService {

    void createSingleDataSource(SingleDataSourceCreateReq singleDataSourceCreateReq);

    void createMultipleDataSource(MultipleDataSourceCreateReq multipleDataSourceCreateReq);

    void updateSingleDataSource(SingleDataSourceUpdateReq  singleDataSourceUpdateReq);

    void updateMultipleDataSource(MultipleDataSourceUpdateReq multipleDataSourceUpdateReq);

    void deleteDataSource(Long id);

    PageResult<DataSourceSelectResp> selectDataSource(DataSourcePageReq dataSourcePageReq);

    List<DataSourceSelectResp> listDataSource();

    MultipleDataSourceResp getMultipleDataSourceById(Long id);

    SingleDataSourceResp getSingleDataSourceById(Long id);

    List<DataSourceExtensionBase> listAllTables(DataBaseInfo dataBaseInfo);

    void checkConnection(DataBaseInfo dataBaseInfo);

    TableDataResp selectData(DataSourceExtensionBase dataSourceExtensionBase);

    String getTableDDL(DataSourceExtensionBase dataSourceExtensionBase);


}
