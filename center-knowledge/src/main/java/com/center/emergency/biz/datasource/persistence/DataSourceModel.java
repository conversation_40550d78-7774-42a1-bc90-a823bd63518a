package com.center.emergency.biz.datasource.persistence;

import com.center.framework.common.enumerate.SingleAndMultipleEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "center_datasource")
public class DataSourceModel extends BaseTenantModel {

    @Column(name = "datasource_name", nullable = false, length = 100)
    private String datasourceName;

    @Column(name = "datasource_description", length = 200)
    private String datasourceDescription;

    @Column(name = "datasource_type", nullable = false, length = 100)
    @Enumerated(value = EnumType.STRING)
    private SingleAndMultipleEnum datasourceType;

    @Column(name = "relation", nullable = true, length = 1000)
    private String relation;

}
