package com.center.emergency.biz.chat.service;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;

/**
 * Chat模块API接口URL服务
 * 根据不同的答案策略返回对应的算法接口URL
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ChatApiUrlService {
    
    /**
     * 根据答案策略获取对应的API接口URL
     * 
     * @param answerStrategy 答案策略枚举
     * @return 对应的算法接口URL
     */
    String getApiUrl(AnswerStrategyEnum answerStrategy);
}
