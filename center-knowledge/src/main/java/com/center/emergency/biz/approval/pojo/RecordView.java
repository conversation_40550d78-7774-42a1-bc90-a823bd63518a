package com.center.emergency.biz.approval.pojo;

import com.center.emergency.common.enumeration.RecordStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RecordView {
    @Schema(description = "记录id")
    private Long id;

    @Schema(description = "操作人")
    private String displayName;

    @Schema(description = "操作结果")
    private RecordStatusEnum operationResult;

    @Schema(description = "操作结果名")
    @EnumConvert(value = RecordStatusEnum.class,srcFieldName = "operationResult")
    private String resultName;

    @Schema(description = "操作原因")
    private String reason;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;
}
