package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ChatSessionResp {

    @Schema(name = "SessionID")
    private Long id;

    @Schema(name = "历史对话标题")
    private String title;

    @Schema(name = "对话类型", example = "NORMAL")
    private ChatTypeEnum chatType;

    @EnumConvert(value = ChatTypeEnum.class, srcFieldName = "chatType")
    @Schema(name = "对话类型名称", example = "普通对话")
    private String chatTypeName;

    @Schema(name = "会话创建时间")
    private LocalDateTime createTime;

    @Schema(name = "最新活动时间（最新回答时间或会话创建时间）")
    private LocalDateTime lastActivityTime;
}
