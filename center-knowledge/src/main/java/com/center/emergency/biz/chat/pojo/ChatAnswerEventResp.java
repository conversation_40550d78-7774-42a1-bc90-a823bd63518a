package com.center.emergency.biz.chat.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 聊天回答事件响应对象
 */
@Data
public class ChatAnswerEventResp {

    @Schema(description = "事件ID")
    private Long id;

    @Schema(description = "回答ID")
    private Long answerId;

    @Schema(description = "事件类型")
    private String eventType;

    @Schema(description = "事件内容")
    private String eventContent;

    @Schema(description = "事件顺序")
    private Integer sequenceOrder;

    @Schema(description = "是否参与上下文")
    private Boolean includeInContext;

    @Schema(description = "事件状态")
    private String eventStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
