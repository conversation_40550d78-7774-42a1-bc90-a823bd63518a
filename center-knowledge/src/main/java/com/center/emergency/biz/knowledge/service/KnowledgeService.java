package com.center.emergency.biz.knowledge.service;


import com.center.emergency.biz.knowledge.pojo.*;
import com.center.framework.web.pojo.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface KnowledgeService {

    /**
     * 创建知识
     * @param req 知识创建请求
     */
    void createKnowledge(KnowledgeCreateReq req);

    /**
     * 更新知识
     * @param req 知识更新请求
     */
    void updateKnowledge(KnowledgeUpdateReq req);

    /**
     * 删除知识
     * @param id 知识ID
     */
    void deleteKnowledge(Long id);

    /**
     * 获取知识详情
     * @param id 知识ID
     * @return KnowledgeResp 知识详情
     */
    KnowledgeResp getKnowledgeById(Long id);

    /**
     * 查询审批列表信息
     * @param req 知识分页请求
     * @return PageResult<KnowledgePageView> 知识分页结果
     */
    PageResult<KnowledgePageView> getKnowledgeList(KnowledgePageReq req);

    /**
     * 提审知识
     * @param id-知识id
     */
    void submitKnowledge(Long id);

    void activeKnowledge(Long id);

    void inactiveKnowledge(Long id);
    /**
     * 创建一个没有文件的知识
     * @param req 知识创建请求对象，包含了创建知识所需的信息
     */
    void createKnowledgeNoFile(KnowledgeNoFileCreateReq req);
    /**
     * 根据知识库ID获取知识列表
     *
     * @param kbId 知识库ID，用于标识特定的知识库
     * @return KnowledgeResp对象列表，包含符合条件的知识信息
     */
    List<KnowledgeResp> getKnowledgeByKnowledgeBaseId(Long kbId);

    /**
     * 根据知识库ID获取知识列表
     *
     * @param knowledgeQueryPageReq@return KnowledgeResp对象列表，包含符合条件的知识信息
     */
    PageResult<KnowledgeResp> getKnowledgeByKnowledgeBaseId(KnowledgeQueryPageReq knowledgeQueryPageReq);

    void updateKnowledgeAndFileStatus(KnowledgeUpdateStatusReq knowledgeUpdateStatusReq);
    /**
     * 获取知识的详细信息包括索引和qa
     * 此方法根据查询请求获取知识的详细信息，包括索引和问题-答案对（qa）
     * 主要用于需要同时获取知识内容及其关联qa场景的情况下
     *
     * @param id Qa查询请求，包含查询知识所需的条件和参数
     * @return KnowledgeWithQaResp 包含知识详细信息和qa对的响应对象
     */
    KnowledgeWithQaResp getKnowledgeWithQa(Long id);
}