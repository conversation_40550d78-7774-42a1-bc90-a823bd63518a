package com.center.emergency.biz.model.pojo;

import com.center.emergency.biz.robot.pojo.RobotModelResp;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PageLargeModelWithRobotsResp extends LargeModelPageResp{
    private List<RobotModelResp> robots;

    @Schema(description = "大模型来源",example = "CUSTOM")
    private SourceTypeEnum sourceType;

    @EnumConvert(value = SourceTypeEnum.class,srcFieldName = "sourceType")
    @Schema(description = "模型来源",example = "自定义")
    private String sourceTypeName;

}
