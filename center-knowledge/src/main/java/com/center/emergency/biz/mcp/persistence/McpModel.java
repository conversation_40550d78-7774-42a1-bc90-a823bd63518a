package com.center.emergency.biz.mcp.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "mcp_table")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class McpModel extends BaseTenantModel {

    @Column(name = "mcp_name", nullable = false, length = 100)
    private String mcpName;

    @Column(name = "mcp_desc")
    private String mcpDesc;

    @Column(name = "install_method", nullable = false, length = 50)
    private String installMethod;

    @Column(name = "server_name", nullable = false)
    private String serverName;

    @Column(name = "server_url", nullable = false)
    private String serverUrl;

    @Column(name = "logo_id")
    private Long logoId;
}