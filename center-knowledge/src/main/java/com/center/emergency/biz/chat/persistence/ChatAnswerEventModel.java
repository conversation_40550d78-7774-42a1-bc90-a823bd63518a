package com.center.emergency.biz.chat.persistence;

import com.center.framework.db.annotation.NotIgnoreNullField;
import com.center.framework.db.core.BaseModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@QueryEntity
@Table(name = "center_chat_answer_event")
@NotIgnoreNullField
public class ChatAnswerEventModel extends BaseModel {

    @Schema(description = "回答ID")
    @Column(name = "answer_id", nullable = false)
    private Long answerId;

    @Schema(description = "事件类型")
    @Column(name = "event_type", nullable = false, length = 50)
    private String eventType;

    @Schema(description = "事件内容")
    @Column(name = "event_content", columnDefinition = "TEXT")
    private String eventContent;

    @Schema(description = "事件顺序")
    @Column(name = "sequence_order", nullable = false)
    private Integer sequenceOrder;

    @Schema(description = "是否参与上下文")
    @Column(name = "include_in_context", nullable = false)
    private Boolean includeInContext;

    @Schema(description = "事件状态")
    @Column(name = "event_status", length = 20)
    private String eventStatus;
} 