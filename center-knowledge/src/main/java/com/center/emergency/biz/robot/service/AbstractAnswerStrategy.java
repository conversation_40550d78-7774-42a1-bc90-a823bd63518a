package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.robot.persitence.RobotKBRepository;
import com.center.emergency.biz.robot.persitence.RobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.RobotModel;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;

@Slf4j
public abstract class AbstractAnswerStrategy implements AnswerStrategyService {

    @Resource
    private RobotKBRepository robotKBRepository;

    @Resource
    private ModelGroupService modelGroupService;

    @Resource
    protected JPAQueryFactory queryFactory;

    @Override
    public void createRobotAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy) {
        RobotKnowledgeModel robotKnowledgeModel = new RobotKnowledgeModel();
        robotKnowledgeModel.setRobotId(robotId);
        robotKnowledgeModel.setKbId(0L);
        robotKnowledgeModel.setAnswerStrategy(answerStrategy);
        robotKBRepository.save(robotKnowledgeModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRobotAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy, List<Long> answerStrategyIds) {
        robotKBRepository.deleteByRobotId(robotId);
        if (answerStrategyIds == null || answerStrategyIds.isEmpty()) {
//            如果没有选择任何任何策略，则创建一个空的关联记录
            createRobotAnswerStrategy(robotId, answerStrategy);
        }else {
            for (Long id : answerStrategyIds) {
                RobotKnowledgeModel robotKnowledgeModel = new RobotKnowledgeModel();
                robotKnowledgeModel.setRobotId(robotId);
                robotKnowledgeModel.setKbId(id);
                robotKnowledgeModel.setAnswerStrategy(answerStrategy);
                robotKBRepository.save(robotKnowledgeModel);
            }
        }
    }

    @Override
    public void deleteAllWithRobotId(Long robotId){
        robotKBRepository.deleteByRobotId(robotId);
    }



    /**
     * 核心抽象方法：子类必须实现策略特有的参数构建逻辑
     */
    @Override
    public abstract HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                                 Long modelGroupId, JPAQueryFactory jpaQueryFactory);

    /**
     * 模板方法：标准的参数构建流程
     * 子类可以重写此方法来自定义构建流程，但大多数情况下只需实现抽象方法即可
     */
    protected final HashMap<String, Object> buildParametersTemplate(ChatVO chatVO, Boolean isSimulate, 
                                                                   Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
        log.info("开始构建{}策略参数: robotId={}, question={}", getStrategyName(), chatVO.getRobotId(), chatVO.getQuestion());
        
        // 1. 构建基础通用参数
        HashMap<String, Object> param = buildCommonBaseParams(chatVO);
        
        // 2. 添加策略特有参数（由子类实现）
        addStrategySpecificParams(param, chatVO, jpaQueryFactory);
        
        // 3. 添加模型组配置
        addModelGroupParams(param, modelGroupId);
        
        // 4. 添加策略特有的机器人配置（可选，由子类决定是否重写）
        addStrategySpecificRobotConfig(param, chatVO.getRobotId(), jpaQueryFactory);
        
        log.info("{}策略参数构建完成", getStrategyName());
        return param;
    }

    /**
     * 抽象方法：添加策略特有参数
     */
    protected abstract void addStrategySpecificParams(HashMap<String, Object> param, ChatVO chatVO, JPAQueryFactory jpaQueryFactory);
    
    /**
     * 抽象方法：获取策略名称（用于日志）
     */
    protected abstract String getStrategyName();
    
    /**
     * 可选重写：添加策略特有的机器人配置
     * 默认实现为空，子类可以根据需要重写
     */
    protected void addStrategySpecificRobotConfig(HashMap<String, Object> param, Long robotId, JPAQueryFactory jpaQueryFactory) {
        // 默认空实现，子类按需重写
    }

    /**
     * 构建通用基础参数（所有策略都需要的参数）
     */
    protected final HashMap<String, Object> buildCommonBaseParams(ChatVO chatVO) {
        HashMap<String, Object> param = new HashMap<>();
        
        // 真正的通用基础参数
        param.put("user_token", String.valueOf(LoginContextHolder.getLoginUserId()));
        param.put("history", null); // 由调用方补充
        
        return param;
    }

    /**
     * 添加模型组配置参数
     */
    protected final void addModelGroupParams(HashMap<String, Object> param, Long modelGroupId) {
        if (modelGroupId != null) {
            List<Map<String, Object>> modelGroupConfig = modelGroupService.buildModelGroupConfig(modelGroupId);
            param.put("model_group", modelGroupConfig);
            log.debug("添加模型组配置: modelGroupId={}", modelGroupId);
        } else {
            // 使用系统默认模型配置
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            param.putAll(defaultModelConfig);
            log.debug("使用系统默认模型配置");
        }
    }

    /**
     * 获取机器人的模型组ID
     */
    protected final Long getRobotModelGroupId(Long robotId) {
        QRobotModel qRobotModel = QRobotModel.robotModel;
        
        Long modelGroupId = queryFactory.select(qRobotModel.modelGroupId)
                .from(qRobotModel)
                .where(qRobotModel.id.eq(robotId))
                .fetchOne();
                
        log.debug("查询机器人模型组ID: robotId={}, modelGroupId={}", robotId, modelGroupId);
        return modelGroupId;
    }

    /**
     * 通用的安全参数添加方法
     */
    protected final BiConsumer<String, Object> createSafeParamAdder(HashMap<String, Object> param) {
        return (key, value) -> {
            if (value != null) {
                param.put(key, value);
            }
        };
    }

    /**
     * 查询机器人配置信息
     */
    protected final RobotModel queryRobotModel(Long robotId, JPAQueryFactory jpaQueryFactory) {
        QRobotModel qRobotModel = QRobotModel.robotModel;
        
        RobotModel robotModel = jpaQueryFactory.selectFrom(qRobotModel)
                .where(qRobotModel.id.eq(robotId))
                .fetchOne();
                
        if (robotModel == null) {
            log.warn("机器人不存在: robotId={}", robotId);
        }
        
        return robotModel;
    }
}
