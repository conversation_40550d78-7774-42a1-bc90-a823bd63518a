package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.biz.model.persistence.QLargeModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.robot.persitence.RobotKBRepository;
import com.center.emergency.biz.robot.persitence.RobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.RobotModel;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.context.LoginContextHolder;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQueryFactory;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;

@Slf4j
public abstract class AbstractAnswerStrategy implements AnswerStrategyService {

    @Resource
    private RobotKBRepository robotKBRepository;

    @Resource
    private ModelGroupService modelGroupService;

    @Resource
    protected JPAQueryFactory queryFactory;

    @Override
    public void createRobotAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy) {
        RobotKnowledgeModel robotKnowledgeModel = new RobotKnowledgeModel();
        robotKnowledgeModel.setRobotId(robotId);
        robotKnowledgeModel.setKbId(0L);
        robotKnowledgeModel.setAnswerStrategy(answerStrategy);
        robotKBRepository.save(robotKnowledgeModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRobotAnswerStrategy(Long robotId, AnswerStrategyEnum answerStrategy, List<Long> answerStrategyIds) {
        robotKBRepository.deleteByRobotId(robotId);
        if (answerStrategyIds == null || answerStrategyIds.isEmpty()) {
//            如果没有选择任何任何策略，则创建一个空的关联记录
            createRobotAnswerStrategy(robotId, answerStrategy);
        }else {
            for (Long id : answerStrategyIds) {
                RobotKnowledgeModel robotKnowledgeModel = new RobotKnowledgeModel();
                robotKnowledgeModel.setRobotId(robotId);
                robotKnowledgeModel.setKbId(id);
                robotKnowledgeModel.setAnswerStrategy(answerStrategy);
                robotKBRepository.save(robotKnowledgeModel);
            }
        }
    }

    @Override
    public void deleteAllWithRobotId(Long robotId){
        robotKBRepository.deleteByRobotId(robotId);
    }

    /**
     * 抽象方法：子类必须实现策略特有的参数构建逻辑
     * 这是旧版本的参数构建接口，主要用于向后兼容
     */
    @Override
    public abstract HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                                           List<Map<String, String>> tags, Boolean isSimulate,
                                                           Long modelId, JPAQueryFactory jpaQueryFactory);

    /**
     * 抽象方法：子类必须实现策略自主收集资源并构建参数的逻辑
     * 这是推荐的参数构建方式，每个策略负责收集自己需要的资源
     */
    @Override
    public abstract HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                                 Long modelGroupId, JPAQueryFactory jpaQueryFactory);

    /**
     * 创建RobotModelDTO的Projection（消除重复代码）
     */
    private com.querydsl.core.types.Expression<RobotModelDTO> createRobotModelProjection() {
        QRobotModel qRobotModel = QRobotModel.robotModel;
        QLargeModel qLargeModel = QLargeModel.largeModel;

        return Projections.bean(
                RobotModelDTO.class,
                qLargeModel.baseUrl.as("apiBase"),
                qLargeModel.apiKey,
                qLargeModel.modelName.as("model"),
                qLargeModel.maxTokens.as("maxToken"),
                qLargeModel.inputContextLength,
                qRobotModel.searchMode,
                qRobotModel.answerMode,
                qRobotModel.similarityThreshold.as("scoreThreshold"),
                qLargeModel.temperature,
                qLargeModel.topP,
                qRobotModel.maxHits.as("topK"),
                qRobotModel.systemPrompt.as("customPrompt")
        );
    }

    /**
     * 查询机器人和模型配置
     */
    protected RobotModelDTO queryRobotModelConfig(Long robotId, Long modelId, JPAQueryFactory jpaQueryFactory) {
        QRobotModel qRobotModel = QRobotModel.robotModel;
        QLargeModel qLargeModel = QLargeModel.largeModel;

        if (modelId != null) {
            // 根据指定modelId查询
            return jpaQueryFactory.select(createRobotModelProjection())
                    .from(qRobotModel)
                    .leftJoin(qLargeModel).on(qRobotModel.modelGroupId.eq(qLargeModel.id))
                    .where(qRobotModel.id.eq(robotId))
                    .fetchOne();
        } else {
            // 使用系统默认模型
            BooleanBuilder builder = new BooleanBuilder();
            builder.and(qRobotModel.id.eq(robotId));
            builder.and(qLargeModel.sourceType.eq(SourceTypeEnum.SYSTEM));

            return jpaQueryFactory.select(createRobotModelProjection())
                    .from(qRobotModel, qLargeModel)
                    .where(builder)
                    .fetchOne();
        }
    }

    /**
     * 构建真正的通用基础参数（所有策略都需要的参数）
     * 只包含所有策略都必须的通用参数
     */
    protected HashMap<String, Object> buildCommonBaseParams(ChatVO chatVO) {
        HashMap<String, Object> param = new HashMap<>();

        // 真正的通用基础参数（所有策略都需要的参数）
        param.put("user_token", String.valueOf(LoginContextHolder.getLoginUserId()));
        param.put("history", null); // 由调用方补充

        return param;
    }

    /**
     * 通用的安全参数添加方法
     */
    private BiConsumer<String, Object> createSafeParamAdder(HashMap<String, Object> param) {
        return (key, value) -> {
            if (value != null) {
                param.put(key, value);
            }
        };
    }

    /**
     * 添加基础模型API配置参数（所有策略通用）
     * 只包含所有策略都需要的基础模型配置，不包含策略特有参数
     */
    protected void addBaseModelConfigParams(HashMap<String, Object> param, RobotModelDTO robotModelDTO) {
        BiConsumer<String, Object> putIfNotNull = createSafeParamAdder(param);

        // 基础模型API配置（所有策略通用）
        param.put("api_base", robotModelDTO.getApiBase());
        param.put("api_key", robotModelDTO.getApiKey());
        param.put("model", robotModelDTO.getModel());
        putIfNotNull.accept("max_token", robotModelDTO.getMaxToken());
        putIfNotNull.accept("api_context_length", robotModelDTO.getInputContextLength());
        putIfNotNull.accept("temperature", robotModelDTO.getTemperature());
        putIfNotNull.accept("top_p", robotModelDTO.getTopP());
    }

    /**
     * 添加模型配置参数（向后兼容方法）
     * @deprecated 建议使用 addBaseModelConfigParams() 替代，策略特有参数应在各自实现类中处理
     */
    @Deprecated
    protected void addModelConfigParams(HashMap<String, Object> param, RobotModelDTO robotModelDTO) {
        // 添加基础模型配置
        addBaseModelConfigParams(param, robotModelDTO);

        // 策略特有配置（为了向后兼容保留，但不推荐使用）
        BiConsumer<String, Object> putIfNotNull = createSafeParamAdder(param);
        if (robotModelDTO.getSearchMode() != null) {
            param.put("search_mode", Integer.valueOf(robotModelDTO.getSearchMode().getDescription()));
        }
        if (robotModelDTO.getAnswerMode() != null) {
            param.put("answer_mode", Integer.valueOf(robotModelDTO.getAnswerMode().getDescription()));
        }
        putIfNotNull.accept("top_k", robotModelDTO.getTopK());
        putIfNotNull.accept("custom_prompt", StringUtils.isNotBlank(robotModelDTO.getCustomPrompt()) ? robotModelDTO.getCustomPrompt() : null);
        putIfNotNull.accept("score_threshold", robotModelDTO.getScoreThreshold());
    }



    /**
     * 添加模型组配置参数
     */
    @Override
    public void addModelGroupParams(HashMap<String, Object> param, Long modelGroupId) {
        log.info("开始添加模型组配置参数，modelGroupId: {}", modelGroupId);

        Long finalModelGroupId = modelGroupId;

        // 1. 检查机器人配置的模型组是否可用
        if (modelGroupId == null) {
            log.warn("机器人模型组ID为空，尝试使用系统内置模型组");
            finalModelGroupId = modelGroupService.getSystemDefaultModelGroupId();
        } else if (!modelGroupService.isModelGroupExists(modelGroupId)) {
            log.warn("机器人模型组不存在: {}, 尝试使用系统内置模型组", modelGroupId);
            finalModelGroupId = modelGroupService.getSystemDefaultModelGroupId();
        } else {
            log.info("机器人模型组存在: {}, 开始构建模型组配置", modelGroupId);
        }

        // 2. 构建模型组配置
        List<Map<String, Object>> modelGroupConfig = null;
        if (finalModelGroupId != null) {
            modelGroupConfig = modelGroupService.buildModelGroupConfig(finalModelGroupId);
            log.info("模型组配置构建结果: modelGroupId={}, 配置数量={}", finalModelGroupId,
                    modelGroupConfig != null ? modelGroupConfig.size() : 0);
        }

        // 3. 根据算法文档要求，优先使用model_group格式
        if (modelGroupConfig != null && !modelGroupConfig.isEmpty()) {
            // 使用模型组配置（符合算法文档要求）
            param.put("model_group", modelGroupConfig);
            log.info("✅ 使用模型组配置，模型组ID: {}, 模型数量: {}", finalModelGroupId, modelGroupConfig.size());
        } else {
            // 最后的回退方案：使用系统默认单模型配置
            log.warn("⚠️ 无法获取任何模型组配置，回退到系统默认单模型配置");
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            if (!defaultModelConfig.isEmpty()) {
                param.putAll(defaultModelConfig);
                log.info("⚠️ 使用系统默认单模型配置: {}", defaultModelConfig);
            } else {
                log.error("❌ 无法获取任何模型配置，既没有模型组配置也没有系统默认配置");
            }
        }
    }
}
