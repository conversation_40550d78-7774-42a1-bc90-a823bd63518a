package com.center.emergency.biz.knowledge.service;

import com.center.emergency.biz.knowledge.pojo.QaConfirmImportReq;
import com.center.emergency.biz.knowledge.pojo.QaImportResultResp;
import com.center.emergency.biz.knowledge.pojo.QaPreviewResp;

import org.springframework.web.multipart.MultipartFile;

/**
 * QA导入服务接口
 * <AUTHOR>
 */
public interface QaImportService {

    /**
     * 解析Excel文件并预检查QA重复性
     * @param file Excel文件
     * @param kbId 知识库ID
     * @return 预检查结果
     */
    QaPreviewResp previewFromExcel(MultipartFile file, Long kbId);

    /**
     * 确认导入QA数据
     * @param req 确认导入请求
     * @return 导入结果
     */
    QaImportResultResp confirmImport(QaConfirmImportReq req);

    /**
     * 解析Excel文件并直接执行QA导入（保留原有功能）
     * @param file Excel文件
     * @param kbId 知识库ID
     * @return 导入结果
     */
    QaImportResultResp importFromExcel(MultipartFile file, Long kbId);

    /**
     * 下载QA导入模板
     * @return 模板文件字节数组
     */
    byte[] downloadTemplate();


}