package com.center.emergency.biz.aisearch.pojo;

import lombok.Getter;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI搜索模式枚举
 */
@Getter
@Schema(description = "AI搜索模式")
public enum SearchMode {
    /**
     * 全网搜索
     */
    @Schema(description = "全网搜索")
    GLOBAL(1, "全网搜索"),
    
    /**
     * 企业内部搜索
     */
    @Schema(description = "企业内部搜索")
    INTERNAL(2, "企业内部搜索");
    
    @Schema(description = "模式代码")
    private final Integer code;
    
    @Schema(description = "模式描述")
    private final String desc;
    
    SearchMode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据代码获取搜索模式
     *
     * @param code 模式代码
     * @return 搜索模式
     * @throws IllegalArgumentException 当代码无效时
     */
    public static SearchMode fromCode(Integer code) {
        for (SearchMode mode : values()) {
            if (mode.code.equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的搜索模式: " + code);
    }
} 