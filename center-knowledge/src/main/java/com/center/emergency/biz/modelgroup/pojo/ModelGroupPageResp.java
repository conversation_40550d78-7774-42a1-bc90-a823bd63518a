package com.center.emergency.biz.modelgroup.pojo;

import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
public class ModelGroupPageResp {

    @Schema(description = "模型组id")
    private Long id;

    @Schema(description = "模型组名称")
    private String groupName;

    @Schema(description = "模型组来源", example = "CUSTOM")
    private SourceTypeEnum sourceType;

    @EnumConvert(value = SourceTypeEnum.class, srcFieldName = "sourceType")
    @Schema(description = "模型组来源名称", example = "用户自定义")
    private String sourceTypeName;

    @Schema(description = "模型组关联大模型")
    private List<LargeModelInfoDTO> modelList;
}
