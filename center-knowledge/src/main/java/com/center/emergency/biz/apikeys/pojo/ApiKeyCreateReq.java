package com.center.emergency.biz.apikeys.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
public class ApiKeyCreateReq {

    @Schema(description = "ApiKey名称",example = "ApiKey名称")
    @NotBlank(message = "ApiKey名称不能为空")
    @Length(min = 1, max = 20, message = "ApiKey名称长度必须在1-20字符之间")
    private String apiKeyName;

    @Schema(description = "智能助手ID", example = "1914200736866541568")
    @NotNull(message = "智能助手ID不能为空")
    private Long robotId;

    @Schema(description = "密钥")
    @NotBlank(message = "ApiKey密钥不能为空")
    private String apiKey;

    @Schema(description = "开始日期",example = "2025-05-26 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "截止日期",example = "2025-05-28 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}

