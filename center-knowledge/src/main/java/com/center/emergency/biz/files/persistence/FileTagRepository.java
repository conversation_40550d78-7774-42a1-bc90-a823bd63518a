package com.center.emergency.biz.files.persistence;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 文件标签关联表的持久层接口
 */
@Repository
public interface FileTagRepository extends JpaRepository<FileTagModel, Long> {

    /**
     * 根据知识库ID删除所有关联的文件标签记录
     *
     * @param kbId 知识库ID
     */
    void deleteByKbId(Long kbId);

    /**
     * 根据文件ID删除所有关联的标签
     *
     * @param fileId 文件ID
     */
    void deleteByFileId(Long fileId);

    /**
     * 根据知识库ID查询文件标签关联关系
     *
     * @param kbId 知识库ID
     * @return 文件标签关联列表
     */
    List<FileTagModel> findByKbId(Long kbId);

    /**
     *
     * 根据标签Id删除文件-标签关联关系
     * @param tagId
     */
    void deleteByTagId(Long tagId);

    /**
     *
     * 根据标签Id判断是否存在文件-标签关联关系
     * @param tagId
     */
    boolean existsByTagId(Long tagId);

    List<Long> findFileIdByTagIdIn( List<Long> tagIds);
}