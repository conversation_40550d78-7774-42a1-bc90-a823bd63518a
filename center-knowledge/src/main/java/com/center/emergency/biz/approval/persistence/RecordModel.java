package com.center.emergency.biz.approval.persistence;

import com.center.emergency.common.enumeration.RecordStatusEnum;
import com.center.framework.db.core.BaseModel;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Entity
@QueryEntity
@Table(name = "center_knowledge_operation_logs")
public class RecordModel extends BaseTenantModel {
    @Schema(description = "需审批的知识id")
    @Column(name = "knowledge_id")
    private Long knowledgeId;

    @Schema(description = "操作时间")
    @Column(name = "operation_time")
    private LocalDateTime operationTime;

    @Schema(description = "操作结果")
    @Column(name = "operation_result")
    @Enumerated(value = EnumType.STRING)
    private RecordStatusEnum operationResult;

    @Schema(description = "操作原因")
    @Column(name = "reason")
    private String reason;

    @Schema(description = "备注")
    @Column(name = "remark")
    private String remark;
}
