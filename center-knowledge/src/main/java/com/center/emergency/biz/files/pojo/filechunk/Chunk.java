package com.center.emergency.biz.files.pojo.filechunk;

import lombok.Data;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class Chunk {

    private String page_content;
    private Metadata metadata;
    private String chunk_id;
    private String header;

    public String getPage_content() {
        String regex = "\\[headers\\]\\(.*?\\)\\n";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(page_content);

        // 替换匹配到的内容为空字符串
        return matcher.replaceAll("");
    }

    public String getHeader() {
        String regex = "\\[headers\\]\\(.*?\\)\\n";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建 matcher 对象
        Matcher matcher = pattern.matcher(page_content);

        // 检查是否匹配
        if (matcher.find()) {
            // 返回匹配到的内容
            return matcher.group(0); // 返回整个匹配的内容
        } else {
            // 如果没有匹配到，返回空字符串或抛出异常
            return "";
        }
    }
}
