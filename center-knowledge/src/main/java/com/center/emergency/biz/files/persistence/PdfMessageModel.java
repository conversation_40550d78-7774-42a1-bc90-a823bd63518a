package com.center.emergency.biz.files.persistence;


import com.center.emergency.common.enumeration.PdfStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@QueryEntity
@Table(name = "center_pdf_message")
public class PdfMessageModel extends BaseTenantModel {

    @Schema(description = "任务id")
    @Column(name = "job_id",nullable = false)
    private Long jobId;

    @Schema(description = "关键词")
    @Column(name = "keyword",nullable = false)
    private String key;

    @Schema(description = "查询结果")
    @Column(name = "value")
    @Lob
    private String value;

    @Schema(description = "开始位置")
    @Column(name = "start_id")
    private String startLocation;

    @Schema(description = "结束位置")
    @Column(name = "end_id")
    private String endLocation;

    @Schema(description = "类型")
    @Column(name = "type")
    private String type;
}
