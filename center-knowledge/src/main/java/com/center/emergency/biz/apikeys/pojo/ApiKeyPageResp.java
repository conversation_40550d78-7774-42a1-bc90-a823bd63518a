package com.center.emergency.biz.apikeys.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ApiKeyPageResp {
    @Schema(description = "ID",example = "ApiKey 的ID")
    private Long id;

    @Schema(description = "ApiKey名称",example = "ApiKey名称")
    private String apiKeyName;

    @Schema(description = "密钥")
    private String apiKey;

    @Schema(description = "开始日期",example = "2025-05-28 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "截止日期",example = "2025-05-28 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "最后使用日期",example = "2025-05-28 10:23:09")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUsedTime;
}
