package com.center.emergency.biz.apikeys.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class ValidateResp {
    @Schema(description = "验证结果是否通过", example = "true")
    private Boolean valid;

    @Schema(description = "配置信息")
    private Config config;

    @Data
    public static class Config {
        @Schema(description = "知识库ID列表")
        private Set<String> kb_ids;

        @Schema(description = "文件ID列表")
        private List<String> file_ids;

        @Schema(description = "是否开启流式模式", example = "1")
        private Integer streaming;

        @Schema(description = "历史记录（一般为空或结构化历史对话）")
        private List<Object> history;

        @Schema(description = "标签列表")
        private List<Long> tags_list;

        @Schema(description = "最大token数", example = "4096")
        private Integer max_token;

        @Schema(description = "搜索模式", example = "3")
        private String search_mode;

        @Schema(description = "得分阈值", example = "0.2")
        private BigDecimal score_threshold;

        @Schema(description = "是否重新排序", example = "1")
        private Integer rerank;

        @Schema(description = "是否仅需要搜索结果", example = "0")
        private Integer only_need_search_results;

        @Schema(description = "API支持的最大上下文长度", example = "16000")
        private Integer api_context_length;

        @Schema(description = "温度参数", example = "0.5")
        private Float temperature;

        @Schema(description = "top_p 采样参数", example = "0.99")
        private Float top_p;

        @Schema(description = "top_k 采样参数", example = "20")
        private Integer top_k;

        @Schema(description = "回答模式", example = "2")
        private String answer_mode;

        @Schema(description = "是否启用查询拆解", example = "1")
        private Integer query_decompose;

        @Schema(description = "所使用的大模型名称", example = "deepseek-chat")
        private String model;

        @Schema(description = "API 基地址", example = "https://api.deepseek.com/v1")
        private String api_base;

        @Schema(description = "API key", example = "sk-dafa3dcc495247ea80e3d609fe463336")
        private String api_key;
    }

    @Schema(description = "请求唯一标识", example = "req_abc123def456")
    private String requestId;

    @Schema(description = "时间戳（单位：秒）", example = "1703123456")
    private Long timestamp;
}
