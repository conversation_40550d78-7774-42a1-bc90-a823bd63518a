package com.center.emergency.biz.files.service;


import com.center.emergency.biz.files.pojo.*;
import com.center.emergency.biz.files.pojo.filechunk.Chunk;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.web.pojo.PageResult;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

/**
 * 文件服务接口
 */
public interface FileService {

    /**
     * 创建文件
     * @param req 文件创建请求
     * @return 文件ID
     */
    Long createFile(FileUploadReq req);

    /**
     * 更新文件
     *
     * @param req 文件更新请求
     */
    void updateFile(FileUpdateReq req);

    /**
     * 删除文件
     *
     * @param id 文件ID
     */
    void deleteFile(Long id);

    /**
     * 预览文件详情
     *
     * @param id 文件ID
     * @return FileResp 文件详情
     */
    FileResp getFileById(Long id);

    /**
     * 根据知识库ID获取文件列表
     *
     * @param kbId 知识库ID
     * @return List<FileResp> 文件列表
     */
    List<FileResp> getFilesByKnowledgeBaseId(Long kbId);

    /**
     * 根据知识库ID获取文件列表
     *
     * @param filePageQueryReq 文件分页查询请求对象，包含知识库ID、分页信息等
     * @return 返回一个包含文件响应对象的页面结果
     */
    PageResult<FileResp> getFilesByKnowledgeBaseId(FilePageQueryReq filePageQueryReq);

    /**
     * 从本地上传文件到指定位置
     *
     * @param file 文件对象，包含文件内容和文件头信息
     * @param kbId 知识库ID，用于确定文件存储的位置或关联的信息
     * @param fileName 文件名，用于存储时的文件命名
     * @param originalFilename 原始文件名，用于记录或显示原始文件名称
     * @param rename 是否重命名的标志，1 表示重命名，0 表示不重命名
     * @return 返回一个文件上传请求对象，包含上传的相关信息
     */
    FileUploadReq uploadFileFromLocal(MultipartFile file, Long kbId, String fileName, String originalFilename, Integer rename);

    /**
     * 在模拟对话过程中从本地上传文件
     * @param file 文件对象，包含文件内容和文件头信息
     */
    Long uploadFileFromLocalForDailogue(MultipartFile file);


    /**
     * 从云存储中上传文件到另一个云存储路径
     * 此方法用于处理从一个云存储路径到另一个云存储路径的文件上传逻辑它接收一个文件路径列表，
     * 一个知识库ID（kbId），以及一个目标目录路径列表方法将根据这些参数实现文件的上传功能
     *
     * @param filePaths 文件路径列表，表示需要上传的文件的路径
     * @param kbId 知识库ID，用于指定上传文件所属的知识库
     * @param dirPaths 目标目录路径列表，表示文件将要上传到的目录路径
     * @return 返回一个FileUploadReq对象列表，表示上传的文件信息
     */
    List<FileUploadReq> uploadFilesFromCloudToCloud(List<String> filePaths, Long kbId, List<String> dirPaths);

    /**
     * 读取文件，转换为pdf，再上传
     *
     * @param originPath 文件路径
     */
    String convertPdf(String originPath);

    /**
     * 更新pdf文件路径
     */
    void updatePdfHdfsPath(Long fileId, String pdfHdfsPath);

    /**
     * 异步调用python接口，上传文件并转换为pdf
     */
    void asyncCallPythonUploadAndConvertPdf(FileUploadReq fileUploadReq);

    /**
     * 批量异步转换以及上传
     */
    void asyncCallPythonUploadBatch(List<FileUploadReq> fileUploadReqList);

    /**
     * python接口返回文件信息，更新文件状态和标签
     */
    void updateFileStatusAndTagsByPython(FileUploadPythonReq req);

    /**
     * 获取指定路径下的所有文件列表
     *
     * @param path 文件路径，指定需要检索文件的目录路径
     * @return 返回一个包含所有文件信息的列表，文件信息封装在FileListResp对象中
     */
    List<FileListResp> getAllFiles(String path);

    /**
     * 根据查询请求获取文件列表
     *
     * @param fileId@return 匹配查询条件的文件列表，每个元素是FileResp对象，包含文件的相关信息
     */
    FileResp getFileByQuery(Long fileId);

    /**
     * 根据文件生成知识
     * 本函数旨在处理文件，并生成相应的知识内容，主要用于知识库的构建和更新
     *
     * @param req 包含文件生成知识所需请求参数的对象
     */
    void generateKnowledgeForFile(FileGenerateKnowledgeReq req);

    /**
     * 转换为pdf文件后上传，返回路径
     *
     * @param file-上传的文件
     * @return 路径
     */
    UploadFileReps uploadFile(MultipartFile file);

    /**
     * 根据任务id获取pdf信息
     *
     * @param jobId-任务id
     * @return pdf信息
     */
    HashMap pdfMessage(Long jobId);

    /**
     * 根据url和关键词列表生成任务id
     * @param pdfReq-请求参数，包含url和关键词列表
     * @return 任务id
     */
    Long getJobId(PdfReq pdfReq);

    /**
     * 保存pdf信息
     *
     * @param pdfMessageReq-pdf信息
     */
    void savePdfMessage(PdfMessageReq pdfMessageReq);

    /**
     * 使用事务上传文件
     * 该方法负责处理文件上传过程，确保文件上传的一致性和完整性
     *
     * @param file 要上传的文件，类型为MultipartFile，适用于Web表单文件上传
     * @param kbId 文件所属知识库的ID，用于确定文件的存储位置或关联信息
     * @param fileName 文件名，用于在系统中标识文件
     * @param originalFilename 原始文件名，即客户端上传文件时的文件名，用于参考或展示
     * @param rename 文件重命名标志，指示是否需要系统重命名文件，1表示需要重命名，其他值表示不需要
     *
     * @return 返回一个FileUploadReq对象，该对象包含了上传文件的请求信息和可能的响应结果
     */
    FileUploadReq uploadFileFromLocalWithTransaction(MultipartFile file, Long kbId, String fileName, String originalFilename, Integer rename);

    /**
     * 迁移云端文件并保存文件信息到数据库
     *
     * @param filePaths 要迁移的文件路径列表
     * @param kbId 知识库ID
     * @param dirPaths 目录路径列表
     * @return 返回包含上传请求的列表
     */
    List<FileUploadReq> uploadAndSaveFilesFromCloud(List<String> filePaths, Long kbId, List<String> dirPaths);

    /**
     * 上传图片
     *
     * @param file-上传的文件
     * @return id
     */
    Long upload(MultipartFile file);

    PageResult<Chunk> getDocChunk(DocChunkPageReq docChunkPageReq);

    void updateDocChunk(UpdateDocChunkReq updateDocChunkReq);

    ResponseEntity<ByteArrayResource> previewById(Long fileId);

}