package com.center.emergency.biz.tag.service;

import com.center.emergency.biz.tag.persistence.TagModel;
import com.center.emergency.biz.tag.pojo.*;
import com.center.framework.web.pojo.PageResult;

import java.util.List;

public interface TagService {
    /**
     * 创建标签
     * @param tagName 标签名称
     */
    void createTag(String tagName);

    /**
     * 获取标签内容
     * @param id 标签id
     * @return TagGetResp类,包含标签名称和标签id
     */
    TagGetResp getTag(Long id);


    /**
     * 查询当前租户的自定义标签以及所有系统标签
     * @param tenantId 当前登录用户的租户ID
     * @return 标签列表
     */
    List<TagResp> getTenantAndSystemTags(Long tenantId);

    /**
     * 获取标签的分页结果
     * @param pageReq 包含分页信息和查询条件的请求对象，包含当前页码和每页大小。
     * @return 标签分页结果,包含标签信息及总记录数。
     */
    PageResult<TagPageResp> getPageTags(TagPageReq pageReq);

    /**
     * 保存标签信息
     * @param req 标签保存所需信息，包含id和名称
     */
    void saveTag(TagSaveReq req);

    /**
     * 删除标签
     * @param id 标签id
     */
    void deleteTag(Long id);
    /**
     * 根据文件ID获取文件标签
     *
     * @param fileId 文件ID，用于唯一标识一个文件
     * @return 返回一个列表，包含与指定文件ID关联的所有标签信息
     */
    List<TagFileRsp> getFileTagsByFileId(Long fileId);
}
