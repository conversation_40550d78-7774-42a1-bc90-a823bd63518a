package com.center.emergency.biz.aisearch.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AI搜索会话Repository
 */
@Repository
public interface AiSearchSessionRepository extends JoinFetchCapableQueryDslJpaRepository<AiSearchSessionModel, Long> {
    
    /**
     * 根据租户ID和创建者ID查询会话列表
     */
    List<AiSearchSessionModel> findByTenantIdAndCreatorIdOrderByCreateTimeDesc(Long tenantId, Long creatorId);
    
    /**
     * 根据租户ID、创建者ID和搜索模式查询会话列表
     */
    List<AiSearchSessionModel> findByTenantIdAndCreatorIdAndSearchModeOrderByCreateTimeDesc(Long tenantId, Long creatorId, Integer searchMode);
    
    /**
     * 检查会话是否存在
     */
    boolean existsByIdAndTenantIdAndCreatorId(Long id, Long tenantId, Long creatorId);
    
    /**
     * 统计用户的会话数量
     */
    long countByTenantIdAndCreatorId(Long tenantId, Long creatorId);
} 