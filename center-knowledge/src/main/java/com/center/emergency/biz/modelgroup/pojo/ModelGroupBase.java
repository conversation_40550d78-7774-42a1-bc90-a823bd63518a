package com.center.emergency.biz.modelgroup.pojo;

import com.center.emergency.common.enumeration.SourceTypeEnum;
import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ModelGroupBase {
    @Schema(description = "模型组id")
    private Long modelGroupId;

    @Schema(description = "模型组名称")
    private String groupName;

    @Schema(description = "模型组来源", example = "CUSTOM")
    private SourceTypeEnum sourceType;

    @EnumConvert(value = SourceTypeEnum.class, srcFieldName = "sourceType")
    @Schema(description = "模型组来源名称", example = "用户自定义")
    private String sourceTypeName;

}
