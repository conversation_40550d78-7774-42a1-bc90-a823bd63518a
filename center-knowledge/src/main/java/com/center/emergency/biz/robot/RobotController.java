package com.center.emergency.biz.robot;


import com.center.emergency.biz.robot.persitence.RobotKBRepository;
import com.center.emergency.biz.robot.persitence.RobotRepository;
import com.center.emergency.biz.robot.pojo.*;
import com.center.emergency.biz.robot.service.RobotService;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.pojo.DepartAndKbListResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import java.util.List;

/**
 * 机器人控制器类
 * <AUTHOR>
 */
@RestController
@Tag(name = "机器人")
@RequestMapping("/robot")
@Validated
public class RobotController {

    @Resource
    private RobotService robotService;
    @Autowired
    private RobotRepository robotRepository;
    @Autowired
    private RobotKBRepository robotKBRepository;

    @PostMapping("/create")
    @Operation(summary = "创建智能助手")
    public CommonResult<Long> createRobot(@Valid @RequestBody AssistantCreateReq req) {
        Long robotId = robotService.createRobots(req);
        return CommonResult.success(robotId,"智能助手创建成功");
    }

    @PostMapping("/update")
    @Operation(summary = "更新智能助手")
    public CommonResult<String> updateRobot(@Valid @RequestBody AssistantUpdateReq req) {
        robotService.updateRobot(req);
        return CommonResult.successWithMessageOnly("智能助手更新成功");
    }

    @PostMapping("/save")
    @Operation(summary = "保存修改后的机器人信息")
    public CommonResult<String> saveRobot(@Valid @RequestBody RobotSaveReq req) {
        robotService.saveRobot(req);
        return CommonResult.successWithMessageOnly("机器人保存成功");
    }


    @GetMapping("/search")
    @Operation(summary = "机器人分页列表及查询")
    @EnumConvertPoint
    public CommonResult<PageResult<RobotPageResp>> getPageRobot(@Valid RobotPageReq pageReq) {
        return CommonResult.success(robotService.getRobotsByPage(pageReq));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除机器人")
    @Transactional
    public CommonResult<String> deleteRobot(@RequestParam(name = "id") Long id) {
        // 根据ID查找机器人
        robotRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人"));
        robotRepository.deleteById(id);
        robotKBRepository.deleteByRobotId(id);
        return CommonResult.successWithMessageOnly("机器人删除成功");
    }

    @PostMapping("/switch")
    @Operation(summary = "切换机器人状态：启用/停用")
    public CommonResult<String> switchRobot(@Valid @RequestBody RobotStatusReq statusReq) {
        robotService.switchStatus(statusReq);
        return CommonResult.successWithMessageOnly("状态切换成功");
    }

    @GetMapping("/list_kb")
    @Operation(summary = "关联知识库")
    public CommonResult<List<DepartAndKbListResp>> listDepartAndKb(Long departId, String path) {
        return CommonResult.success(robotService.listDepartAndKb(departId, path));
    }

    @GetMapping("/details")
    @Operation(summary = "获取机器人详细信息")
    @EnumConvertPoint
    public CommonResult<RobotDetailsResp> getRobotDetails(@RequestParam(name = "id") Long id) {
        return CommonResult.success(robotService.getRobotDetails(id));
    }

    @GetMapping("/mcp_info")
    @Operation(summary = "获取机器人关联的MCP信息")
    public CommonResult<List<MCPResp>> getMCPInfo(@RequestParam(name = "id") Long id) {
        return CommonResult.success(robotService.getMCPInfoByRobotId(id));
    }

}
