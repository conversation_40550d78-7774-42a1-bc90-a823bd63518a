package com.center.emergency.biz.chat.persistence;

import com.center.framework.db.annotation.NotIgnoreNullField;
import com.center.framework.db.core.BaseModel;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


@Data
@Entity
@QueryEntity
@Table(name = "center_chat_answer")
@NotIgnoreNullField
public class ChatAnswerModel extends BaseModel {

    @Schema(description = "回答内容")
    @Column(name = "content",nullable = false)
    private String content;

    @Schema(description = "问题ID")
    @Column(name = "question_id",nullable = false)
    private Long questionId;

    @Schema(description = "会话ID")
    @Column(name = "session_id",nullable = false)
    private Long sessionId;

    @Schema(description = "点赞")
    @Column(name = "thumbs_up")
    private Boolean thumbsUp;
}
