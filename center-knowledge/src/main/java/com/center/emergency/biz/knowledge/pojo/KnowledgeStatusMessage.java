package com.center.emergency.biz.knowledge.pojo;

import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;

/**
 * 响应对象：返回知识库的详细信息
 */
@Data
public class KnowledgeStatusMessage {

    @Schema(description = "知识id")
    private String id;

    @Schema(description = "知识名称", example = "知识A")
    private String knowledgeName;

    @Enumerated(EnumType.STRING)
    @Schema(description = "知识状态")
    private KnowledgeStatusEnum status;

    @Schema(description = "知识状态名")
    @EnumConvert(value = KnowledgeStatusEnum.class,srcFieldName = "status")
    private String statusName;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    @Schema(description = "操作人")
    private String operator;



}