package com.center.emergency.biz.robot.DTO;

import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class KnowledgeBaseDTO {
    //知识库Id
    @Schema(description = "知识库Id",example = "1")
    private Long id;

    //知识库名称
    @Schema(description = "知识库名称",example = "知识库1")
    private String kbName;

}
