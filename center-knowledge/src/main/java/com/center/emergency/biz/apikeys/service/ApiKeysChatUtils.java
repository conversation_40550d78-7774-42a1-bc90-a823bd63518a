package com.center.emergency.biz.apikeys.service;

import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.files.persistence.QFileTagModel;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.tag.persistence.QTagModel;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * API Keys 聊天工具类
 * 从 chat 模块复制必要的方法，确保 apikeys 模块的独立性
 */
@Service
@Slf4j
public class ApiKeysChatUtils {
    
    /**
     * 从知识库获取标签ID列表（只包含标签ID）
     * 复制自 ChatTagService.getTagsList
     * 
     * @param knowledgeBaseId 知识库ID
     * @param queryFactory JPA查询工厂
     * @return 标签ID列表
     */
    public List<Long> getKnowledgeBaseTagIds(Long knowledgeBaseId, JPAQueryFactory queryFactory) {
        log.debug("从知识库获取标签ID列表: knowledgeBaseId={}", knowledgeBaseId);
        
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QFileModel qFileModel = QFileModel.fileModel;
        QFileTagModel qFileTagModel = QFileTagModel.fileTagModel;
        QTagModel qTagModel = QTagModel.tagModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeModel.kbId.eq(knowledgeBaseId));
        builder.and(qKnowledgeModel.status.eq(KnowledgeStatusEnum.ENABLED));
        builder.and(qKnowledgeModel.fileId.eq(qFileModel.id));
        builder.and(qFileTagModel.fileId.eq(qFileModel.id));
        builder.and(qTagModel.id.eq(qFileTagModel.tagId));

        List<Tuple> tupleList = queryFactory.select(qTagModel.id, qTagModel.tagName)
                .distinct()
                .from(qTagModel, qFileModel, qFileTagModel, qKnowledgeModel)
                .where(builder)
                .fetch();
                
        Iterator<Tuple> iterator = tupleList.iterator();
        List<Long> result = new ArrayList<>();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            result.add(tuple.get(qTagModel.id));
        }
        
        log.debug("知识库标签ID列表获取完成: 共{}个标签", result.size());
        return result;
    }
    
    /**
     * 从机器人构建知识库和文件ID（API Key场景专用）
     * 复制自 ChatResourceService.buildForApiKey
     * 
     * @param robotId 机器人ID
     * @param kbIds 知识库ID集合（输出参数）
     * @param fileIds 文件ID列表（输出参数）
     * @param queryFactory JPA查询工厂
     * @param departId 指定的部门ID
     * @param tenantId 指定的租户ID
     * @param departRepository 部门仓库，用于权限查询
     */
    public void buildRobotResourcesForApiKey(Long robotId, Set<String> kbIds, List<String> fileIds, JPAQueryFactory queryFactory, Long departId, Long tenantId, DepartRepository departRepository) {
        log.debug("从机器人构建资源范围(API Key场景): robotId={}, departId={}, tenantId={}", robotId, departId, tenantId);
        
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(robotId));
        builder.and(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id));

        List<Long> subDepartIds = departRepository.selectSubById(departId);
        List<DepartModel> rootParent = departRepository.findByParentIdAndTenantId(0L, tenantId);
        for (DepartModel departModel : rootParent) {
            subDepartIds.add(departModel.getId());
        }
        builder.and(qKnowledgeBaseModel.departmentId.in(subDepartIds));

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qRobotKnowledgeModel)
                .leftJoin(qKnowledgeBaseModel).on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .leftJoin(qKnowledgeModel).on(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId))
                .where(builder)
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }
        
        log.debug("机器人资源范围构建完成(API Key场景): kbIds.size={}, fileIds.size={}", kbIds.size(), fileIds.size());
    }
}
