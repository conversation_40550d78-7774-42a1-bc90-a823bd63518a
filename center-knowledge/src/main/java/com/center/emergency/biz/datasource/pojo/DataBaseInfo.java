package com.center.emergency.biz.datasource.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class DataBaseInfo {

    @NotBlank(message = "数据库地址不能为空")
    @Schema(description = "数据库地址")
    @Size(message = "数据库地址长度不能超过50。",max = 50)
    private String ip;

    @NotNull(message = "端口不能为空")
    @Schema(description = "数据库端口")
    private Integer port;

    @Schema(description = "数据库名")
    @NotNull(message = "数据库名不能为空")
    @Size(message = "数据库名长度不能超过50。",max = 50)
    private String databaseName;

    @Schema(description = "数据库用户名")
    @NotNull(message = "数据库用户名不能为空")
    @Size(message = "数据库用户名长度不能超过20。",max = 20)
    private String userName;

    @Schema(description = "数据库密码")
    @NotNull(message = "数据库密码不能为空")
    @Size(message = "数据库密码长度不能超过20。",max = 20)
    private String password;

    @Schema(description = "jdbc参数")
    private String jdbcParameter;
}
