package com.center.emergency.biz.robot.persitence;

import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.db.core.BaseDeleteModel;
import com.center.framework.db.core.BaseTenantModel;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@QueryEntity
@Table(
        name = "center_robot_knowledge_bases"
)
public class RobotKnowledgeModel extends BaseTenantModel {

    @Column(name = "robot_id")
    Long robotId;

    @Column(name = "kb_id")
    private Long kbId;

    @Column(name = "answer_strategy")
    @Enumerated(value = EnumType.STRING)
    private AnswerStrategyEnum answerStrategy;

}
