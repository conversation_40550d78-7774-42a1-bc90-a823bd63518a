package com.center.emergency.biz.aisearch.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AI搜索事件Repository
 */
@Repository
public interface AiSearchEventRepository extends JoinFetchCapableQueryDslJpaRepository<AiSearchEventModel, Long> {
    
    /**
     * 根据消息ID查询事件列表，按序号排序
     */
    List<AiSearchEventModel> findByMessageIdAndTenantIdOrderBySequenceOrderAsc(Long messageId, Long tenantId);
    
    /**
     * 根据会话ID查询事件列表，按创建时间排序
     */
    List<AiSearchEventModel> findBySessionIdAndTenantIdOrderByCreateTimeAsc(Long sessionId, Long tenantId);
    
    /**
     * 根据消息ID和事件类型查询事件列表
     */
    List<AiSearchEventModel> findByMessageIdAndTenantIdAndEventTypeOrderBySequenceOrderAsc(Long messageId, Long tenantId, String eventType);
    
    /**
     * 删除指定消息的所有事件
     */
    @Modifying
    void deleteByMessageIdAndTenantId(Long messageId, Long tenantId);
    
    /**
     * 删除指定会话的所有事件
     */
    @Modifying
    void deleteBySessionIdAndTenantId(Long sessionId, Long tenantId);
    
    /**
     * 统计消息的事件数量
     */
    long countByMessageIdAndTenantId(Long messageId, Long tenantId);
    
    /**
     * 统计会话的事件数量
     */
    long countBySessionIdAndTenantId(Long sessionId, Long tenantId);
} 