package com.center.emergency.biz.knowledgebase;


import com.center.emergency.biz.knowledgebase.pojo.*;
import com.center.emergency.biz.knowledgebase.service.KnowledgeBaseService;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
@RestController
@Tag(name = "知识库管理", description = "提供知识库的创建、更新、删除、查询功能")
@RequestMapping("/knowledgebase")
@Validated
public class KnowledgeBaseController {
    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @PostMapping("/create")
    @Operation(summary = "创建知识库", description = "根据请求参数创建一个新的知识库。")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "创建知识库的请求体，包含知识库名称，用户ID，部门ID",
            required = true,
            content = @Content(schema = @Schema(implementation = KnowledgeBaseCreateReq.class))
    )
    public CommonResult<String> createKnowledgeBase(@Valid @RequestBody KnowledgeBaseCreateReq req) {
        knowledgeBaseService.createKnowledgeBase(req);
        return CommonResult.successWithMessageOnly("知识库创建成功");
    }

    @PostMapping("/update")
    @Operation(summary = "更新知识库信息", description = "根据请求参数更新知识库的名称。")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "更新知识库的请求体，包含需要更新的知识库ID和新的名称",
            required = true,
            content = @Content(schema = @Schema(implementation = KnowledgeBaseUpdateReq.class))
    )
    public CommonResult<String> updateKnowledgeBase(@Valid @RequestBody KnowledgeBaseUpdateReq req) {
        knowledgeBaseService.updateKnowledgeBase(req);
        return CommonResult.successWithMessageOnly("知识库更新成功");
    }

    // 更新此方法，将 @RequestParam 更改为 @PathVariable
    @PostMapping("/delete/{id}")
    @Operation(summary = "删除知识库", description = "根据知识库ID删除对应的知识库。")
    @Parameter(name = "id", description = "知识库的ID", example = "1", required = true)
    public CommonResult<String> deleteKnowledgeBase(@PathVariable @NotNull Long id) {
        knowledgeBaseService.deleteKnowledgeBase(id);
        return CommonResult.successWithMessageOnly("知识库删除成功");
    }

    @GetMapping("/get")
    @Operation(summary = "获取知识库详细信息", description = "根据知识库ID获取其详细信息，包括标签。")
    @Parameter(name = "id", description = "要查询的知识库ID", example = "1", required = true)
    public CommonResult<KnowledgeBaseResp> getKnowledgeBaseById(@RequestParam @NotNull Long id) {
        KnowledgeBaseResp resp = knowledgeBaseService.getKnowledgeBaseByIdWithTags(id);
        return CommonResult.success(resp);
    }

    @GetMapping("/search")
    @Operation(summary = "弃用-根据条件查询知识库", description = "如果 deptId 为 null 或 0，则查询所有公司层级的知识库；否则根据部门ID和租户ID进行过滤查询。")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "查询条件请求体，包含部门ID和租户ID等过滤条件",
            required = true,
            content = @Content(schema = @Schema(implementation = KnowledgeBaseQueryReq.class))
    )
    public List<KnowledgeBaseResp> searchKnowledgeBases(@RequestBody KnowledgeBaseQueryReq queryReq) {
        return knowledgeBaseService.getKnowledgeBases(queryReq);
    }

    @GetMapping("/list_by_dept")
    @Operation(
            summary = "根据部门ID查询知识库",
            description = "根据部门ID查询所属部门的知识库，0表示公司层级的知识库。"
    )
    @Parameter(name = "deptId", description = "部门ID", example = "0", required = true)
    @Parameter(name = "path", description = "权限路径，用于权限校验", example = "/123123/123123", required = true)
    public CommonResult<List<KnowledgeBaseResp>> listByDeptId(
            @RequestParam Long deptId,
            @RequestParam(required = false) String path) {
        List<KnowledgeBaseResp> result = knowledgeBaseService.getKnowledgeBasesByDeptId(deptId, path);
        return CommonResult.success(result);
    }

    @GetMapping("/search_knowledge_base")
    @Operation(summary = "根据搜索条件查询知识库", description = "")
    public CommonResult<SearchResult> searchKnowledgeBase(@RequestParam(required = false) String keyWord) {
        return CommonResult.success(knowledgeBaseService.searchKBases(keyWord));
    }

}