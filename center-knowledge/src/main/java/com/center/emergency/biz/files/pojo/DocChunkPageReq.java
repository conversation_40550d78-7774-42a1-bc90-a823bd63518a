package com.center.emergency.biz.files.pojo;

import com.center.framework.web.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DocChunkPageReq extends PageParam {

    @NotNull(message = "文件ID不能为空")
    @Schema(description = "文件的ID")
    private Long fileId;

    @Schema(description = "查询内容")
    private String keyword;
}
