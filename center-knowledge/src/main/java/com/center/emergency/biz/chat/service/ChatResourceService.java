package com.center.emergency.biz.chat.service;

import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.framework.common.context.LoginContextHolder;
import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * 聊天资源服务
 * 迁移自AgentChatUtils中的资源构建方法
 */
@Service
@Slf4j
public class ChatResourceService {
    
    /**
     * 从机器人构建知识库和文件ID（机器人对话和重新回答场景使用）
     * 迁移自AgentChatUtils.buildFromRobot
     * 
     * @param robotId 机器人ID
     * @param kbIds 知识库ID集合（输出参数）
     * @param fileIds 文件ID列表（输出参数）
     * @param queryFactory JPA查询工厂
     * @param departRepository 部门仓库，用于权限查询
     */
    public void buildFromRobot(Long robotId, Set<String> kbIds, List<String> fileIds, JPAQueryFactory queryFactory, DepartRepository departRepository) {
        log.debug("从机器人构建资源范围: robotId={}", robotId);
        
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(robotId));
        builder.and(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id));

        List<Long> subDepartIds = departRepository.selectSubById(LoginContextHolder.getLoginUserDepartId());
        List<DepartModel> rootParent = departRepository.findByParentIdAndTenantId(0L, LoginContextHolder.getLoginUserTenantId());
        for (DepartModel departModel : rootParent) {
            subDepartIds.add(departModel.getId());
        }
        builder.and(qKnowledgeBaseModel.departmentId.in(subDepartIds));

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qRobotKnowledgeModel)
                .leftJoin(qKnowledgeBaseModel).on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .leftJoin(qKnowledgeModel).on(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId))
                .where(builder)
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }
        
        log.debug("机器人资源范围构建完成: kbIds.size={}, fileIds.size={}", kbIds.size(), fileIds.size());
    }
    
    /**
     * 从机器人构建知识库和文件ID（API Key场景专用）
     * 迁移自AgentChatUtils.buildForApiKey
     * 
     * @param robotId 机器人ID
     * @param kbIds 知识库ID集合（输出参数）
     * @param fileIds 文件ID列表（输出参数）
     * @param queryFactory JPA查询工厂
     * @param departId 指定的部门ID
     * @param tenantId 指定的租户ID
     * @param departRepository 部门仓库，用于权限查询
     */
    public void buildForApiKey(Long robotId, Set<String> kbIds, List<String> fileIds, JPAQueryFactory queryFactory, Long departId, Long tenantId, DepartRepository departRepository) {
        log.debug("从机器人构建资源范围(API Key场景): robotId={}, departId={}, tenantId={}", robotId, departId, tenantId);
        
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotKnowledgeModel.robotId.eq(robotId));
        builder.and(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id));

        List<Long> subDepartIds = departRepository.selectSubById(departId);
        List<DepartModel> rootParent = departRepository.findByParentIdAndTenantId(0L, tenantId);
        for (DepartModel departModel : rootParent) {
            subDepartIds.add(departModel.getId());
        }
        builder.and(qKnowledgeBaseModel.departmentId.in(subDepartIds));

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qRobotKnowledgeModel)
                .leftJoin(qKnowledgeBaseModel).on(qRobotKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .leftJoin(qKnowledgeModel).on(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId))
                .where(builder)
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }
        
        log.debug("机器人资源范围构建完成(API Key场景): kbIds.size={}, fileIds.size={}", kbIds.size(), fileIds.size());
    }

    /**
     * 从知识库构建知识库和文件ID（知识库对话场景使用）
     * 迁移自AgentChatUtils.buildFromKnowledgeBase
     *
     * @param knowledgeBaseId 知识库ID
     * @param kbIds 知识库ID集合（输出参数）
     * @param fileIds 文件ID列表（输出参数）
     * @param queryFactory JPA查询工厂
     */
    public void buildFromKnowledgeBase(Long knowledgeBaseId, Set<String> kbIds, List<String> fileIds, JPAQueryFactory queryFactory) {
        log.debug("从知识库构建资源范围: knowledgeBaseId={}", knowledgeBaseId);

        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qKnowledgeBaseModel.id.eq(knowledgeBaseId));
        builder.and(qKnowledgeBaseModel.id.eq(qKnowledgeModel.kbId));

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qKnowledgeBaseModel)
                .leftJoin(qKnowledgeModel).on(qKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .where(qKnowledgeBaseModel.id.eq(knowledgeBaseId))
                .fetch();

        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                kbIds.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }

        log.debug("知识库资源范围构建完成: kbIds.size={}, fileIds.size={}", kbIds.size(), fileIds.size());
    }
}
