package com.center.emergency.biz.chat.common.collector;

import com.center.emergency.biz.chat.common.enumeration.ParsedEventType;
import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 智能事件收集器，用于收集对话过程中的所有事件
 * 会自动合并连续的MESSAGE事件为一个完整的回答
 */
@Slf4j
public class EventCollector {
    
    private final List<ParsedEvent> mergedEvents = new ArrayList<>();
    private final StringBuilder currentMessageBuilder = new StringBuilder();
    private final StringBuilder totalMessageBuilder = new StringBuilder();
    
    /**
     * 添加事件 - 智能合并连续的MESSAGE事件
     */
    public void addEvent(ParsedEvent event) {
        if (event.getEventType() == ParsedEventType.MESSAGE && event.getContent() != null) {
            // 累积MESSAGE内容（保留完整内容，包含思考标签）
            currentMessageBuilder.append(event.getContent());
            totalMessageBuilder.append(event.getContent());
            log.debug("累积MESSAGE内容: {}, 当前长度: {}", event.getContent(), currentMessageBuilder.length());
        } else {
            // 非MESSAGE事件，先处理之前累积的MESSAGE
            flushCurrentMessage();

            // 添加当前非MESSAGE事件
            mergedEvents.add(event);
            log.debug("添加非MESSAGE事件: {}, 当前合并事件总数: {}", event.getEventType(), mergedEvents.size());
        }
    }
    
    /**
     * 将当前累积的MESSAGE内容作为一个事件添加到合并列表
     */
    private void flushCurrentMessage() {
        if (currentMessageBuilder.length() > 0) {
            ParsedEvent messageEvent = new ParsedEvent();
            messageEvent.setEventType(ParsedEventType.MESSAGE);
            messageEvent.setContent(currentMessageBuilder.toString());
            mergedEvents.add(messageEvent);
            
            log.debug("合并MESSAGE事件，内容长度: {}, 当前合并事件总数: {}", 
                     currentMessageBuilder.length(), mergedEvents.size());
            
            // 清空当前MESSAGE累积器
            currentMessageBuilder.setLength(0);
        }
    }
    
    /**
     * 获取所有合并后的事件（用于数据库存储）
     */
    public List<ParsedEvent> getMergedEvents() {
        // 确保最后的MESSAGE也被处理
        flushCurrentMessage();
        return new ArrayList<>(mergedEvents);
    }
    
    /**
     * 获取累积的完整消息内容
     */
    public String getAccumulatedMessage() {
        return totalMessageBuilder.toString();
    }
    
    /**
     * 获取合并后的事件总数
     */
    public int getMergedEventCount() {
        // 临时flush来计算准确数量，但不改变状态
        int currentCount = mergedEvents.size();
        if (currentMessageBuilder.length() > 0) {
            currentCount++; // 还有未flush的MESSAGE
        }
        return currentCount;
    }
    
    /**
     * 是否包含指定类型的事件
     */
    public boolean hasEventType(ParsedEventType eventType) {
        // 检查已合并的事件
        boolean found = mergedEvents.stream().anyMatch(event -> event.getEventType() == eventType);
        
        // 如果查找MESSAGE类型，还要检查当前累积的MESSAGE
        if (!found && eventType == ParsedEventType.MESSAGE && currentMessageBuilder.length() > 0) {
            found = true;
        }
        
        return found;
    }
    
    /**
     * 清空所有事件
     */
    public void clear() {
        mergedEvents.clear();
        currentMessageBuilder.setLength(0);
        totalMessageBuilder.setLength(0);
    }
    
    /**
     * 获取当前状态信息（用于调试）
     */
    public String getStatusInfo() {
        return String.format("合并事件数: %d, 当前MESSAGE长度: %d, 总MESSAGE长度: %d",
                           mergedEvents.size(), currentMessageBuilder.length(), totalMessageBuilder.length());
    }
}