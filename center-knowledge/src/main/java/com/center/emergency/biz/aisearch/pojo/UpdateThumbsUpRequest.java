package com.center.emergency.biz.aisearch.pojo;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 更新点赞状态请求参数
 */
@Data
@Schema(description = "更新点赞状态请求参数")
public class UpdateThumbsUpRequest {
    
    /**
     * 点赞状态：0-无，1-赞，-1-踩
     */
    @Schema(description = "点赞状态：0-无，1-赞，-1-踩", required = true, example = "1")
    @NotNull(message = "点赞状态不能为空")
    @Min(value = -1, message = "点赞状态必须为-1、0或1")
    @Max(value = 1, message = "点赞状态必须为-1、0或1")
    private Integer thumbsUp;
} 