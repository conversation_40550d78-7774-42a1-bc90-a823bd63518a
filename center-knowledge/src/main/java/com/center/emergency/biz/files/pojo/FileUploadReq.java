package com.center.emergency.biz.files.pojo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 文件更新请求类，继承 FileBase，增加 id 字段
 * <AUTHOR>
 */
@Data
public class FileUploadReq extends FileBase {

    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不能为空")
    private Long id;

    /**
     * 原始文件名称
     */
    @Schema(description = "原始文件名称")
    @NotNull(message = "原始文件名称不能为空")
    @Length(max = 100, message = "原始文件名称长度不能超过100个字符")
    private String originalName;

    /**
     * 文件重名序号
     */
    @Schema(description = "文件重名序号")
    private Integer fileNum;
}