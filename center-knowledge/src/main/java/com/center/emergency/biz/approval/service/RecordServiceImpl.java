package com.center.emergency.biz.approval.service;

import com.center.emergency.biz.approval.persistence.QRecordModel;
import com.center.emergency.biz.approval.persistence.RecordModel;
import com.center.emergency.biz.approval.persistence.RecordRepository;
import com.center.emergency.biz.approval.pojo.KnowledgeRecordMessage;
import com.center.emergency.biz.approval.pojo.RecordView;
import com.center.emergency.biz.knowledge.persistence.KnowledgeModel;
import com.center.emergency.biz.knowledge.persistence.KnowledgeRepository;
import com.center.emergency.biz.webskt.WebSocketServer;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.emergency.common.enumeration.RecordStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RecordServiceImpl implements RecordService {
    @Resource
    private JPAQueryFactory queryFactory;
    @Resource
    private KnowledgeRepository knowledgeRepository;
    @Resource
    private RecordRepository recordRepository;
    @Autowired
    private UserRepository userRepository;

    @Override
    public List<RecordView> getRecordList(Long knowledgeId) {
        try {
            knowledgeRepository.findById(knowledgeId)
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));
            BooleanBuilder builder = new BooleanBuilder();
            QRecordModel qRecordModel = QRecordModel.recordModel;
            QUserModel qUserModel = QUserModel.userModel;
            builder.and(qRecordModel.creatorId.eq(qUserModel.id));
            builder.and(qRecordModel.knowledgeId.eq(knowledgeId));
            JPQLQuery<RecordView> jpqlQuery = queryFactory.select((Projections.bean(
                            RecordView.class,
                            qRecordModel.id,
                            qRecordModel.operationResult,
                            qRecordModel.reason,
                            qRecordModel.operationTime,
                            qUserModel.displayName
                    )))
                    .from(qRecordModel, qUserModel)
                    .orderBy(qRecordModel.createTime.desc())
                    .where(builder);
            return jpqlQuery.fetch();
        } catch (IllegalArgumentException e) {
            log.error("查询记录错误", e);
            throw  ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,e, "查询记录错误");
        }
    }

    @Transactional
    @Override
    public void pass(Long knowledgeId) {
        try {
            KnowledgeModel knowledgeModel = knowledgeRepository.findById(knowledgeId)
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));
            if(knowledgeModel.getStatus().equals(KnowledgeStatusEnum.APPROVALING)) {
                knowledgeModel.setStatus(KnowledgeStatusEnum.ENABLED);
                knowledgeRepository.save(knowledgeModel);
                RecordSave(knowledgeId,RecordStatusEnum.PASS,null);
            }
            else {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "知识状态错误");
            }
        } catch (IllegalArgumentException e) {
            log.error("查询知识错误", e);
            throw  ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,e, "查询知识错误");
        }
    }

    @Transactional
    @Override
    public void refuse(Long knowledgeId, String refuseReason) {
        try {
            KnowledgeModel knowledgeModel = knowledgeRepository.findById(knowledgeId)
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "知识不存在"));
            if(knowledgeModel.getStatus().equals(KnowledgeStatusEnum.APPROVALING)) {
                knowledgeModel.setStatus(KnowledgeStatusEnum.REJECTED);
                knowledgeRepository.save(knowledgeModel);
                RecordSave(knowledgeId,RecordStatusEnum.REFUSE,refuseReason);
            }
            else {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "知识状态错误");
            }
        } catch (IllegalArgumentException e) {
            log.error("查询知识不存在", e);
            throw  ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR,e, "查询知识错误");
        }
    }

    @Override
    public void RecordSave(Long knowledgeId,RecordStatusEnum status,String reason) {
        RecordModel recordModel = new RecordModel();
        recordModel.setKnowledgeId(knowledgeId);
        recordModel.setOperationResult(status);
        recordModel.setReason(reason);
        LocalDateTime time = LocalDateTime.now();
        recordModel.setOperationTime(time);
        try {

            RecordModel saveModel = recordRepository.save(recordModel);

            //获取操作人名称
            String displayName = userRepository.findById(saveModel.getCreatorId()).get().getDisplayName();
            // 发送 WebSocket 通知

            // 构建 KnowledgeApprovingResp 并发送到 `record` 主题
            KnowledgeRecordMessage knowledgeRecordMessage = new KnowledgeRecordMessage();
            knowledgeRecordMessage.setId(String.valueOf(saveModel.getId()));
            knowledgeRecordMessage.setKnowledgeId(String.valueOf(knowledgeId));
            knowledgeRecordMessage.setOperationTime(time);
            knowledgeRecordMessage.setDisplayName(displayName);
            knowledgeRecordMessage.setOperationResult(saveModel.getOperationResult());
            knowledgeRecordMessage.setResultName(saveModel.getOperationResult().getDescription());
            knowledgeRecordMessage.setReason(saveModel.getReason());

            WebSocketServer.broadcastToTopic(knowledgeRecordMessage, "record");

        } catch (DataAccessException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR, "数据库连接错误");
        } catch (JsonProcessingException e) {
            log.error("发送前端 websocket 消息失败，文件ID: {}, 知识ID: {}", knowledgeId, e);
            /*throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DOLPHIN_SCHEDULER_RESPONSE_ERROR, "调度引擎-调度websocket失败。");*/
        }
    }

    @Override
    @Transactional
    public void recordDelete(Long knowledgeBaseId) {
        List<KnowledgeModel> knowledgeList = knowledgeRepository.findByKbId(knowledgeBaseId);
        if (!knowledgeList.isEmpty()) {
            try {
                List<Long> knowledgeIds = knowledgeList.stream()
                        .map(KnowledgeModel::getId)
                        .collect(Collectors.toList());
                recordRepository.deleteByKnowledgeIdIn(knowledgeIds);
            } catch (DataAccessException e) {
                log.info("删除知识记录失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR, e, "数据库连接错误");
            }
        }
    }
}

