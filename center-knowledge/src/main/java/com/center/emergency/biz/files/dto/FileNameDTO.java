package com.center.emergency.biz.files.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FileNameDTO {
    @Schema(description = "文件名")
    private String fileName;
    @Schema(description = "文件数")
    private int fileNum;

    // 全参构造函数
    public FileNameDTO(String fileName, int fileNum) {
        this.fileName = fileName;
        this.fileNum = fileNum;
    }
}