package com.center.emergency.biz.robot.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class RobotStatusReq {

    //机器人ID
    @Schema(description = "机器人ID",example = "3")
    @NotNull(message = "机器人ID不为空")
    private Long id;

    @Schema(description = "机器人状态",example = "ACTIVE")
    @EnumValidate(message = "机器人状态不正确",value = CommonStatusEnum.class)
    @NotBlank(message = "机器人状态不能为空")
    private String robotStatus;
}
