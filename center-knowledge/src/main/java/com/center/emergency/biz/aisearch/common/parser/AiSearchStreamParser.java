package com.center.emergency.biz.aisearch.common.parser;

import com.center.emergency.biz.aisearch.common.persistence.AiSearchParsedEvent;

/**
 * AI搜索流解析器接口
 */
public interface AiSearchStreamParser {
    /**
     * 解析SSE事件数据
     * 
     * @param rawData  SSE返回的分段（字符串）
     * @param eventType SSE的事件名
     * @return 解析后的 AiSearchParsedEvent
     * @throws Exception 解析中出现的异常
     */
    AiSearchParsedEvent parse(String rawData, String eventType) throws Exception;

    /**
     * 处理敏感词情况，立即结束SSE
     *
     * @param message  需要处理的敏感词字符串
     */
    void handleSpamResponse(String message);
} 