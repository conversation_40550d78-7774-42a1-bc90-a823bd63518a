package com.center.emergency.biz.database.connector;

import com.center.emergency.common.enumeration.DataSourceEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;

public class DataConnectorFactory {

    public static DatabaseConnector getConnector(DataSourceEnum categoryEnum, String catalog, String databaseName, String host, Integer port, String userName, String password, String jdbcPara) {

        DatabaseConnector connector = null;

        switch (categoryEnum) {
            case MYSQL:
                connector = new MySqlConnector(host, port, databaseName, userName, password,jdbcPara);
                break;
            default:
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "数据源类型不存在");
        }
        return connector;
    }
}
