package com.center.emergency.biz.aisearch;

import com.center.emergency.biz.aisearch.pojo.SearchRequest;
import com.center.emergency.biz.aisearch.pojo.UpdateSessionTitleRequest;
import com.center.emergency.biz.aisearch.pojo.UpdateThumbsUpRequest;
import com.center.emergency.biz.aisearch.pojo.AiSearchSessionPageReq;
import com.center.emergency.biz.aisearch.pojo.AiSearchSessionPageResp;
import com.center.emergency.biz.aisearch.service.AiSearchService;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.hibernate.validator.constraints.Length;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

/**
 * AI搜索控制器
 */
@Tag(name = "AI搜索管理", description = "AI搜索相关接口")
@RestController
@RequestMapping("/api/ai-search")
@Validated
public class AiSearchController {
    
    private static final Logger log = LoggerFactory.getLogger(AiSearchController.class);
    
    @Autowired
    private AiSearchService aiSearchService;
    
    @Operation(summary = "执行AI搜索", 
               description = "支持全网搜索和企业内部搜索，返回SSE流式数据。" +
                            "企业内部搜索时，系统自动根据用户权限控制可访问的知识库范围：" +
                            "若指定kbIds，仅搜索用户有权限的知识库；" +
                            "若未指定，自动搜索用户所有有权限的知识库。")
    @PostMapping(value = "/search", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter search(@RequestBody @Valid SearchRequest request) {
        log.info("接收AI搜索请求: question={}, searchMode={}, sessionId={}", 
                request.getQuestion(), request.getSearchMode(), request.getSessionId());
        
        try {
            return aiSearchService.search(request);
        } catch (Exception e) {
            log.error("AI搜索失败: {}", e.getMessage(), e);
            SseEmitter emitter = new SseEmitter();
            emitter.completeWithError(e);
            return emitter;
        }
    }
    
    @Operation(summary = "获取会话历史", description = "获取指定会话的完整历史记录")
    @GetMapping("/sessions/{sessionId}/history")
    public CommonResult<List<Map<String, Object>>> getSessionHistory(
            @Parameter(description = "会话ID", required = true) 
            @PathVariable @Positive(message = "会话ID必须为正数") Long sessionId) {
        
        log.info("获取会话历史: sessionId={}", sessionId);
        
        try {
            List<Map<String, Object>> history = aiSearchService.getSessionHistory(sessionId);
            return CommonResult.success(history);
        } catch (Exception e) {
            log.error("获取会话历史失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.GET_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "删除会话", description = "删除指定会话及其所有关联数据")
    @PostMapping("/sessions/{sessionId}")
    public CommonResult<Void> deleteSession(
            @Parameter(description = "会话ID", required = true) 
            @PathVariable @Positive(message = "会话ID必须为正数") Long sessionId) {
        
        log.info("删除会话: sessionId={}", sessionId);
        
        try {
            aiSearchService.deleteSession(sessionId);
            return CommonResult.success();
        } catch (Exception e) {
            log.error("删除会话失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "分页查询会话列表", 
               description = "支持多条件筛选的分页查询，包括关键词、搜索模式等。" +
                            "返回格式化的时间和枚举描述，推荐使用此接口替代旧的查询接口。")
    @GetMapping("/sessions/page")
    public CommonResult<PageResult<AiSearchSessionPageResp>> pageUserSessions(
            @Parameter(description = "页码，从1开始", required = true)
            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小值为1") Integer pageNo,
            
            @Parameter(description = "每页条数，最大值为100", required = true)
            @RequestParam(defaultValue = "10") @Min(value = 1, message = "每页条数最小值为1") @Max(value = 100, message = "每页条数最大值为100") Integer pageSize,
            
            @Parameter(description = "会话标题关键词（模糊搜索）")
            @RequestParam(required = false) @Length(max = 100, message = "搜索关键词长度不能超过100个字符") String keyword,
            
            @Parameter(description = "搜索模式：1-全网搜索，2-企业内部搜索（可选，不传则查询所有）")
            @RequestParam(required = false) @Min(value = 1, message = "搜索模式必须为1或2") @Max(value = 2, message = "搜索模式必须为1或2") Integer searchMode,
            
            @Parameter(description = "搜索引擎类型（可选）")
            @RequestParam(required = false) @Length(max = 50, message = "搜索引擎类型长度不能超过50个字符") String searchEngine) {
        
        log.info("分页查询会话列表: pageNo={}, pageSize={}, keyword={}, searchMode={}, searchEngine={}", 
                pageNo, pageSize, keyword, searchMode, searchEngine);
        
        try {
            // 构造请求对象
            AiSearchSessionPageReq request = new AiSearchSessionPageReq();
            request.setPageNo(pageNo);
            request.setPageSize(pageSize);
            request.setKeyword(keyword);
            request.setSearchMode(searchMode);
            request.setSearchEngine(searchEngine);
            
            PageResult<AiSearchSessionPageResp> result = aiSearchService.pageUserSessions(request);
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("分页查询会话列表失败: {}", e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.GET_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "获取用户会话列表", description = "获取当前用户的所有会话列表，建议使用分页查询接口")
    @GetMapping("/sessions")
    @Deprecated
    public CommonResult<List<Map<String, Object>>> getUserSessions() {
        log.info("获取用户会话列表（已过时接口）");
        
        try {
            List<Map<String, Object>> sessions = aiSearchService.getUserSessions();
            return CommonResult.success(sessions);
        } catch (Exception e) {
            log.error("获取用户会话列表失败: error={}", e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.GET_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "根据搜索模式获取会话列表", description = "获取指定搜索模式的会话列表，建议使用分页查询接口")
    @GetMapping("/sessions/by-mode")
    @Deprecated
    public CommonResult<List<Map<String, Object>>> getUserSessionsByMode(
            @Parameter(description = "搜索模式：1-全网搜索，2-企业内部搜索", required = true) 
            @RequestParam @Min(value = 1, message = "搜索模式必须为1或2") @Max(value = 2, message = "搜索模式必须为1或2") Integer searchMode) {
        
        log.info("根据搜索模式获取会话列表（已过时接口）: searchMode={}", searchMode);
        
        try {
            List<Map<String, Object>> sessions = aiSearchService.getUserSessionsByMode(searchMode);
            return CommonResult.success(sessions);
        } catch (Exception e) {
            log.error("根据搜索模式获取会话列表失败: searchMode={}, error={}", searchMode, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.GET_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "获取消息详情", description = "获取指定消息的详细信息，包括关联的事件")
    @GetMapping("/messages/{messageId}")
    public CommonResult<Map<String, Object>> getMessageDetail(
            @Parameter(description = "消息ID", required = true) 
            @PathVariable @Positive(message = "消息ID必须为正数") Long messageId) {
        
        log.info("获取消息详情: messageId={}", messageId);
        
        try {
            Map<String, Object> messageDetail = aiSearchService.getMessageDetail(messageId);
            return CommonResult.success(messageDetail);
        } catch (Exception e) {
            log.error("获取消息详情失败: messageId={}, error={}", messageId, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.GET_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "更新消息点赞状态", description = "对助手回复进行点赞或点踩操作")
    @PostMapping("/messages/{messageId}/thumbs-up")
    public CommonResult<Void> updateMessageThumbsUp(
            @Parameter(description = "消息ID", required = true) 
            @PathVariable @Positive(message = "消息ID必须为正数") Long messageId,
            @RequestBody @Valid UpdateThumbsUpRequest request) {
        
        log.info("更新消息点赞状态: messageId={}, thumbsUp={}", messageId, request.getThumbsUp());
        
        try {
            aiSearchService.updateMessageThumbsUp(messageId, request.getThumbsUp());
            return CommonResult.success();
        } catch (Exception e) {
            log.error("更新消息点赞状态失败: messageId={}, thumbsUp={}, error={}", messageId, request.getThumbsUp(), e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "获取会话统计信息", description = "获取指定会话的统计信息")
    @GetMapping("/sessions/{sessionId}/stats")
    public CommonResult<Map<String, Object>> getSessionStats(
            @Parameter(description = "会话ID", required = true) 
            @PathVariable @Positive(message = "会话ID必须为正数") Long sessionId) {
        
        log.info("获取会话统计信息: sessionId={}", sessionId);
        
        try {
            Map<String, Object> stats = aiSearchService.getSessionStats(sessionId);
            return CommonResult.success(stats);
        } catch (Exception e) {
            log.error("获取会话统计信息失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.GET_OBJECT_ERROR.getCode(), e.getMessage());
        }
    }
    
    @Operation(summary = "更新会话标题", description = "更新指定会话的标题")
    @PostMapping("/sessions/{sessionId}/title")
    public CommonResult<Void> updateSessionTitle(
            @Parameter(description = "会话ID", required = true) 
            @PathVariable @Positive(message = "会话ID必须为正数") Long sessionId,
            @RequestBody @Valid UpdateSessionTitleRequest request) {
        
        log.info("更新会话标题: sessionId={}, title={}", sessionId, request.getTitle());
        
        try {
            aiSearchService.updateSessionTitle(sessionId, request.getTitle());
            return CommonResult.success();
        } catch (Exception e) {
            log.error("更新会话标题失败: sessionId={}, title={}, error={}", sessionId, request.getTitle(), e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR.getCode(), e.getMessage());
        }
    }
} 