package com.center.emergency.biz.files.persistence;


import com.center.emergency.common.enumeration.FileStatusEnum;
import com.center.framework.db.core.BaseTenantModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.*;

/**
 * 文件实体类，表示文件表结构
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "center_files")
public class FileModel extends BaseTenantModel {

    /**
     * 知识库ID
     */
    @Column(name = "kb_id", nullable = false)
    @Schema(description = "知识库ID")
    private Long kbId;

    /**
     * 模型文件库的文件ID
     */
    @Column(name = "ai_fileb_file_id")
    @Schema(description = "模型文件库的文件ID")
    private Long aiFilebFileId;

    /**
     * 文件名称
     */
    @Column(name = "file_name", nullable = false, length = 100)
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 原始文件名称
     */
    @Column(name = "original_name", nullable = false, length = 100)
    @Schema(description = "原始文件名称")
    private String originalName;

    /**
     * HDFS路径
     */
    @Column(name = "hdfs_path", length = 255)
    @Schema(description = "HDFS路径")
    private String hdfsPath;

    /**
     * 文件状态
     */
    @Column(name = "status", nullable = false, length = 50)
    @Schema(description = "文件状态")
    @Enumerated(value = EnumType.STRING)
    private FileStatusEnum status;

    /**
     * 文件重名序号
     */
    @Column(name = "file_num")
    @Schema(description = "文件重名序号")
    private Integer fileNum;

    /**
     * HDFS路径
     */
    @Column(name = "pdf_hdfs_path", length = 255)
    @Schema(description = "HDFS路径")
    private String pdfHdfsPath;

}