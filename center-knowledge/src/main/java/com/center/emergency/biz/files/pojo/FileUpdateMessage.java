package com.center.emergency.biz.files.pojo;

import com.center.emergency.biz.tag.pojo.TagFileRsp;
import com.center.emergency.biz.tag.pojo.TagUpdateReq;
import com.center.emergency.common.enumeration.FileStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class FileUpdateMessage {
    // 文件 ID
    private String id;
    // 文件状态
    private String status;

    private String statusName;

    private List<TagFileRsp> tags;

    private String updateTime;

    private String pdfPreviewPath;
}