package com.center.emergency.biz.robot.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

@Data
public class RobotModelResp extends RobotBase{
    @Schema(description = "机器人ID")
    private Long id;

    @Schema(description = "机器人状态",example = "ACTIVE")
    @EnumValidate(message = "机器人状态不正确",value = CommonStatusEnum.class)
    @NotBlank(message = "机器人状态不能为空")
    private CommonStatusEnum robotStatus;

    @EnumConvert(value = CommonStatusEnum.class,srcFieldName = "robotStatus")
    @Schema(description = "机器人状态名称",example = "激活")
    private String statusName;
}