package com.center.emergency.biz.datasource.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;

import java.util.List;

public interface DataSourceExtensionRepository extends JoinFetchCapableQueryDslJpaRepository<DataSourceExtensionModel,Long> {

    DataSourceExtensionModel getByDatasourceId(Long dataSourceId);

    List<DataSourceExtensionModel> getAllByDatasourceId(Long dataSourceId);

    void deleteAllByDatasourceId(Long dataSourceId);
}
