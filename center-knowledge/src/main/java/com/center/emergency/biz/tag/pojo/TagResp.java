package com.center.emergency.biz.tag.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 标签返回类
 * @date 2024/10/17 9:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TagResp extends TagBase {

    @Schema(description = "主键ID", example = "1")
    private Long id;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2024-10-16 18:06:00")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2024-10-17 12:06:00")
    private LocalDateTime updateTime;
}