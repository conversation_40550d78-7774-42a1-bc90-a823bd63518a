package com.center.emergency.biz.knowledge.converter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.center.emergency.biz.knowledge.pojo.QaImportResultResp;
import com.center.emergency.biz.knowledge.pojo.QaPreviewResp;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * QA导入响应转换器
 * 将算法接口的复杂响应转换为前端友好的详细响应
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class QaImportResponseConverter {

    /**
     * 转换算法check_faqs_duplicates响应为预览响应
     */
    public static QaPreviewResp convertToPreviewResp(JSONObject algorithmResp) {
        QaPreviewResp resp = new QaPreviewResp();
        
        Integer code = algorithmResp.getInteger("code");
        boolean isSuccess = code != null && code == 200;
        resp.setSuccess(isSuccess);
        resp.setMessage(algorithmResp.getString("msg"));
        
        if (!isSuccess) {
            resp.setCanImport(false);
            return resp;
        }
        
        // 转换统计信息
        JSONObject stats = algorithmResp.getJSONObject("stats");
        if (stats != null) {
            QaPreviewResp.PreviewStats previewStats = new QaPreviewResp.PreviewStats();
            previewStats.setNewCount(stats.getInteger("new_count"));
            previewStats.setDuplicateCount(stats.getInteger("duplicate_count"));
            previewStats.setErrorCount(stats.getInteger("error_count"));
            previewStats.setTotalCount(stats.getInteger("total_count"));
            resp.setStats(previewStats);
            
            // 设置是否可导入：必须有新增FAQ且无错误才可导入
            resp.setCanImport(previewStats.getNewCount() > 0);
        }
        
        // 转换详情为统一的预览项目列表（保留详细信息）
        JSONObject details = algorithmResp.getJSONObject("details");
        if (details != null) {
            List<QaPreviewResp.QaPreviewItem> allItems = new ArrayList<>();
            
            // 处理新增FAQ
            if (details.containsKey("new_faqs")) {
                JSONArray newFaqs = details.getJSONArray("new_faqs");
                List<QaPreviewResp.QaPreviewItem> newItems = newFaqs.stream()
                    .map(item -> convertToNewPreviewItem((JSONObject) item))
                    .collect(Collectors.toList());
                allItems.addAll(newItems);
            }

            // 处理重复FAQ（保留原始信息）
            if (details.containsKey("duplicate_faqs")) {
                JSONArray duplicateFaqs = details.getJSONArray("duplicate_faqs");
                List<QaPreviewResp.QaPreviewItem> duplicateItems = duplicateFaqs.stream()
                    .map(item -> convertToDuplicatePreviewItem((JSONObject) item))
                    .collect(Collectors.toList());
                allItems.addAll(duplicateItems);
            }

            // 处理错误FAQ
            if (details.containsKey("error_faqs")) {
                JSONArray errorFaqs = details.getJSONArray("error_faqs");
                List<QaPreviewResp.QaPreviewItem> errorItems = errorFaqs.stream()
                    .map(item -> convertToErrorPreviewItem((JSONObject) item))
                    .collect(Collectors.toList());
                allItems.addAll(errorItems);
            }
            
            // 按行号排序
            allItems.sort((a, b) -> Integer.compare(a.getRowNumber(), b.getRowNumber()));
            resp.setItems(allItems);
        }
        
        return resp;
    }

    /**
     * 转换算法upload_faqs响应为导入结果响应
     */
    public static QaImportResultResp convertToImportResultResp(JSONObject algorithmResp) {
        QaImportResultResp resp = new QaImportResultResp();
        
        Integer code = algorithmResp.getInteger("code");
        boolean isSuccess = code != null && code == 200;
        resp.setSuccess(isSuccess);
        resp.setMessage(algorithmResp.getString("msg"));
        resp.setStatus(isSuccess ? "SUCCESS" : "FAILED");
        
        // 转换统计信息
        JSONObject stats = algorithmResp.getJSONObject("stats");
        if (stats != null) {
            QaImportResultResp.ImportStats importStats = new QaImportResultResp.ImportStats();
            importStats.setNewCount(stats.getInteger("new_count"));
            importStats.setDuplicateCount(stats.getInteger("duplicate_count"));
            importStats.setErrorCount(stats.getInteger("error_count"));
            importStats.setTotalCount(stats.getInteger("total_count"));
            resp.setStats(importStats);
        }
        
        // 设置错误详情（只保留关键信息）
        JSONArray errorDetails = algorithmResp.getJSONArray("error_details");
        if (errorDetails != null && !errorDetails.isEmpty()) {
            List<QaImportResultResp.ErrorDetail> errors = errorDetails.stream()
                .map(item -> convertToErrorDetail((JSONObject) item))
                .collect(Collectors.toList());
            resp.setErrorDetails(errors);
        }
        
        return resp;
    }

    /**
     * 转换为新增预览项目
     */
    private static QaPreviewResp.QaPreviewItem convertToNewPreviewItem(JSONObject item) {
        QaPreviewResp.QaPreviewItem previewItem = new QaPreviewResp.QaPreviewItem();
        previewItem.setRowNumber(item.getInteger("index"));
        previewItem.setQuestion(item.getString("question"));
        previewItem.setAnswer(item.getString("answer"));
        previewItem.setStatus("NEW");
        
        // 使用算法返回的reason，如果没有则使用默认描述
        String reason = item.getString("reason");
        previewItem.setStatusDesc(reason != null ? reason : "新FAQ，可以导入");
        
        return previewItem;
    }

    /**
     * 转换为重复预览项目（保留原始FAQ信息）
     */
    private static QaPreviewResp.QaPreviewItem convertToDuplicatePreviewItem(JSONObject item) {
        QaPreviewResp.QaPreviewItem previewItem = new QaPreviewResp.QaPreviewItem();
        previewItem.setRowNumber(item.getInteger("index"));
        previewItem.setQuestion(item.getString("question"));
        previewItem.setAnswer(item.getString("answer"));
        previewItem.setStatus("DUPLICATE");
        
        // 使用算法返回的reason
        String reason = item.getString("reason");
        previewItem.setStatusDesc(reason != null ? reason : "与现有FAQ重复");
        
        // 对于重复的FAQ，将当前FAQ的问题和答案作为原始信息
        previewItem.setOriginalQuestion(item.getString("question"));
        previewItem.setOriginalAnswer(item.getString("answer"));
        
        return previewItem;
    }

    /**
     * 转换为错误预览项目
     */
    private static QaPreviewResp.QaPreviewItem convertToErrorPreviewItem(JSONObject item) {
        QaPreviewResp.QaPreviewItem previewItem = new QaPreviewResp.QaPreviewItem();
        previewItem.setRowNumber(item.getInteger("index"));
        previewItem.setQuestion(item.getString("question"));
        previewItem.setAnswer(item.getString("answer"));
        previewItem.setStatus("ERROR");
        
        // 设置错误信息
        String error = item.getString("error");
        previewItem.setErrorMsg(error);
        previewItem.setStatusDesc("数据错误：" + (error != null ? error : "未知错误"));
        
        return previewItem;
    }

    /**
     * 转换为错误详情
     */
    private static QaImportResultResp.ErrorDetail convertToErrorDetail(JSONObject item) {
        QaImportResultResp.ErrorDetail errorDetail = new QaImportResultResp.ErrorDetail();
        errorDetail.setRowNumber(item.getInteger("index"));
        errorDetail.setQuestion(item.getString("question"));
        errorDetail.setErrorMessage(item.getString("error"));
        return errorDetail;
    }
} 