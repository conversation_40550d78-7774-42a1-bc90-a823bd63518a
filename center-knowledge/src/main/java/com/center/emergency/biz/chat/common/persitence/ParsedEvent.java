package com.center.emergency.biz.chat.common.persitence;

import com.center.emergency.biz.chat.common.enumeration.ParsedEventType;
import lombok.Data;

/**
 * 解析后的事件对象
 */
@Data
public class ParsedEvent {
    // MESSAGE/END/SPAM/ERROR等
    private ParsedEventType eventType;
    // 原始SSE事件名
    private String originalEventType;
    // 解析出的文本
    private String content;
    // 是否结束
    private boolean end;
    // 原始数据(可选, 用于排查问题)
    private String rawData;
    //  可选,返回给前端
    private String conversationId;
}