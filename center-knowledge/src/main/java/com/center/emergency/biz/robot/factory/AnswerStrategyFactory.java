package com.center.emergency.biz.robot.factory;

import com.center.emergency.biz.robot.service.*;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 智能助手服务工厂类，根据智能助手回答策略获取对应的回复服务实例。
 */
@Component
public class AnswerStrategyFactory {

    @Resource(name = "MCP")
    private AnswerStrategyService mcp;

    @Resource(name = "DATABASE")
    private AnswerStrategyService database;

    @Resource(name = "FILE")
    private AnswerStrategyService file ;

    @Resource(name = "KNOWLEDGE_BASE")
    private AnswerStrategyService knowledgeBase;

    public AnswerStrategyService getAnswerStrategyService(AnswerStrategyEnum answerStrategy) {
        AnswerStrategyService answerStrategyService = null;
        if (answerStrategy==AnswerStrategyEnum.MCP){
            answerStrategyService = mcp;
        }else if(answerStrategy==AnswerStrategyEnum.DATABASE){
            answerStrategyService = database;
        }else if(answerStrategy==AnswerStrategyEnum.FILE){
            answerStrategyService = file;
        }else if(answerStrategy==AnswerStrategyEnum.KNOWLEDGE_BASE){
            answerStrategyService = knowledgeBase;
        }
        return answerStrategyService;
    }
}
