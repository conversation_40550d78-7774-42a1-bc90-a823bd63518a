package com.center.emergency.biz.webskt;

import com.center.emergency.biz.files.pojo.FileUpdateMessage;
import com.center.framework.web.jwt.JwtTokenProvider;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@ServerEndpoint("/ws")
@Component
@Slf4j
public class WebSocketServer {
    private static ConcurrentHashMap<String, Session> sessions = new ConcurrentHashMap<>();
    // 存储每个会话的主题
    private static ConcurrentHashMap<String, String> sessionTopics = new ConcurrentHashMap<>();
    // Token 校验
    private static JwtTokenProvider jwtTokenProvider;

    @Autowired
    public void setJwtTokenProvider(JwtTokenProvider jwtTokenProvider) {
        WebSocketServer.jwtTokenProvider = jwtTokenProvider;
    }

    // 新增方法：检查 token 是否有效
    private boolean isTokenValid(String token) {
        return jwtTokenProvider.validateToken(token);
    }

    // 发送指定主题的消息到所有订阅者
    public static void sendToTopic(String topic, String message) {
        for (Session session : sessions.values()) {
            String sessionTopic = sessionTopics.get(session.getId());
            if (session.isOpen() && topic.equals(sessionTopic)) {
                try {
                    session.getBasicRemote().sendText(message);
                } catch (IOException e) {
                    log.error("Error sending message to session ID: " + session.getId(), e);
                }
            }
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        Map<String, List<String>> parameters = session.getRequestParameterMap();
        List<String> tokenList = parameters.get("token");

        if (tokenList == null || tokenList.isEmpty() || !isTokenValid(tokenList.get(0))) {
            log.warn("Unauthorized WebSocket connection attempt, session ID: {}", session.getId());
            try {
                session.close(); // 关闭未授权的连接
            } catch (IOException e) {
                log.error("Error closing unauthorized session: " + session.getId(), e);
            }
            return;
        }

        sessions.put(session.getId(), session);
        log.info("Connection opened, session ID: {}", session.getId());
        try {
            session.getBasicRemote().sendText("Welcome! You are connected.");
        } catch (IOException e) {
            log.error("Error sending welcome message to session: " + session.getId(), e);
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("Received message from session ID {}: {}", session.getId(), message);

        // 前端传递的消息格式为：{"topic": "file"} 进行订阅
        try {
            Map<String, String> messageMap = new ObjectMapper().readValue(message, Map.class);

            String messageType = messageMap.get("type");

            // 以ping'作为消息心跳机制
            if ("ping".equals(messageType)) {
                log.info("Received heartbeat ping from session ID: {}", session.getId());
                return;
            }
            // 获取主题
            String topic = messageMap.get("topic");
            // 将主题和 session ID 关联
            sessionTopics.put(session.getId(), topic);

            // 发送消息通知当前客户端已订阅
            session.getBasicRemote().sendText("Subscribed to topic: " + topic);
            log.info("Session ID {} subscribed to topic: {}", session.getId(), topic);
        } catch (Exception e) {
            log.error("Error processing message from session ID: " + session.getId(), e);
        }
    }

    @OnClose
    public void onClose(Session session) {
        sessions.remove(session.getId());
        // 移除主题信息
        sessionTopics.remove(session.getId());
        log.info("Connection closed, session ID: {}", session.getId());
    }

    // 发送消息到指定主题
    public static void broadcastToTopic(Object message, String topic) throws JsonProcessingException {
        String jsonMessage = convertToJson(message);
        log.info("Broadcasting message to topic {}: {}", topic, jsonMessage);
        sendToTopic(topic, jsonMessage);
    }

    // 将任意对象转换为 JSON 字符串
    private static String convertToJson(Object message) throws JsonProcessingException {
        return new ObjectMapper().writeValueAsString(message);
    }
}
