package com.center.emergency.biz.files.pojo;

import com.netease.yidun.sdk.core.validation.limitation.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "云端文件上传请求")
public class CloudFileUploadReq {

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", example = "12345")
    private Long kbId;

    @Schema(description = "文件路径列表")
    private List<String> filePaths;

    @Schema(description = "目录路径列表")
    private List<String> dirPaths;
}