package com.center.emergency.biz.aisearch.common.listener;

import com.center.emergency.biz.aisearch.common.AiSearchUtils;
import com.center.emergency.biz.aisearch.common.persistence.AiSearchParsedEvent;
import com.center.emergency.biz.aisearch.common.parser.AiSearchStreamParser;
import com.center.framework.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * AI搜索协议层通用 SSE 监听器：
 * <pre>
 *   1. 负责将 OkHttp 回调转换为 AiSearchParsedEvent
 *   2. 统一管理 complete / timeout / failure
 *   3. 子类重写 {@link #handleParsedEvent(AiSearchParsedEvent, EventSource)} 处理业务
 * </pre>
 */
@Slf4j
public class AiSearchGenericSSEListener extends EventSourceListener implements AiSearchUtils.CountDownLatchHolder {

    protected final SseEmitter emitter;
    protected final AiSearchStreamParser parser;
    protected final CountDownLatch latch;
    protected final AtomicBoolean completed = new AtomicBoolean(false);

    public AiSearchGenericSSEListener(SseEmitter emitter,
                                     AiSearchStreamParser parser,
                                     CountDownLatch latch) {
        this.emitter = emitter;
        this.parser = parser;
        this.latch = latch;

        /* 监听 Spring 端超时 / 完成 */
        emitter.onTimeout(() -> {
            log.warn("AI搜索 SseEmitter 超时，强制完成");
            complete();
        });
        emitter.onCompletion(() -> {
            log.info("AI搜索 SseEmitter 完成");
            completed.set(true);
        });
        emitter.onError(ex -> {
            log.error("AI搜索 SseEmitter 错误: {}", ex.getMessage());
            completed.set(true);
        });
    }

    /* ----------------- OkHttp 回调 ----------------- */

    @Override
    public void onOpen(EventSource es, Response res) {
        log.info("AI搜索 SSE 连接已建立: {}", res.request().url());
    }

    @Override
    public void onEvent(EventSource es, String id, String eventType, String data) {
        if (completed.get()) {
            log.warn("AI搜索 SSE 已完成，忽略事件: {}", eventType);
            return;
        }

        try {
            log.debug("收到AI搜索 SSE 事件: type={}, data={}", eventType, data);
            AiSearchParsedEvent event = parser.parse(data, eventType);
            handleParsedEvent(event, es);
        } catch (Exception ex) {
            log.error("解析AI搜索 SSE 数据异常: {}", ex.getMessage(), ex);
            sendError("解析失败：" + ex.getMessage());
            es.cancel();
        }
    }

    @Override
    public void onClosed(EventSource es) {
        log.info("AI搜索 SSE 连接已由服务端关闭");
        complete();
    }

    @Override
    public void onFailure(EventSource es, Throwable t, Response res) {
        String errorMsg = t != null ? t.getMessage() : "未知错误";
        log.error("AI搜索 SSE 连接失败: {}", errorMsg, t);
        
        if (res != null) {
            log.error("失败响应状态: code={}, message={}", res.code(), res.message());
        }
        
        sendError("SSE 通信失败: " + errorMsg);
    }

    /* ----------------- 供子类复写 ----------------- */

    /**
     * 子类根据事件类型进行业务处理
     */
    protected void handleParsedEvent(AiSearchParsedEvent event, EventSource es) throws IOException {
        // 默认实现为空，由子类重写
    }

    /* ----------------- 公共辅助 ----------------- */

    /**
     * 发送事件到前端
     */
    protected void sendEvent(String name, Object data) {
        if (completed.get()) {
            log.warn("AI搜索 SSE 已完成，忽略事件发送: {}", name);
            return;
        }

        try {
            log.debug("发送AI搜索事件: name={}, data={}", name, data);
            emitter.send(SseEmitter.event().name(name).data(data));
        } catch (IOException io) {
            log.error("发送AI搜索事件失败: name={}, error={}", name, io.getMessage());
            complete();
        }
    }

    /**
     * 发送错误事件
     */
    protected void sendError(String message) {
        log.error("发送AI搜索错误事件: {}", message);
        sendEvent("error", CommonResult.error(500, message));
        complete();
    }

    /**
     * 完成 SSE 连接
     */
    protected void complete() {
        if (completed.compareAndSet(false, true)) {
            try {
                log.debug("完成AI搜索 SSE 连接");
                emitter.complete();
            } catch (Exception e) {
                log.error("完成AI搜索 SSE 连接失败: {}", e.getMessage());
            }
            // 确保在任何完成路径都释放latch
            if (latch != null) {
                latch.countDown();
                log.debug("Latch已释放");
            }
        }
    }

    @Override
    public CountDownLatch getLatch() {
        return latch;
    }
} 