package com.center.emergency.biz.modelgroup.persistence;

import com.center.emergency.common.enumeration.SourceTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ModelGroupRepository extends JpaRepository<ModelGroup, Long> {
    boolean existsByGroupNameAndTenantId(String groupName,Long tenantId);

    boolean existsByGroupNameAndSourceType(String groupName, SourceTypeEnum sourceTypeEnum);
}
