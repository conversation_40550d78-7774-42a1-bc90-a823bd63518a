package com.center.emergency.biz.modelgroup.persistence;

import com.center.emergency.common.enumeration.SourceTypeEnum;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Repository
public interface ModelGroupRepository extends JpaRepository<ModelGroup, Long> {
    boolean existsByGroupNameAndTenantId(String groupName,Long tenantId);
}
