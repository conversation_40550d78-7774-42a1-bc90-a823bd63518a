package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.persistence.QChatQuestionModel;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.files.persistence.QFileModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.EventTypeEnum;
import com.center.framework.common.pojo.IdAndValue;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component("FILE")
@Slf4j
public class FileServiceForAnswerStrategy extends AbstractAnswerStrategy implements AnswerStrategyService{

    @Resource
    private ModelGroupService modelGroupService;

    @Override
    public List<Long> listAnswerStrategyIds(Long robotId) {
        // 文件策略不从机器人配置中获取文件列表，而是从会话历史中动态获取
        // 这里返回空列表，实际文件ID在对话时从会话历史中获取
        log.info("文件策略不从机器人配置中获取文件列表，文件ID将从会话历史中动态获取");
        return Collections.emptyList();
    }

    /**
     * 文件策略的参数构建 - 实现文件特定的参数构建逻辑
     */
    @Override
    public HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                                   List<Map<String, String>> tags, Boolean isSimulate,
                                                   Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("构建文件策略对话参数: chatVO={}, fileIds.size={}, modelId={}, isSimulate={}",
                chatVO.getQuestion(), fileIds.size(), modelId, isSimulate);

        // 1. 收集当前会话中的文件ID
        List<String> robotFileIds = collectFileIdsFromSession(chatVO.getSessionId(), jpaQueryFactory);

        // 2. 构建文件策略基础参数
        HashMap<String, Object> param = buildFileBaseParams(chatVO, robotFileIds);

        // 3. 查询机器人的systemPrompt作为user_custom_prompt
        String userCustomPrompt = queryRobotSystemPrompt(chatVO.getRobotId(), jpaQueryFactory);
        param.put("user_custom_prompt", userCustomPrompt);

        // 4. 查询并添加模型配置
        if (modelId != null) {
            RobotModelDTO robotModelDTO = queryRobotModelConfig(chatVO.getRobotId(), modelId, jpaQueryFactory);
            // 文件策略只需要基础模型配置，不需要策略特有参数
            addBaseModelConfigParams(param, robotModelDTO);
        } else {
            // 使用系统默认模型配置
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            param.putAll(defaultModelConfig);
        }

        log.info("文件策略参数构建完成，file_ids数量: {}", robotFileIds.size());
        return param;
    }

    /**
     * 文件策略的资源收集和参数构建 - 实现文件特定的参数构建逻辑
     */
    @Override
    public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                         Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
        log.info("文件策略自主收集资源并构建参数: chatVO={}, modelGroupId={}, isSimulate={}",
                chatVO.getQuestion(), modelGroupId, isSimulate);

        // 1. 收集当前会话中的文件ID
        List<String> fileIds = collectFileIdsFromSession(chatVO.getSessionId(), jpaQueryFactory);

        // 2. 构建文件策略基础参数
        HashMap<String, Object> param = buildFileBaseParams(chatVO, fileIds);

        // 3. 查询机器人的systemPrompt作为user_custom_prompt
        String userCustomPrompt = queryRobotSystemPrompt(chatVO.getRobotId(), jpaQueryFactory);
        param.put("user_custom_prompt", userCustomPrompt);

        // 4. 添加模型组配置
        addModelGroupParams(param, modelGroupId);

        log.info("文件策略参数构建完成，file_ids数量: {}", fileIds.size());
        return param;
    }

    /**
     * 构建文件策略的基础参数
     * 包含文件策略特有的参数格式
     */
    private HashMap<String, Object> buildFileBaseParams(ChatVO chatVO, List<String> fileIds) {
        HashMap<String, Object> param = buildCommonBaseParams(chatVO);

        // 文件策略特有参数
        param.put("file_ids", fileIds);
        param.put("question", chatVO.getQuestion());
        param.put("streaming", 1); // 统一使用1而不是true
        // user_custom_prompt需要从数据库查询，这里先设置为null，由调用方设置
        param.put("user_custom_prompt", null);

        return param;
    }



    /**
     * 从当前会话中收集文件ID（根据session获取FILE类型的消息）
     */
    private List<String> collectFileIdsFromSession(Long sessionId, JPAQueryFactory jpaQueryFactory) {
        QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;

        // 查询当前会话中消息类型为FILE的记录，按创建时间倒序，取最新的10个
        List<String> fileContents = jpaQueryFactory
                .select(qChatQuestionModel.content)
                .from(qChatQuestionModel)
                .where(qChatQuestionModel.sessionId.eq(sessionId)
                        .and(qChatQuestionModel.eventType.eq(EventTypeEnum.FILE)))
                .orderBy(qChatQuestionModel.createTime.desc())
                .limit(10)
                .fetch();

        log.info("从会话{}中收集到{}个文件ID", sessionId, fileContents.size());
        return fileContents;
    }



    /**
     * 查询机器人的systemPrompt作为user_custom_prompt
     * 如果没有配置则返回null
     */
    private String queryRobotSystemPrompt(Long robotId, JPAQueryFactory jpaQueryFactory) {
        QRobotModel qRobotModel = QRobotModel.robotModel;

        String systemPrompt = jpaQueryFactory
                .select(qRobotModel.systemPrompt)
                .from(qRobotModel)
                .where(qRobotModel.id.eq(robotId))
                .fetchOne();

        log.info("查询机器人{}的systemPrompt作为user_custom_prompt: {}", robotId,
                systemPrompt != null ? "已配置" : "未配置");

        return systemPrompt;
    }
}
