package com.center.emergency.biz.knowledge.pojo;

import com.center.emergency.common.enumeration.FileStatusEnum;
import com.center.emergency.common.enumeration.KnowledgeStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDateTime;

/**
 * 知识响应类
 * <AUTHOR>
 */
@Data
public class KnowledgeResp {
    // 知识ID
    @Schema(description = "知识ID")
    private Long id;
    // 知识库ID
    @Schema(description = "知识库ID")
    private Long kbId;
    // 关联文件ID
    @Schema(description = "关联文件ID")
    private Long fileId;
    // 模型知识库的知识ID
//    @Schema(description = "模型知识库的知识ID")
//    private Long aiFaqbFaqId;
//    // 模型文件库的文件ID
//    @Schema(description = "模型文件库的文件ID")
//    private Long aiFilebFileId;
    // 知识名称
    @Schema(description = "知识名称")
    private String knowledgeName;
    // 知识状态
    @Schema(description = "知识状态")
    private KnowledgeStatusEnum status;
    // 知识状态名
    @Schema(description = "知识状态名")
    @EnumConvert(value = KnowledgeStatusEnum.class,srcFieldName = "status")
    private String statusName;
    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}