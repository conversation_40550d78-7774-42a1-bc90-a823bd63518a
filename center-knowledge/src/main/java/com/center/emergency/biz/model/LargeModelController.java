package com.center.emergency.biz.model;

import com.center.emergency.biz.model.pojo.*;
import com.center.emergency.biz.model.persistence.LargeModel;
import com.center.emergency.biz.model.service.LargeModelService;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "大模型管理")
@RequestMapping("/largeModel")
@Validated
public class LargeModelController {

    @Resource
    private LargeModelService largeModelService;

    @PostMapping("/create")
    @Operation(summary = "创建大模型")
    @EnumConvertPoint
    public CommonResult<LargeModelResp> createLargeModel(@RequestBody @Validated LargeModelCreateReq req) {
        return CommonResult.success(largeModelService.createLargeModel(req));
    }

    @PostMapping("/update")
    @Operation(summary = "更新大模型")
    @EnumConvertPoint
    public CommonResult<LargeModelResp> updateLargeModel(@RequestBody @Validated LargeModelUpdateReq req) {
        return CommonResult.success(largeModelService.updateLargeModel(req));
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除大模型")
    public CommonResult<Boolean> deleteLargeModel(@PathVariable("id") Long id) {
        largeModelService.deleteLargeModel(id);
        return CommonResult.success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询大模型")
    @EnumConvertPoint
    public CommonResult<PageResult<PageLargeModelWithRobotsResp>> pageLargeModels(
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String keyword) {

        LargeModelPageReq req = new LargeModelPageReq();
        req.setPageNo(pageNo);
        req.setPageSize(pageSize);
        req.setKeyword(keyword);
        return CommonResult.success(largeModelService.pageLargeModels(req));
    }

    @GetMapping("/list-all")
    @Operation(summary = "查询所有大模型（下拉列表）")
    public CommonResult<List<SelectListLargeModelResp>> listAllLargeModels() {
        return CommonResult.success(largeModelService.listAllModelsForSelect());
    }
    @PostMapping("/test-list")
    @Operation(summary = "测试服务是否正常并且获取可用模型列表")
    public CommonResult<List<AvailableModelResp>> testModelList(@RequestBody LargeModelPingReq req) {
        return CommonResult.success(largeModelService.getAvailableModels(OrikaUtils.convert(req,LargeModelCreateReq.class)));
    }

    @GetMapping("/usage/{id}")
    @Operation(summary = "查询模型被机器人引用情况")
    @EnumConvertPoint
    public CommonResult<LargeModelUsageResp> getModelUsage(@PathVariable("id") Long id) {
        return CommonResult.success(largeModelService.getModelUsageInfo(id));
    }
    @PostMapping("/toggle-status/{id}")
    @Operation(summary = "切换模型启用状态")
    @EnumConvertPoint
    public CommonResult<LargeModelWithRobotsResp> toggleModelStatus(@PathVariable Long id) {
        return CommonResult.success(largeModelService.toggleModelStatus(id));
    }
    @GetMapping("/{id}")
    @Operation(summary = "查询模型详情（含机器人）")
    @EnumConvertPoint
    public CommonResult<LargeModelWithRobotsResp> getModelDetail(@PathVariable("id") Long id) {
        return CommonResult.success(largeModelService.getModelDetail(id));
    }

}
