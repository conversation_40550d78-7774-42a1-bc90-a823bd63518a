package com.center.emergency.biz.model.persistence;



import com.center.framework.common.enumerate.CommonStatusEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LargeModelRepository extends JpaRepository<LargeModel, Long> {
    boolean existsByModelDisplayNameAndTenantId(String modelDisplayName, Long tenantId);


    List<LargeModel> findByTenantId(Long tenantId);


}
