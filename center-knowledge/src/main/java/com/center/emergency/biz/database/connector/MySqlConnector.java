package com.center.emergency.biz.database.connector;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import java.sql.*;
import java.util.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MySqlConnector extends AbstractDatabaseConnector {

    private String url;
    private String user;
    private String password;
    private Connection connection;

    public MySqlConnector(String host,Integer port,String databaseName,String userName, String password, String jdbcPara) {
        this.url = "jdbc:mysql://" + host + ":" + port + "/" + (databaseName != null ? databaseName : "" );
        // 如果 jdbcPara 不为空，则将其附加到 URL 末尾
        if (jdbcPara != null && !jdbcPara.isEmpty()) {
            this.url += "?" + jdbcPara;
        }
        this.user = userName;
        this.password = password;
    }


    @Override
    public Connection getConnection()  {
        try {
            if(connection==null || connection.isClosed()){
                this.connection= DriverManager.getConnection(url, user, password);
            }
            return connection;
        }catch (Exception e){
            log.error("MySql数据库连接出错!",e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR);
        }
    }

    @Override
    public List getTables(String databaseName) {
        try {
            ResultSet resultSet = getConnection().getMetaData().getTables(databaseName, null, "%", new String[]{"TABLE"});
            if (!resultSet.next()) {
              throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "数据库不存在");
            }
            resultSet.beforeFirst();
            return getTableNameList(resultSet,databaseName);
        } catch (SQLException e) {
            //表异常处理
            log.error("Mysql获取表清单出错!",e);
            String message = e.getMessage();
            if (message.contains("Communications link failure")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR);
            } else if (message.contains("Access denied for user")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN);
            } else if (message.contains("Unknown database")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "数据库不存在");
            } else if (message.contains("No database selected")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DATABASE_NOT_SELECTED_ERROR);
            } else {
                throw ServiceExceptionUtil.exception(e.getErrorCode(),e, e.getMessage());
            }
        }finally {
            try {
                getConnection().close();
            } catch (SQLException e) {
                log.error("Mysql关闭连接出错!", e);
            }
        }
    }

    @Override
    public List getColumns(DatabaseConnector connector,String databaseName, String tableName)  {
        try {
            ResultSet resultSet = getConnection().getMetaData().getColumns(databaseName, null, tableName, null);
            if (!resultSet.next()) {
              throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "表不存在");
            }
            resultSet.beforeFirst();
            return getColumnList(connector,resultSet,getKeys(databaseName,tableName));
        } catch (SQLException e) {
            log.error("Mysql获取列清单出错!",e);
            //列异常处理
            String message=e.getMessage();
            if(message.contains("Communications link failure")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR);
            }
            else if(message.contains("Access denied for user")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN);
            }
            else if(message.contains("Table")&&message.contains("doesn't exist")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "表不存在");
            }
            else if(message.contains("Lost connection to MySQL server during query")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_LOST_ERROR);
            }
            else{
                throw ServiceExceptionUtil.exception(e.getErrorCode(),e,e.getMessage());
            }
        }finally {
            try {
                getConnection().close();
            } catch (SQLException e) {
                log.error("Mysql关闭连接出错!", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CLOSE_DATABASE_ERROR);
            }
        }
    }

    public ResultSet getKeys(String databaseName, String tableName)  {
        try {
            return getConnection().getMetaData().getPrimaryKeys(databaseName, null, tableName);
        } catch (SQLException e) {
            log.error("Mysql获取主键出错!",e);
            //主键异常处理
            String message=e.getMessage();
            if(message.contains("Communications link failure")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR);
            }
            else if(message.contains("Table")&&message.contains("not found")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "表不存在");
            }
            else if(message.contains("Connection is closed")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_CLOSE_ERROR);
            }
            else if(message.contains("Invalid column name")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "列不存在");
            }
            else{
                throw ServiceExceptionUtil.exception(e.getErrorCode(),e,e.getMessage());
            }
        }
    }

    /**
     * 检查数据库连接
     *
     * @return
     * @throws SQLException
     */
    @Override
    public void checkConnection()  {
        Connection connection = null;
        try {
            // 尝试建立数据库连接
            connection = DriverManager.getConnection(url, user, password);
            log.info("数据库连接成功!");
        } catch (SQLException e) {
            log.error("Mysql连接出错!",e);
            // 捕获并分析 SQLException
            String message = e.getMessage();
            if (message.contains("Access denied for user")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.USERANDPASSWORD_ERROR);
            } else if (message.contains("Unknown database")) {
                throw  ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "数据库不存在");
            } else if (message.contains("Communications link failure")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_ERROR);
            } else if (message.contains("Connection timed out")){
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CONNECTION_TIMEOUT_ERROR);
            } else if (message.contains("No suitable driver found")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DRIVER_NOT_FOUND_ERROR);
            }
            else if (message.contains("WrongArgumentException")) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "数据库名参数错误");
            } else {
                throw ServiceExceptionUtil.exception(e.getErrorCode(),e,e.getMessage());
            }
        } finally {
            // 清理环境
            try {
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭数据库连接出错!",e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CLOSE_DATABASE_ERROR);
            }
        }
    }

    @Override
    public String getSql(String tableName) {
        return "SELECT * FROM " + "`" + tableName + "`"+" LIMIT 100";
    }

    @Override
    public void close() {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.error("关闭数据库连接出错!",e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CLOSE_DATABASE_ERROR);
            }
        }
    }

    /**
     * 数据类型由数字转换为对应字符串表示
     * @param dataType
     * @return
     */
    @Override
    public String convertDataType(int dataType) {
        switch (dataType) {
            case Types.VARCHAR:
                return "Varchar";
            case Types.CHAR:
                return "Char";
            case Types.BIGINT:
                return "BigInt";
            case Types.SMALLINT:
                return "SmallInt";
            case Types.TINYINT:
                return "TinyInt";
            case Types.INTEGER:
                return "Integer";
            case Types.DOUBLE:
                return "Double";
            case Types.FLOAT:
                return "Float";
            case Types.DATE:
                return "Date";
            case Types.TIME:
                return "Time";
            case Types.TIMESTAMP:
                return "Timestamp";
            case Types.BOOLEAN:
                return "Boolean";
            default:
                return "Unknown Type";
        }
    }

    @Override
    public String toString() {
        return "MySqlConnector{" +
                "url='" + url + '\'' +
                ", user='" + user + '\'' +
                ", password='******' + " +
                ", connection=" + connection +
                '}';
    }
}
