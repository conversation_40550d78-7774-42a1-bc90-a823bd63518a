package com.center.emergency.auth.request;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class LoginInReq {
  @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "99999999999")
  @NotBlank(message = "手机号不能为空")
  private String loginName;

  @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "p%SS0510")
  @NotBlank(message = "密码不能为空")
  private String loginPassword;
}
