package com.center.emergency.auth;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.center.cache.factory.CacheFactory;
import com.center.infrastructure.system.biz.user.pojo.UserInfo;
import com.center.emergency.auth.request.LoginInReq;
import com.center.emergency.auth.respeonse.LoginResp;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.jwt.JwtTokenProvider;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.role.persistence.QRoleModel;
import com.center.infrastructure.system.biz.tenant.persitence.QTenantModel;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.center.infrastructure.system.biz.user.persistence.QUserRoleModel;
import com.center.infrastructure.system.biz.user.service.UserService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.INACTIVE_OBJECT;
import static com.center.framework.web.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户认证")
@RestController
@RequestMapping("/admin/auth")
@Validated
public class AuthController {

    @Resource
    JwtTokenProvider jwtTokenProvider;

    @Resource
    private UserService userService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private CacheFactory cacheFactory;

    @Operation(summary = "用户登录接口")
    @ResponseBody
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public CommonResult<LoginResp> load(@Valid @RequestBody LoginInReq loginInReq,
                                        HttpServletRequest request) {
        QUserModel qUserModel = QUserModel.userModel;
        QUserRoleModel qUserRoleModel = QUserRoleModel.userRoleModel;
        QRoleModel qRoleModel = QRoleModel.roleModel;
        QTenantModel qTenantModel = QTenantModel.tenantModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUserRoleModel.userId.eq(qUserModel.id));
        builder.and(qUserModel.phoneNumber.eq(loginInReq.getLoginName())).or(qUserModel.username.eq(loginInReq.getLoginName()));
        builder.and(qUserModel.password.eq(SecureUtil.md5(loginInReq.getLoginPassword())));
        UserInfo userInfo = jpaQueryFactory.select(Projections.bean(
                        UserInfo.class,
                        qUserModel.id.as("userId"),
                        qUserModel.username.as("userName"),
                        qUserModel.displayName,
                        qUserModel.departId,
                        qUserModel.tenantId,
                        qRoleModel.id.as("roleId"),
                        qRoleModel.code,
                        qTenantModel.status.as("tenantStatus")))
                .from(qUserModel)
                .leftJoin(qTenantModel).on(qTenantModel.id.eq(qUserModel.tenantId))
                .leftJoin(qUserRoleModel).on(qUserRoleModel.userId.eq(qUserModel.id))
                .leftJoin(qRoleModel).on(qRoleModel.id.eq(qUserRoleModel.roleId))
                .where(builder)
                .fetchFirst();
        if (userInfo == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_USERNAME_PASSWORD, "用户名或密码错误");
        } else {
            if (!userInfo.getTenantStatus().equals(CommonStatusEnum.ACTIVE)) {
                throw ServiceExceptionUtil.exception(INACTIVE_OBJECT, "此公司已停用");
            }
        }
        LoginContextHolder.setLoginUserId(userInfo.getUserId());
        LoginContextHolder.setLoginUserTenantId(userInfo.getTenantId());
        LoginContextHolder.setLoginUserDepartId(userInfo.getDepartId());
        userService.updateUserLoginInfo(userInfo.getUserId(), ServletUtil.getClientIP(request));
        String token = jwtTokenProvider.createToken(userInfo.getUserId(), userInfo.getTenantId(), userInfo.getDepartId());
        userService.saveLoginInfo();
        return success(new LoginResp(token, userInfo.getUserId(), userInfo.getUserName(), userInfo.getDisplayName(), userInfo.getCode(), userInfo.getCode().getDescription()));
    }

    @Operation(summary = "获取用户登录信息")
    @ResponseBody
    @EnumConvertPoint
    @GetMapping(value = "/get_login_info")
    public CommonResult<UserInfo> getLoginInfo() {
        return CommonResult.success((UserInfo) cacheFactory.getHashCache().get(LoginContextHolder.getLoginUserId().toString()));
    }
}
