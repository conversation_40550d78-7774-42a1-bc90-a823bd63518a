package com.center.emergency.auth.respeonse;

import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Schema(description = "管理后台 - 联系人 Response VO")
@Data
@AllArgsConstructor
public class LoginResp {

  private static final long serialVersionUID = -6815327523262077776L;

  @Schema(description = "Token", example = "1feaab34fde94f58a461b9fbd30161be")
  private String token;

  @Schema(description = "用户ID", example = "1")
  private Long userId;

  @Schema(description = "用户名", example = "admin")
  private String loginName;

  @Schema(description = "显示名")
  private String displayName;

  @Schema(description = "角色")
  private RoleEnum role;

  @Schema(description = "角色中文名称")
  private String roleName;
}
