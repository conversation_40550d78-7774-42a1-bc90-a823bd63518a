package com.center.emergency.common.enumeration;


import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/9/2 15:48
 */
@AllArgsConstructor
public enum DataSourceEnum implements IEnumerate<String> {

    MYSQL("MYSQL","MYSQL");
    private String value;
    private String description;


    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
