package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum RecordStatusEnum implements IEnumerate<String> {

    CREATE("CREATE", "创建"),
    APPROVAL("APPROVAL", "提审"),
    MODIFY("MODIFY", "修改"),
    PASS("PASS", "审批通过"),
    REFUSE("REFUSE", "审批拒绝"),
    ENABLED("ENABLED", "启用"),
    DISABLED("DISABLED", "停用"),
    ;

    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
