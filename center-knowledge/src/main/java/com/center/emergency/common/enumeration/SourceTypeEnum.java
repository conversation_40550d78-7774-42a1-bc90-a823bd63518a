package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于表示是系统内置还是用户自定义
 * @date 2024/10/16 18:12
 */
@AllArgsConstructor
public enum SourceTypeEnum implements IEnumerate<String> {
    SYSTEM("SYSTEM", "内置模型"),
    CUSTOM("CUSTOM", "用户自定义");

    private String value;
    private String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
