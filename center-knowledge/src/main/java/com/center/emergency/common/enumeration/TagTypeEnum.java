package com.center.emergency.common.enumeration;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/10/16 18:12
 */
@AllArgsConstructor
public enum TagTypeEnum {
    SYSTEM("SYSTEM", "系统标签"),
    CUSTOM("CUSTOM", "自定义标签");

    private String value;
    private String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
