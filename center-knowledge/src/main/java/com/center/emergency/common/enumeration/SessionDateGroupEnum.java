package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 会话日期分组枚举
 * 用于将会话按时间分组显示
 */
@AllArgsConstructor
public enum SessionDateGroupEnum implements IEnumerate<String> {
    TODAY("TODAY", "今天"),
    YESTERDAY("YESTERDAY", "昨天"),
    THIS_WEEK("THIS_WEEK", "本周"),
    LAST_WEEK("LAST_WEEK", "上周"),
    THIS_MONTH("THIS_MONTH", "本月"),
    EARLIER("EARLIER", "更早");

    private final String value;
    private final String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * 根据时间判断属于哪个日期分组
     * @param dateTime 要判断的时间
     * @return 对应的日期分组枚举
     */
    public static SessionDateGroupEnum getDateGroup(LocalDateTime dateTime) {
        if (dateTime == null) {
            return EARLIER;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfToday = now.toLocalDate().atStartOfDay();
        LocalDateTime startOfYesterday = startOfToday.minusDays(1);
        LocalDateTime startOfThisWeek = startOfToday.minusDays(now.getDayOfWeek().getValue() - 1);
        LocalDateTime startOfLastWeek = startOfThisWeek.minusDays(7);
        LocalDateTime startOfThisMonth = now.toLocalDate().withDayOfMonth(1).atStartOfDay();

        // 今天
        if (dateTime.isAfter(startOfToday) || dateTime.isEqual(startOfToday)) {
            return TODAY;
        }
        // 昨天
        else if (dateTime.isAfter(startOfYesterday) || dateTime.isEqual(startOfYesterday)) {
            return YESTERDAY;
        }
        // 本周（不包括今天和昨天）
        else if (dateTime.isAfter(startOfThisWeek) || dateTime.isEqual(startOfThisWeek)) {
            return THIS_WEEK;
        }
        // 上周
        else if (dateTime.isAfter(startOfLastWeek) || dateTime.isEqual(startOfLastWeek)) {
            return LAST_WEEK;
        }
        // 本月（不包括本周和上周）
        else if (dateTime.isAfter(startOfThisMonth) || dateTime.isEqual(startOfThisMonth)) {
            return THIS_MONTH;
        }
        // 更早
        else {
            return EARLIER;
        }
    }

    /**
     * 获取排序权重，用于控制分组显示顺序
     * @return 权重值，越小越靠前
     */
    public int getSortOrder() {
        switch (this) {
            case TODAY:
                return 1;
            case YESTERDAY:
                return 2;
            case THIS_WEEK:
                return 3;
            case LAST_WEEK:
                return 4;
            case THIS_MONTH:
                return 5;
            case EARLIER:
                return 6;
            default:
                return 999;
        }
    }
}
