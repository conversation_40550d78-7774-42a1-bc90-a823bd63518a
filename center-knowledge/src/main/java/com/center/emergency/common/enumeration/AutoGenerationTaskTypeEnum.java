package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AutoGenerationTaskTypeEnum implements IEnumerate<String> {
    SYSTEM_PROMPT("SYSTEM_PROMPT","系统提示词"),
    OPENING_REMARK("OPENING_REMARK","开场白设置");
    private String value;
    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
