package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum PdfStatusEnum implements IEnumerate<String> {
    PROCESSING("PROCESSING", "待处理"),
    PROCESSED("PROCESSED", "已处理"),
    FAILED("FAILED", "处理失败");

    private String value;
    private String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
