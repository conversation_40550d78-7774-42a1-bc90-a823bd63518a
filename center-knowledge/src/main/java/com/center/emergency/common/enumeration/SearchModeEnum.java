package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SearchModeEnum implements IEnumerate<String> {
    VECTOR("VECTOR","1"),
    TEXT("TEXT","2"),
    HYBRID("HYBRID","3");

    private String value;
    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
