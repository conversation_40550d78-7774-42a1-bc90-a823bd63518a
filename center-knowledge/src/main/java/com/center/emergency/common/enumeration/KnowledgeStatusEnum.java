package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum KnowledgeStatusEnum implements IEnumerate<String> {
    CREATING("CREATING", "生成中"),
    CREATE_FAILED("CREATE_FAILED", "生成失败"),

    PENDING_APPROVAL("PENDING_APPROVAL", "待审批"),
    APPROVALING("APPROVALING", "审批中"),
    REJECTED("REJECTED", "审批未通过"),
    ENABLED("ENABLED", "启用"),
    DISABLED("DISABLED", "停用"),
    ;

    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 判断枚举中是否包含某个值
    public static boolean contains(String status) {
        for (KnowledgeStatusEnum enumValue : KnowledgeStatusEnum.values()) {
            if (enumValue.name().equalsIgnoreCase(status)) {
                return true;
            }
        }
        return false;
    }
}
