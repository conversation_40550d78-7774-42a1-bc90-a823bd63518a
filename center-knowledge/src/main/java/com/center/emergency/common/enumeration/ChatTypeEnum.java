package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于表示是系统内置还是用户自定义
 * @date 2024/10/16 18:12
 */
@AllArgsConstructor
public enum ChatTypeEnum implements IEnumerate<String> {
    NORMAL("NORMAL", "普通对话"),
    SIMULATION("SIMULATION", "模拟对话");

    private String value;
    private String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
