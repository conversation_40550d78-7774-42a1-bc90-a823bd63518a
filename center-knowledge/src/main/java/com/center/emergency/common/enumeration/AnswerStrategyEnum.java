package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于表示智能助手回答策略
 * @date 2025/6/3 18:12
 */
@AllArgsConstructor
public enum AnswerStrategyEnum implements IEnumerate<String> {
    MCP("MCP", "MCP"),
    DATABASE("DATABASE", "数据库"),
    FILE("FILE", "file_answer"),
    KNOWLEDGE_BASE("KNOWLEDGE_BASE", "kb_answer");

    private String value;
    private String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
