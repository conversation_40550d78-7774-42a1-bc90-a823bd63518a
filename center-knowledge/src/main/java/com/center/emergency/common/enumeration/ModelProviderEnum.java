package com.center.emergency.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ModelProviderEnum implements IEnumerate<String> {

    OPENAI("OPENAI", "OpenAI - 全球领先生成式AI模型"),
    DEEPSEEK("DEEPSEEK", "DeepSeek - 深度求索公司AI解决方案"),
    TONGYI("TONGYI", "通义千问 - 阿里云智能对话模型");

    private final String value;
    private final String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 判断枚举中是否包含某个值
    public static boolean contains(String input) {
        for (ModelProviderEnum provider : ModelProviderEnum.values()) {
            if (provider.name().equalsIgnoreCase(input) || provider.getValue().equalsIgnoreCase(input)) {
                return true;
            }
        }
        return false;
    }
}