# QA导入增强响应格式

## 问题分析

当前算法接口`check_faqs_duplicates`的响应中，重复FAQ缺少原始FAQ的详细信息，导致前端无法展示用户与哪个现有FAQ重复。

## 建议的算法接口增强

### 当前算法响应（不足）
```json
{
    "details": {
        "duplicate_faqs": [
            {
                "index": 1,
                "question": "什么是人工智能？",
                "answer": "人工智能是计算机科学的一个分支...",
                "reason": "与现有FAQ重复"
            }
        ]
    }
}
```

### 建议的增强响应
```json
{
    "details": {
        "duplicate_faqs": [
            {
                "index": 1,
                "question": "什么是人工智能？",
                "answer": "人工智能是计算机科学的一个分支...",
                "reason": "与现有FAQ重复",
                "original_question": "什么是AI？",
                "original_answer": "AI（人工智能）是计算机科学的一个分支...",
                "similarity_score": 0.95,
                "match_type": "semantic"
            }
        ]
    }
}
```

## 前端展示效果对比

### 当前显示（信息不足）
```
状态：重复
描述：与现有FAQ重复
原问题：null
原答案：null
```

### 增强后显示（信息完整）
```
状态：重复
描述：与现有FAQ重复（相似度：95%）
原问题：什么是AI？
原答案：AI（人工智能）是计算机科学的一个分支...
匹配类型：语义匹配
```

## 当前转换器的适配性

新的转换器`QaImportResponseConverter`已经支持处理原始FAQ信息：

```java
// 保留原始FAQ信息（如果算法接口支持）
previewItem.setOriginalQuestion(item.getString("original_question"));
previewItem.setOriginalAnswer(item.getString("original_answer"));

// 如果算法接口未提供原始信息，在statusDesc中提供更多详情
if (previewItem.getOriginalQuestion() == null && reason != null) {
    previewItem.setStatusDesc(reason + "，将跳过导入");
}
```

## 业务价值

### 1. **用户决策支持**
- 用户可以看到与哪个现有FAQ重复
- 可以判断是否需要修改问题或答案
- 可以决定是否保留重复或修改现有FAQ

### 2. **FAQ质量管理**
- 发现相似但措辞不同的FAQ
- 帮助标准化FAQ表述
- 减少冗余内容

### 3. **用户体验提升**
- 提供完整的重复性分析
- 减少用户困惑
- 支持更精确的内容管理

## 临时解决方案

在算法接口增强之前，可以：

1. **在statusDesc中提供更详细的描述**
```java
previewItem.setStatusDesc("与现有FAQ重复，建议检查知识库中是否已有相似问题");
```

2. **提供知识库查询建议**
```java
previewItem.setStatusDesc("与现有FAQ重复，可在知识库中搜索关键词：" + 
    extractKeywords(item.getString("question")));
```

3. **添加操作建议**
```json
{
    "statusDesc": "与现有FAQ重复，建议：1.修改问题表述 2.检查现有FAQ是否需要更新",
    "actionSuggestions": ["modify_question", "check_existing", "skip_import"]
}
``` 