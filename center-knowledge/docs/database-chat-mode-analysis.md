# 数据库对话模式分析文档

## 📋 概述

数据库对话模式（DATABASE策略）允许用户通过自然语言与数据库进行交互，系统会将用户的问题转换为SQL查询并返回结果。

## 🏗️ 架构分析

### 1. 数据库表结构

#### **center_datasource（数据源基础表）**
```sql
CREATE TABLE center_datasource (
    `id` BIGINT(20) NOT NULL COMMENT '主键',
    `datasource_name` varchar(100) NOT NULL COMMENT '数据库名称',
    `datasource_description` varchar(200) NULL COMMENT '数据库描述说明',
    `larger_model_id` BIGINT NOT NULL COMMENT '关联的大模型',
    `datasource_type` varchar(100) NOT NULL COMMENT '数据库类型（SINGLE/MULTIPLE）',
    `tenant_id` BIGINT(20) NOT NULL COMMENT '租户ID',
    -- 其他字段...
);
```

#### **center_datasource_extension（数据源扩展表）**
```sql
CREATE TABLE center_datasource_extension (
    `id` BIGINT(20) NOT NULL COMMENT '主键',
    `datasource_id` BIGINT(20) NOT NULL COMMENT '数据库基础表ID',
    `category` varchar(100) NOT NULL COMMENT '数据源类型',
    `ip` varchar(100) NOT NULL COMMENT '数据库地址',
    `port` int NOT NULL COMMENT '端口',
    `database_name` varchar(100) DEFAULT NULL COMMENT '数据库名',
    `user_name` varchar(100) DEFAULT NULL COMMENT '数据库用户名',
    `password` varchar(100) DEFAULT NULL COMMENT '数据库密码',
    `table_name` varchar(50) DEFAULT NULL COMMENT '表名称',
    `table_description` varchar(500) DEFAULT NULL COMMENT '表结构说明',
    -- 其他字段...
);
```

### 2. 数据源类型

#### **SingleAndMultipleEnum**
- **SINGLE**: 单数据库模式 - 返回单个数据库配置对象
- **MULTIPLE**: 多数据库模式 - 返回数据库配置列表

## 🔧 实现分析

### 1. DataBaseServiceForAnswerStrategy类

#### **核心功能**
- ✅ **listAnswerStrategyIds()** - 查询机器人关联的数据源
- ✅ **buildChatParams()** - 构建数据库对话参数（已实现）
- ❌ **buildChatParamsWithResourceCollection()** - 资源自主收集（TODO）

#### **参数构建流程**
```java
// 1. 查询数据库表信息
List<DataBaseTableInfo> dataBaseTableInfoList = queryFactory.select(...)
    .from(qDataSourceExtensionModel)
    .join(qDataSourceModel).on(...)
    .join(qRobotKnowledgeModel).on(...)
    .where(qRobotKnowledgeModel.robotId.eq(chatVO.getRobotId()))
    .fetch();

// 2. 构建数据库配置
Object dbConfig = buildDatabaseConfig(dataBaseTableInfoList);

// 3. 添加到参数中
param.put("db_config", dbConfig);
```

### 2. 数据库配置构建逻辑

#### **配置格式差异**

| 数据源类型 | 返回格式 | 说明 |
|-----------|---------|------|
| **SINGLE** | `Map<String, Object>` | 单个数据库配置对象 |
| **MULTIPLE** | `List<Map<String, Object>>` | 多个数据库配置列表 |

#### **单数据库配置示例**
```json
{
  "host": "*************",
  "port": 3306,
  "user": "root",
  "password": "password",
  "database": "test_db",
  "tables": ["users", "orders", "products"]
}
```

#### **多数据库配置示例**
```json
[
  {
    "host": "*************",
    "port": 3306,
    "user": "root",
    "password": "password",
    "database": "test_db1",
    "tables": ["users", "orders"]
  },
  {
    "host": "*************",
    "port": 3306,
    "user": "admin",
    "password": "admin123",
    "database": "test_db2",
    "tables": ["products", "categories"]
  }
]
```

## 🔄 数据流程

### 1. 机器人配置关联
```sql
-- 查询机器人关联的数据源
SELECT ds.datasource_name, dse.ip, dse.port, dse.database_name, 
       dse.user_name, dse.password, dse.table_name, ds.datasource_type
FROM center_robot_knowledge_bases rkb
JOIN center_datasource ds ON rkb.kb_id = ds.id
JOIN center_datasource_extension dse ON ds.id = dse.datasource_id
WHERE rkb.robot_id = ? AND rkb.answer_strategy = 'DATABASE'
```

### 2. 参数构建流程
1. **查询数据源信息** → 获取机器人关联的数据库配置
2. **按类型分组** → 区分SINGLE和MULTIPLE类型
3. **构建配置对象** → 根据类型构建不同格式的配置
4. **添加到参数** → 将db_config添加到请求参数中

### 3. 算法接口调用
- **接口URL**: `/api/database_qa/database_chat` (TODO: 需要确认真实接口)
- **参数格式**: 包含db_config的完整参数

## ❌ 当前问题分析

### 1. **buildChatParamsWithResourceCollection未实现**
```java
@Override
public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                     Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
    // TODO: 收集数据库策略特定的资源
    // 暂时使用父类的默认实现
    return super.buildChatParamsWithResourceCollection(chatVO, isSimulate, modelGroupId, jpaQueryFactory);
}
```

### 2. **算法接口URL未确认**
```java
case DATABASE:
    // 数据库策略 - TODO: 配置数据库对话接口URL
    String databaseUrl = modelUrl + "/api/database_qa/database_chat";
    return databaseUrl;
```

### 3. **缺少数据库连接验证**
- 当前只构建配置，未验证数据库连接有效性
- 可能导致算法接口调用失败

## 🔧 需要完善的功能

### 1. **实现buildChatParamsWithResourceCollection**
```java
@Override
public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                     Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
    log.info("数据库策略自主收集资源并构建参数: chatVO={}, modelGroupId={}, isSimulate={}",
            chatVO.getQuestion(), modelGroupId, isSimulate);

    // 1. 收集数据库配置信息
    List<DataBaseTableInfo> dataBaseTableInfoList = collectDatabaseInfo(chatVO.getRobotId(), jpaQueryFactory);
    
    // 2. 构建基础参数
    HashMap<String, Object> param = buildDatabaseBaseParams(chatVO, modelGroupId, jpaQueryFactory);
    
    // 3. 添加数据库配置
    Object dbConfig = buildDatabaseConfig(dataBaseTableInfoList);
    if (dbConfig != null) {
        param.put("db_config", dbConfig);
    }
    
    return param;
}
```

### 2. **确认算法接口URL**
需要与算法团队确认数据库对话的真实接口地址和参数格式。

### 3. **添加连接验证**
在构建配置前验证数据库连接的有效性。

### 4. **错误处理优化**
完善数据库连接失败、配置错误等异常情况的处理。

## 📊 与其他策略对比

| 策略 | 核心参数 | 接口URL | 实现状态 |
|------|---------|---------|---------|
| **KNOWLEDGE_BASE** | `kb_ids`, `file_ids` | `/local_doc_chat` | ✅ 完整实现 |
| **MCP** | `dynamic_servers`, `max_tool_rounds` | `/mcp_chat_stream` | ✅ 完整实现 |
| **FILE** | `file_ids`, `user_token` | `/doc_chat_qa` | ✅ 完整实现 |
| **DATABASE** | `db_config` | `/database_qa/database_chat` | ⚠️ 部分实现 |

## 🎯 总结

数据库对话模式的基础架构已经搭建完成，包括：
- ✅ 数据库表结构设计
- ✅ 数据源配置管理
- ✅ 基础参数构建逻辑
- ✅ 单/多数据库配置支持

**需要完善的部分：**
- ❌ buildChatParamsWithResourceCollection方法实现
- ❌ 算法接口URL确认
- ❌ 数据库连接验证
- ❌ 错误处理优化

一旦完善这些功能，数据库对话模式就能正常工作，支持用户通过自然语言查询数据库。
