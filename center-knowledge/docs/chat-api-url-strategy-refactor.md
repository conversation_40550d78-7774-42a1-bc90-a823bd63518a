# Chat模块接口调用策略重构文档

## 📋 重构概述

针对助手的四种对话模式（KNOWLEDGE_BASE、DATABASE、MCP、FILE），将原本硬编码的接口URL调用逻辑抽取为策略模式，实现不同答案策略调用不同的算法接口。

## 🔧 重构内容

### 1. 新增服务接口

#### ChatApiUrlService
- **位置**: `center-knowledge/src/main/java/com/center/emergency/biz/chat/service/ChatApiUrlService.java`
- **功能**: 定义根据答案策略获取API接口URL的服务接口

#### ChatApiUrlServiceImpl  
- **位置**: `center-knowledge/src/main/java/com/center/emergency/biz/chat/service/ChatApiUrlServiceImpl.java`
- **功能**: 实现不同答案策略对应的API接口URL映射

### 2. 接口URL映射策略

| 答案策略 | 接口URL | 状态 |
|---------|---------|------|
| **KNOWLEDGE_BASE** | `/api/local_doc_qa/local_doc_chat` | ✅ 已实现 |
| **DATABASE** | `/api/database_qa/database_chat` | 🔄 TODO |
| **MCP** | `/api/mcp_qa/mcp_chat` | 🔄 TODO |
| **FILE** | `/api/file_qa/file_chat` | 🔄 TODO |

### 3. 修改的方法

#### ChatServiceImpl中的三个核心方法：

1. **chatWithRobot()** - 机器人对话
   - 原来: 硬编码 `modelUrl + "/api/local_doc_qa/local_doc_chat"`
   - 现在: `chatApiUrlService.getApiUrl(answerStrategy)`

2. **chatWithKnowledge()** - 知识库对话  
   - 原来: 硬编码 `modelUrl + "/api/local_doc_qa/local_doc_chat"`
   - 现在: `chatApiUrlService.getApiUrl(AnswerStrategyEnum.KNOWLEDGE_BASE)`

3. **reAnswer()** - 重新回答
   - 原来: 硬编码 `modelUrl + "/api/local_doc_qa/local_doc_chat"`
   - 现在: `chatApiUrlService.getApiUrl(answerStrategy)`

## 🎯 重构优势

### 1. **策略模式解耦**
- 将接口URL选择逻辑从业务逻辑中分离
- 便于后续扩展新的答案策略

### 2. **配置集中管理**
- 所有接口URL配置集中在ChatApiUrlServiceImpl中
- 便于维护和修改

### 3. **向后兼容**
- 保持现有业务逻辑不变
- 知识库策略继续使用原有接口

### 4. **扩展性强**
- 新增答案策略只需在ChatApiUrlServiceImpl中添加对应URL
- 不需要修改核心业务逻辑

## 🔄 TODO项目

### 1. 配置其他策略的真实接口URL
当算法团队提供DATABASE、MCP、FILE策略的接口时，只需修改ChatApiUrlServiceImpl中的URL配置

### 2. 可选：配置文件外部化
可以考虑将接口URL配置移到application.yml中

## ✅ 验证要点

1. **功能不变**: 现有知识库对话功能保持完全一致
2. **策略生效**: 不同答案策略的机器人调用对应的接口URL
3. **日志清晰**: 可以通过日志确认调用的具体接口
4. **错误处理**: 未知策略回退到知识库策略

## 🎉 总结

本次重构成功将Chat模块的接口调用逻辑抽取为策略模式，为后续支持多种答案策略的不同接口调用奠定了基础。
