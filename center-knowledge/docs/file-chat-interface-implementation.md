# 文件问答模式接口完善文档

## 📋 实现概述

根据算法文档完善了FILE策略的文件问答接口，实现了单/多文档问答的参数构建逻辑和接口调用。

## 🔧 主要修改

### 1. 更新接口URL

**ChatApiUrlServiceImpl.java**
- 更新FILE策略的API接口URL
- 从占位符URL改为真实的文件问答接口

```java
case FILE:
    // 文件策略 - 使用文件问答接口
    String fileUrl = modelUrl + "/api/local_doc_qa/doc_chat_qa";
    return fileUrl;
```

### 2. 完善文件策略参数构建逻辑

**FileServiceForAnswerStrategy.java**
- 实现了文件特定的参数构建逻辑
- 添加了文件ID收集功能
- 支持单/多文档问答参数构建

#### 核心功能：

1. **collectFileIds()** - 收集机器人关联的文件ID
2. **buildFileBaseParamsWithModelGroup()** - 构建文件基础参数（模型组版本）
3. **buildFileBaseParamsWithModel()** - 构建文件基础参数（单模型版本）
4. **listAnswerStrategyIds()** - 查询机器人关联的文件列表

## 🎯 文件接口参数映射

### 算法文档要求的参数：

| 参数名 | 是否必须 | 类型 | 描述 | 实现状态 |
|--------|---------|------|------|---------|
| **user_token** | 必填 | string | 用户唯一标识 | ✅ 已实现 |
| **file_ids** | 必填 | array | 文件ID数组 | ✅ 已实现 |
| **question** | 必填 | string | 用户问题 | ✅ 已实现 |
| **history** | 可选 | array | 历史消息 | ✅ 已实现 |
| **model** | 可选 | string | 使用的模型 | ✅ 已实现 |
| **api_base** | 可选 | string | API基础地址 | ✅ 已实现 |
| **api_key** | 可选 | string | API密钥 | ✅ 已实现 |
| **enable_reasoning** | 可选 | boolean | 是否启用深度思考 | ✅ 已实现 |
| **enable_model_router** | 可选 | boolean | 是否启用模型路由 | ✅ 已实现 |
| **streaming** | 可选 | boolean | 是否流式响应 | ✅ 固定true |
| **user_custom_prompt** | 可选 | string | 用户自定义提示词 | ✅ 固定null |
| **model_group** | 可选 | array | 模型组配置 | ✅ 已实现 |

## 🔄 数据流程

### 1. 文件ID收集（从会话历史中获取）
```sql
-- 查询当前会话中消息类型为FILE的记录，按创建时间倒序，取最新的10个
SELECT content
FROM center_chat_question
WHERE session_id = ? AND event_type = 'FILE'
ORDER BY create_time DESC
LIMIT 10
```

### 2. 参数构建流程
1. **收集文件ID** → `collectFileIdsFromSession()` - 从会话历史中获取
2. **构建基础参数** → `buildFileBaseParams*()`
3. **添加文件特有参数** → `user_token`, `file_ids`, `streaming`
4. **添加模型配置** → `model_group` 或单模型配置
5. **添加可选参数** → `enable_model_router`, `enable_reasoning`

### 3. 最终参数示例
```json
{
  "user_token": "default_user",
  "file_ids": ["202505291", "202505292"],
  "question": "请总结文档的主要内容",
  "history": [
    {"role": "user", "content": "你好，我想了解深圳的公园"},
    {"role": "assistant", "content": "深圳有很多公园，比如深圳湾公园..."}
  ],
  "streaming": true,
  "user_custom_prompt": null,
  "model_group": [
    {
      "role": "router",
      "model": "qwen-turbo-latest",
      "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
      "api_key": "sk-897fd687b2ef402db4428331cfb89884"
    }
  ],
  "enable_reasoning": false,
  "enable_model_router": false
}
```

## 🎯 关键特性

### 1. **文件特有参数处理**
- 使用`user_token`标识用户
- 使用`file_ids`数组指定文档
- 使用`question`而不是`query`
- 固定`streaming=true`启用流式响应

### 2. **文件ID动态收集**
- 从当前会话的历史消息中动态收集文件ID
- 查询消息类型为FILE的记录，获取content作为文件ID
- 按创建时间倒序，取最新的10个文件
- 支持单个或多个文件同时问答

### 3. **模型组支持**
- 完整支持模型组配置
- 自动回退到系统默认配置
- 兼容单模型和多模型配置

### 4. **向后兼容**
- 保持与现有接口的兼容性
- 支持旧的modelId参数格式
- 渐进式升级到模型组配置

## 📊 参数差异对比

| 参数 | 知识库策略 | MCP策略 | 文件策略 |
|------|-----------|---------|---------|
| **问题字段** | `question` | `query` | `question` |
| **核心参数** | `kb_ids`, `file_ids` | `dynamic_servers` | `file_ids`, `user_token` |
| **特有参数** | `tags_list`, `search_mode` | `max_tool_rounds` | `streaming`, `user_custom_prompt` |
| **接口URL** | `/local_doc_chat` | `/mcp_chat_stream` | `/doc_chat_qa` |

## ✅ 验证要点

1. **接口调用** - FILE策略使用正确的文件问答接口URL
2. **参数格式** - 符合算法文档要求的参数结构
3. **文件收集** - 正确收集和转换文件ID配置
4. **模型配置** - 正确传递模型组或单模型配置
5. **用户标识** - 正确设置user_token参数

## 🔄 后续优化

1. **用户标识** - 从安全上下文获取真实的用户标识
2. **文件验证** - 添加文件存在性和权限验证
3. **错误处理** - 完善文件不存在时的降级策略
4. **性能优化** - 缓存文件配置减少数据库查询

## 🎉 总结

文件问答模式已完全按照算法文档要求实现，支持：
- ✅ 文件问答接口调用
- ✅ 动态文件ID收集
- ✅ 完整的参数格式转换
- ✅ 模型组配置支持
- ✅ 单/多文档问答

现在FILE策略可以正常进行文档问答，支持单个或多个文件的智能问答功能。

## 🔄 重要更新：文件ID获取逻辑修改

### 📝 **修改说明**
根据用户需求，文件ID的获取逻辑已从"机器人配置关联"改为"会话历史动态获取"：

**原逻辑（已废弃）：**
- 从`robot_knowledge_bases`表查询机器人关联的文件
- 文件ID固定配置在机器人中

**新逻辑（当前实现）：**
- 从当前会话的`center_chat_question`表中查询
- 筛选消息类型为`FILE`的记录
- 获取`content`字段作为文件ID
- 按创建时间倒序，取最新的10个文件

### 🔧 **实现细节**

#### 核心查询逻辑：
```sql
SELECT content
FROM center_chat_question
WHERE session_id = ? AND event_type = 'FILE'
ORDER BY create_time DESC
LIMIT 10
```

#### 方法实现：
```java
private List<String> collectFileIdsFromSession(Long sessionId, JPAQueryFactory jpaQueryFactory) {
    QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;

    List<String> fileContents = jpaQueryFactory
            .select(qChatQuestionModel.content)
            .from(qChatQuestionModel)
            .where(qChatQuestionModel.sessionId.eq(sessionId)
                    .and(qChatQuestionModel.eventType.eq(EventTypeEnum.FILE)))
            .orderBy(qChatQuestionModel.createTime.desc())
            .limit(10)
            .fetch();

    return fileContents;
}
```

### 🎯 **优势**
1. **动态性** - 文件ID根据会话上下文动态获取
2. **时效性** - 优先使用最新上传的文件
3. **灵活性** - 不需要预先配置机器人关联的文件
4. **上下文相关** - 文件问答基于当前会话的文件上传历史

### ⚠️ **注意事项**
1. 需要确保前端在上传文件时正确创建`event_type=FILE`的question记录
2. `content`字段应该存储文件ID（字符串格式）
3. 最多获取10个最新文件，避免参数过大
4. 如果会话中没有文件上传记录，`file_ids`将为空数组
