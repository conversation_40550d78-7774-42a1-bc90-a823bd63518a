# MCP对话接口完善文档

## 📋 实现概述

根据算法文档完善了MCP策略的对话接口，实现了MCP特定的参数构建逻辑和接口调用。

## 🔧 主要修改

### 1. 更新接口URL

**ChatApiUrlServiceImpl.java**
- 更新MCP策略的API接口URL
- 从占位符URL改为真实的MCP流式对话接口

```java
case MCP:
    // MCP策略 - 使用MCP流式对话接口
    String mcpUrl = modelUrl + "/api/local_doc_qa/mcp_chat_stream";
    return mcpUrl;
```

### 2. 完善MCP参数构建逻辑

**McpServiceForAnswerStrategy.java**
- 实现了MCP特定的参数构建逻辑
- 添加了MCP服务配置收集功能
- 支持dynamic_servers参数构建

#### 核心功能：

1. **collectMcpServers()** - 收集机器人关联的MCP服务
2. **buildMcpBaseParams()** - 构建MCP基础参数
3. **buildChatParamsWithResourceCollection()** - 完整的参数构建

## 🎯 MCP接口参数映射

### 算法文档要求的参数：

| 参数名 | 是否必须 | 类型 | 描述 | 实现状态 |
|--------|---------|------|------|---------|
| **query** | 是 | string | 当前用户请求 | ✅ 已实现 |
| **history** | 是 | object | 历史消息，OpenAI格式 | ✅ 已实现 |
| **max_tool_rounds** | 否 | integer | 最大工具调用轮次(1-20) | ✅ 默认10 |
| **dynamic_servers** | 否 | array | MCP服务器配置列表 | ✅ 已实现 |
| **model_group** | 否 | array | 模型组配置 | ✅ 已实现 |
| **enable_model_router** | 否 | boolean | 模型路由开关 | ✅ 已实现 |
| **enable_reasoning** | 否 | boolean | 推理模式开关 | ✅ 已实现 |

### dynamic_servers对象结构：

| 参数名 | 是否必须 | 类型 | 描述 | 实现状态 |
|--------|---------|------|------|---------|
| **server_key** | 是 | string | 服务器唯一标识符 | ✅ 使用serverName |
| **url** | 是 | string | 服务器URL | ✅ 使用serverUrl |
| **description** | 否 | string | 服务器描述 | ✅ 使用mcpDesc |
| **make_default** | 否 | boolean | 是否设为默认服务器 | ✅ 固定false |

## 🔄 数据流程

### 1. MCP服务配置收集
```sql
-- 查询机器人关联的MCP服务
SELECT mcp.server_name, mcp.server_url, mcp.mcp_desc
FROM center_robot_knowledge_bases rkb
JOIN mcp_table mcp ON rkb.kb_id = mcp.id
WHERE rkb.robot_id = ? AND rkb.answer_strategy = 'MCP'
```

### 2. 参数构建流程
1. **收集MCP服务** → `collectMcpServers()`
2. **构建基础参数** → `buildMcpBaseParams()`
3. **添加MCP特有参数** → `dynamic_servers`, `max_tool_rounds`
4. **添加模型组配置** → `model_group`
5. **添加可选参数** → `enable_model_router`, `enable_reasoning`

### 3. 最终参数示例
```json
{
  "query": "帮我随意画个柱状图看看",
  "history": [
    {"role": "user", "content": "用户消息内容"},
    {"role": "assistant", "content": "助手回复内容"}
  ],
  "max_tool_rounds": 10,
  "dynamic_servers": [
    {
      "server_key": "mcp-data-charts",
      "url": "http://0.0.0.0:8027/sse",
      "description": "提供数据图表生成工具服务",
      "make_default": false
    }
  ],
  "model_group": [
    {
      "role": "router",
      "model": "qwen-turbo-latest",
      "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
      "api_key": "sk-897fd687b2ef402db4428331cfb89884"
    }
  ],
  "enable_model_router": true,
  "enable_reasoning": false
}
```

## 🎯 关键特性

### 1. **MCP特有参数处理**
- 不使用`kb_ids`和`file_ids`（MCP不需要知识库）
- 使用`query`而不是`question`
- 添加`max_tool_rounds`工具调用轮次控制

### 2. **动态服务配置**
- 从数据库动态收集机器人关联的MCP服务
- 自动转换为算法要求的`dynamic_servers`格式
- 支持多个MCP服务同时配置

### 3. **模型组支持**
- 完整支持模型组配置
- 自动回退到系统默认配置
- 兼容单模型和多模型配置

### 4. **向后兼容**
- 保持与现有接口的兼容性
- 支持旧的modelId参数格式
- 渐进式升级到模型组配置

## ✅ 验证要点

1. **接口调用** - MCP策略使用正确的流式接口URL
2. **参数格式** - 符合算法文档要求的参数结构
3. **服务配置** - 正确收集和转换MCP服务配置
4. **模型配置** - 正确传递模型组或单模型配置
5. **日志记录** - 详细的参数构建和服务收集日志

## 🔄 后续优化

1. **配置验证** - 添加MCP服务配置的有效性验证
2. **错误处理** - 完善MCP服务不可用时的降级策略
3. **性能优化** - 缓存MCP服务配置减少数据库查询
4. **监控告警** - 添加MCP服务调用的监控和告警

## 🎉 总结

MCP对话接口已完全按照算法文档要求实现，支持：
- ✅ MCP流式对话接口调用
- ✅ 动态MCP服务配置收集
- ✅ 完整的参数格式转换
- ✅ 模型组配置支持
- ✅ 工具调用轮次控制

现在MCP策略可以正常进行对话，并调用配置的MCP服务进行工具操作。
