# 文件ID预处理功能实现文档

## 📋 功能概述

根据用户需求，在文件问答模式下，需要先将前端传递的文件ID列表插入到question表中，然后再进行正常的参数拼接对话。

## 🔧 实现内容

### 1. 修改请求参数结构

**ChatWithRobotReq.java**
- 添加了`fileIds`属性，用于接收前端传递的文件ID列表

```java
@Schema(description = "文件ID列表，用于文件问答模式")
private List<String> fileIds;
```

### 2. 添加文件ID预处理逻辑

**ChatServiceImpl.java**
- 在`chatWithRobot`方法中添加了文件ID预处理逻辑
- 当答案策略为FILE且提供了文件ID时，先插入question表

```java
// 如果是文件策略且提供了文件ID，先将文件ID插入question表
if (answerStrategy == AnswerStrategyEnum.FILE && chatWithRobotReq.getFileIds() != null && !chatWithRobotReq.getFileIds().isEmpty()) {
    insertFileIdsToQuestionTable(chatVO.getSessionId(), chatWithRobotReq.getFileIds(), isSimulate);
}
```

### 3. 实现文件ID插入方法

**insertFileIdsToQuestionTable方法**
- 将文件ID列表插入到question表中
- 设置`event_type=FILE`，`content`为文件ID
- 模拟模式下不执行插入操作
- **重复检查**：避免同一会话中重复插入相同的文件ID

**getExistingFileIdsInSession方法**
- 查询当前会话中已存在的文件ID
- 返回文件ID集合，用于重复检查

```java
private void insertFileIdsToQuestionTable(Long sessionId, List<String> fileIds, Boolean isSimulate) {
    if (isSimulate) {
        log.info("模拟模式下不插入文件ID到question表");
        return;
    }

    // 1. 查询当前会话中已存在的文件ID
    Set<String> existingFileIds = getExistingFileIdsInSession(sessionId);

    // 2. 过滤出需要插入的文件ID（去除重复）
    List<String> newFileIds = new ArrayList<>();
    for (String fileId : fileIds) {
        if (!existingFileIds.contains(fileId)) {
            newFileIds.add(fileId);
        }
    }

    // 3. 插入新的文件ID
    for (String fileId : newFileIds) {
        ChatQuestionModel fileQuestionModel = new ChatQuestionModel();
        fileQuestionModel.setId(snowflake.nextId());
        fileQuestionModel.setContent(fileId);
        fileQuestionModel.setSessionId(sessionId);
        fileQuestionModel.setEventType(EventTypeEnum.FILE);

        chatQuestionRepository.save(fileQuestionModel);
    }
}

private Set<String> getExistingFileIdsInSession(Long sessionId) {
    QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;

    List<String> existingFileIds = queryFactory
            .select(qChatQuestionModel.content)
            .from(qChatQuestionModel)
            .where(qChatQuestionModel.sessionId.eq(sessionId)
                    .and(qChatQuestionModel.eventType.eq(EventTypeEnum.FILE)))
            .fetch();

    return new HashSet<>(existingFileIds);
}
```

## 🔄 完整流程

### 1. **前端请求**
```json
{
  "question": "请总结这些文档的主要内容",
  "robotId": 123,
  "sessionId": 456,
  "fileIds": ["202505291", "202505292", "202505293"]
}
```

### 2. **后端处理流程**
1. **检查答案策略** → 判断是否为FILE策略
2. **重复检查** → 查询当前会话中已存在的文件ID
3. **预处理文件ID** → 如果是FILE策略且有fileIds，去重后插入question表
4. **参数构建** → 文件策略从question表中收集文件ID
5. **发起对话** → 使用收集到的文件ID进行问答

### 3. **数据库变化**
插入question表的记录：
```sql
INSERT INTO center_chat_question (id, content, session_id, event_type, create_time, creator_id)
VALUES 
  (生成的ID1, '202505291', 456, 'FILE', NOW(), 当前用户ID),
  (生成的ID2, '202505292', 456, 'FILE', NOW(), 当前用户ID),
  (生成的ID3, '202505293', 456, 'FILE', NOW(), 当前用户ID);
```

### 4. **文件策略收集**
文件策略的`collectFileIdsFromSession`方法会查询：
```sql
SELECT content
FROM center_chat_question
WHERE session_id = 456 AND event_type = 'FILE'
ORDER BY create_time DESC
LIMIT 10
```

## 🎯 关键特性

### 1. **时序保证**
- 先插入文件ID到question表
- 再进行参数构建和对话
- 确保文件策略能正确收集到文件ID

### 2. **模拟模式处理**
- 模拟模式下不插入数据库
- 避免测试数据污染生产环境

### 3. **策略判断**
- 只有FILE策略才执行文件ID预处理
- 其他策略不受影响

### 4. **重复检查**
- 查询当前会话中已存在的文件ID
- 过滤出需要插入的新文件ID
- 避免重复插入相同的文件ID

### 5. **空值处理**
- 检查fileIds是否为null或空列表
- 避免不必要的数据库操作

## 📊 数据流向图

```
前端请求 → ChatWithRobotReq.fileIds
    ↓
判断答案策略 = FILE?
    ↓ (是)
getExistingFileIdsInSession() → 查询已存在的文件ID
    ↓
过滤重复文件ID → 只保留新文件ID
    ↓
insertFileIdsToQuestionTable() → 插入新文件ID
    ↓
插入question表 (event_type=FILE)
    ↓
文件策略.collectFileIdsFromSession()
    ↓
查询question表获取文件ID
    ↓
构建算法参数 (file_ids数组)
    ↓
调用文件问答接口 (/api/local_doc_qa/doc_chat_qa)
```

## ✅ 验证要点

1. **参数传递** - 前端正确传递fileIds数组
2. **策略判断** - 只有FILE策略执行预处理
3. **数据插入** - 文件ID正确插入question表
4. **数据收集** - 文件策略正确收集文件ID
5. **模拟模式** - 模拟模式下不插入数据库

## 🔄 后续优化

1. **批量插入** - 考虑使用批量插入提高性能
2. **事务管理** - 确保文件ID插入和对话的事务一致性
3. **错误处理** - 完善文件ID插入失败的错误处理
4. **性能优化** - 考虑使用批量查询和批量插入提高性能

## 🎉 总结

文件ID预处理功能已完全实现，支持：
- ✅ 前端传递文件ID列表
- ✅ 文件策略下的预处理逻辑
- ✅ 自动插入question表
- ✅ 重复检查避免重复插入
- ✅ 文件策略正确收集文件ID
- ✅ 模拟模式兼容处理

现在文件问答模式可以正确处理前端传递的文件ID，实现完整的文件问答流程。
