# 数据库对话模式完善实现文档

## 📋 实现概述

根据算法文档完善了数据库对话模式（DATABASE策略），实现了多数据库查询的参数构建逻辑和接口调用。

## 🔧 主要修改

### 1. 更新接口URL

**ChatApiUrlServiceImpl.java**
- 更新DATABASE策略的API接口URL
- 从占位符URL改为真实的多数据库查询接口

```java
case DATABASE:
    // 数据库策略 - 使用多数据库查询接口
    String databaseUrl = modelUrl + "/api/local_doc_qa/multi_db_query";
    return databaseUrl;
```

### 2. 完善数据库策略参数构建逻辑

**DataBaseServiceForAnswerStrategy.java**
- 实现了`buildChatParamsWithResourceCollection`方法
- 添加了数据库配置收集功能
- 支持多数据库配置参数构建

#### 核心功能：

1. **collectDatabaseInfo()** - 收集机器人关联的数据库信息
2. **buildDatabaseBaseParamsWithModelGroup()** - 构建数据库基础参数
3. **buildDatabaseConfigs()** - 构建数据库配置列表

## 🎯 算法接口参数映射

### 算法文档要求的参数：

| 参数名 | 类型 | 必填 | 描述 | 实现状态 |
|--------|------|------|------|---------|
| **query** | string | 是 | 用户查询文本 | ✅ 已实现 |
| **db_configs** | array | 是 | 多个数据库的连接配置数组 | ✅ 已实现 |
| **schema_info** | object | 否 | 数据库结构信息 | 🔄 TODO |
| **history** | array | 否 | 历史消息 | ✅ 已实现 |

### db_configs数组格式：

| 参数名 | 类型 | 描述 | 实现状态 |
|--------|------|------|---------|
| **host** | string | 数据库地址 | ✅ 使用ip字段 |
| **port** | integer | 端口号 | ✅ 已实现 |
| **user** | string | 用户名 | ✅ 使用userName字段 |
| **password** | string | 密码 | ✅ 已实现 |
| **database** | string | 数据库名 | ✅ 使用databaseName字段 |
| **tables** | array | 表名列表 | ✅ 已实现 |

## 🔄 数据流程

### 1. 数据库信息收集
```sql
-- 查询机器人关联的数据库配置
SELECT dse.ip, dse.port, dse.database_name, dse.user_name, 
       dse.password, dse.table_name, ds.datasource_type
FROM center_datasource_extension dse
JOIN center_datasource ds ON dse.datasource_id = ds.id
JOIN center_robot_knowledge_bases rkb ON rkb.kb_id = dse.datasource_id
WHERE rkb.robot_id = ? AND rkb.answer_strategy = 'DATABASE'
```

### 2. 参数构建流程
1. **收集数据库信息** → `collectDatabaseInfo()`
2. **构建基础参数** → `buildDatabaseBaseParamsWithModelGroup()`
3. **构建数据库配置** → `buildDatabaseConfigs()`
4. **添加到参数** → `db_configs`数组
5. **添加模型配置** → `model_group`

### 3. 最终参数示例
```json
{
  "query": "帮我查询userinfo表中人员的体温的分布情况。",
  "history": [
    {"role": "user", "content": "用户消息内容"},
    {"role": "assistant", "content": "助手回复内容"}
  ],
  "db_configs": [
    {
      "host": "*************",
      "port": 3311,
      "user": "root",
      "password": "sribdwx",
      "database": "nursing_home",
      "tables": ["userinfo"]
    },
    {
      "host": "*************",
      "port": 3311,
      "user": "root",
      "password": "sribdwx",
      "database": "ChatBI_TestDB",
      "tables": ["orders", "products"]
    }
  ],
  "model_group": [
    {
      "role": "router",
      "model": "qwen-turbo-latest",
      "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
      "api_key": "sk-897fd687b2ef402db4428331cfb89884"
    }
  ]
}
```

## 🎯 关键特性

### 1. **参数格式统一**
- 统一使用`db_configs`数组格式
- 兼容原有的单/多数据库配置逻辑
- 符合算法文档要求

### 2. **数据库配置动态收集**
- 从数据库动态收集机器人关联的数据源
- 自动按数据库分组并构建配置
- 支持多个数据库同时查询

### 3. **模型组支持**
- 完整支持模型组配置
- 自动回退到系统默认配置
- 兼容单模型和多模型配置

### 4. **向后兼容**
- 保持与现有接口的兼容性
- 支持旧的buildChatParams方法
- 渐进式升级到新参数格式

## 📊 参数差异对比

| 参数 | 知识库策略 | MCP策略 | 文件策略 | 数据库策略 |
|------|-----------|---------|---------|-----------|
| **问题字段** | `question` | `query` | `question` | `query` |
| **核心参数** | `kb_ids`, `file_ids` | `dynamic_servers` | `file_ids`, `user_token` | `db_configs` |
| **特有参数** | `tags_list`, `search_mode` | `max_tool_rounds` | `streaming` | `schema_info` |
| **接口URL** | `/local_doc_chat` | `/mcp_chat_stream` | `/doc_chat_qa` | `/multi_db_query` |

## ✅ 验证要点

1. **接口调用** - DATABASE策略使用正确的多数据库查询接口URL
2. **参数格式** - 符合算法文档要求的参数结构
3. **数据库收集** - 正确收集和转换数据库配置
4. **模型配置** - 正确传递模型组或单模型配置
5. **数组格式** - db_configs使用数组格式而不是单个对象

## 🔄 后续优化

1. **schema_info参数** - 添加数据库结构信息参数
2. **连接验证** - 添加数据库连接有效性验证
3. **错误处理** - 完善数据库连接失败的降级策略
4. **性能优化** - 缓存数据库配置减少查询次数

## 🎉 总结

数据库对话模式已完全按照算法文档要求实现，支持：
- ✅ 多数据库查询接口调用
- ✅ 动态数据库配置收集
- ✅ 完整的参数格式转换
- ✅ 模型组配置支持
- ✅ 多数据库并行查询

现在DATABASE策略可以正常进行数据库查询，支持用户通过自然语言查询多个数据库的数据。
