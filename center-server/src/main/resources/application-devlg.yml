server:
  url: http://localhost:9082/knowledge/file/preview_url

spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: root
    password: liugan

  flyway:
    url: **************************************************************************************************************************************************
    user: root
    password: liugan
    table: emergency_flyway_schema_history


  #    flyway元数据表名称，如果不指定，则默认为flyway_schema_history，多个系统共用一个库时，可以通过使用不再的表来控制数据库脚本的版本。
  #    同一个表的元数据，最好是由一个系统进行维护
  # Redis 配置
  redis:
    host: 127.0.0.1 # 地址
    port: 6379 # 端口
    database: 1 # 数据库索引
  #    password: dev # 密码，建议生产环境开启
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
#  -- #################### 接口文档配置（生产环境关闭-false） ####################

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
###################业务逻辑相关配置##########################

#hdfs配置
hadoop:
  fs:
#    defaultFS: hdfs://***************:8020
    defaultFS: hdfs://127.0.0.1:9000
  dfs:
    client:
      use-datanode-hostname: true
  user:
    name: root
  home:
    dir: D:\sss
#  host: ***************
  host: localhost
  webHdfsPort: 9870
logging:
  config: classpath:logback-devlg.xml
file:
  localPath: /usr/local/backend/knowledge/temp

model:
  url: http://
#调用算法接口
python:
  api-base-url: http://***************:8777
  api-base-dfsurl: http://127.0.0.1:9870/webhdfs/v1
  api-url-upload: http://***************:8798/upload


webhdfs:
  baseUrl: http://127.0.0.1:9870

#创建企业管理员用户初始密码
admin:
  password: e10adc3949ba59abbe56e057f20f883e
password:
  default: 123456
storage:
  type: HDFS  # 或者 minio, ftp, oss 等
minio:
  endpoint: http://localhost:9000  # MinIO的服务地址
  access-key: your-access-key      # MinIO的访问密钥
  secret-key: your-secret-key      # MinIO的秘密密钥
