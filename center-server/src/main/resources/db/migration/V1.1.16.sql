-- 会话文件关联表
CREATE TABLE `center_chat_session_file` (
                                   `id` bigint(20) NOT NULL COMMENT '主键ID',
                                   `robot_id` bigint(20) NOT NULL COMMENT '关联的助手id）',
                                   `session_id` bigint(20) NOT NULL COMMENT '会话ID',
                                   `file_id` bigint(20) NOT NULL COMMENT '会话ID',
                                   `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                   `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话文件关联表';