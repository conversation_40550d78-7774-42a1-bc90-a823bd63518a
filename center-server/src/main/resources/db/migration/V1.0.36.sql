-- 1. 模型组主表
CREATE TABLE model_group
(
    id          BIGINT(20) NOT NULL COMMENT '模型组主键',
    group_name  VARCHAR(100) NOT NULL COMMENT '模型组名称',
    tenant_id   BIGINT(20) NULL COMMENT '租户号',
    creator_id  BIGINT(20) NULL COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'创建时间',
    updater_id  BIGINT(20) NULL COMMENT '更新人ID',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='模型组表';

-- 2. 模型组成员表
CREATE TABLE model_group_member
(
    id             BIGINT(20) NOT NULL COMMENT ' 模型组成员表主键',
    model_group_id BIGINT(20)   NOT NULL COMMENT '所属模型组ID',
    large_model_id BIGINT(20)   NOT NULL COMMENT '大模型ID',
    role           VARCHAR(50) NULL COMMENT '成员角色：自适应模型 / 深度思考模型 / 普通模型',
    tenant_id      BIGINT(20) NULL COMMENT '租户号',
    creator_id     BIGINT(20) NULL COMMENT '创建人ID',
    create_time    DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'创建时间',
    updater_id     BIGINT(20) NULL COMMENT '更新人ID',
    update_time    DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='模型组-模型关联表';