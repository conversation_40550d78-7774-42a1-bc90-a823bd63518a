-- 1. MCP主表
CREATE TABLE mcp_table
(
    id          BIGINT(20) NOT NULL COMMENT 'MCP主键',
    mcp_name  VARCHAR(100) NOT NULL COMMENT 'MCP名称',
    mcp_desc  VARCHAR(255) NULL COMMENT 'MCP描述',
    install_method VARCHAR(50) NOT NULL COMMENT '安装方式',
    server_name VARCHAR(255) NOT NULL COMMENT '服务名称',
    server_url VARCHAR(255) NOT NULL COMMENT '服务URL',
    tenant_id   BIGINT(20) NULL COMMENT '租户号',
    creator_id  BIGINT(20) NULL COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'创建时间',
    updater_id  BIGINT(20) NULL COMMENT '更新人ID',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='Microservice Control Platform 表';
