ALTER TABLE `model_group`
    ADD COLUMN `source_type` varchar(50) NULL COMMENT '模型源类型： 系统内置，自定义模型' AFTER `group_name`;

INSERT INTO `model_group`
(id, group_name,source_type, tenant_id, create_time, update_time, creator_id, updater_id)
VALUES (1926893956866392064, '系统内置模型组','SYSTEM',1845709218968526848,current_timestamp,current_timestamp,1825843512497688576,1825843512497688576);

INSERT INTO `model_group_member`
(id, model_group_id, large_model_id, role, tenant_id, create_time, update_time, creator_id, updater_id)
VALUES (1926937946131173376, 1926893956866392064, 1914200736866541568,'NORMAL',1845709218968526848, current_timestamp,current_timestamp,1825843512497688576,1825843512497688576);




