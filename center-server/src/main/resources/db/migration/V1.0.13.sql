CREATE TABLE center_chat_question (
    `id` BIGINT(20) NOT NULL COMMENT '主键ID',
    `content` MEDIUMTEXT NOT NULL COMMENT '问题内容',
    `session_id` BIGINT NOT NULL COMMENT '会话ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='用户问题表';

CREATE TABLE center_chat_answer (
    `id` BIGINT(20) NOT NULL COMMENT '主键ID',
    `content` MEDIUMTEXT NOT NULL COMMENT '问题内容',
    `session_id` BIGINT NOT NULL COMMENT '会话ID',
    `question_id` BIGINT(20) NOT NULL COMMENT '问题ID',
    `thumbs_up` BIT(1) COMMENT '点赞/倒赞',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
     PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='模型回答表';

