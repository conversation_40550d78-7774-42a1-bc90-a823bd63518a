CREATE TABLE center_robots
(
    `id`          BIGINT(20)   NOT NULL COMMENT '主键',
    `robot_name`  varchar(50)  COMMENT '机器人名称',
    `status`  varchar(50)  COMMENT '状态',
    `operator_id`   BIGINT(20)  COMMENT '操作用户ID',
    `welcome_message`       varchar(100)   COMMENT '欢迎语',
    `remark`       varchar(255)   COMMENT '备注',
    `is_deleted` INT COMMENT '删除标示',
    `tenant_id`   BIGINT(20)  COMMENT '租户号',
    `create_time` datetime   DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime   DEFAULT NULL COMMENT '更新时间',
    `creator_id`  bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id`  bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    COMMENT ='机器人表';