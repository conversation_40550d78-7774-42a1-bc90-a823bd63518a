CREATE TABLE large_model (
                             `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键',

                             `provider` VARCHAR(100) NOT NULL COMMENT '语言模型提供商，如 OpenAI、DeepSeek、Tongyi',

                             `model_display_name` VARCHAR(100) NOT NULL COMMENT '模型显示名称',

                             `model_name` VARCHAR(100) NOT NULL COMMENT '模型名称，例如 deepseek-chat',

                             `base_url` VARCHAR(255) COMMENT '模型基础 URL，例如 https://api.deepseek.com',

                             `model_desc` VARCHAR(255) COMMENT '模型描述',

                             `api_key` VARCHAR(255) NOT NULL COMMENT 'API Key',

                             `temperature` FLOAT  COMMENT '生成随机性（temperature）',

                             `top_p` FLOAT  COMMENT 'Top P 采样参数',

                             `status` VARCHAR(50) COMMENT '状态：INACTIVE=停用，ACTIVE=启用',

                             `tenant_id` BIGINT(20) COMMENT '租户号',

                             `creator_id` BIGINT(20) COMMENT '创建人ID',

                             `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

                             `updater_id` BIGINT(20) COMMENT '更新人ID',

                             `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

                             PRIMARY KEY (`id`)
) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='大模型配置表';
