-- AI搜索会话表
CREATE TABLE `ai_search_session` (
                                     `id` bigint(20) NOT NULL COMMENT '主键ID',
                                     `title` varchar(500) DEFAULT NULL COMMENT '会话标题',
                                     `search_mode` tinyint NOT NULL COMMENT '搜索模式：1-全网搜索，2-企业内部搜索',
                                     `search_engine` varchar(50) DEFAULT NULL COMMENT '搜索引擎类型（全网搜索时使用，如tavily）',
                                     `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                     `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
                                     `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI搜索会话表';

-- AI搜索对话表（统一存储问题和回答）
CREATE TABLE `ai_search_message` (
                                     `id` bigint(20) NOT NULL COMMENT '主键ID',
                                     `session_id` bigint(20) NOT NULL COMMENT '会话ID',
                                     `role` varchar(20) NOT NULL COMMENT '角色：user-用户，assistant-助手',
                                     `content` longtext NOT NULL COMMENT '对话内容',
                                     `search_mode` tinyint DEFAULT NULL COMMENT '搜索模式：1-全网搜索，2-企业内部搜索（仅user角色时有值）',
                                     `search_engine` varchar(50) DEFAULT NULL COMMENT '搜索引擎类型（仅user角色时有值）',
                                     `max_results` int DEFAULT 10 COMMENT '最大搜索结果数（仅user角色时有值）',
                                     `kb_ids` json DEFAULT NULL COMMENT '知识库ID列表（企业内部搜索时使用）',
                                     `file_ids` json DEFAULT NULL COMMENT '文件ID列表（企业内部搜索时使用）',
                                     `model_config` json DEFAULT NULL COMMENT '模型配置信息（仅user角色时有值）',
                                     `thumbs_up` bit(1) DEFAULT NULL COMMENT '点赞状态：1-点赞，0-点踩，null-未操作（仅assistant角色时有值）',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                     `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI搜索对话表';

-- AI搜索事件表（存储详细响应数据）
CREATE TABLE `ai_search_event` (
                                   `id` bigint(20) NOT NULL COMMENT '主键ID',
                                   `message_id` bigint(20) NOT NULL COMMENT '关联的message ID（assistant角色）',
                                   `session_id` bigint(20) NOT NULL COMMENT '会话ID',
                                   `event_type` varchar(50) NOT NULL COMMENT '事件类型：MESSAGE-消息内容，DOCUMENT-搜索文档，RECOMMENDED_QUESTION-推荐问题，SEARCH_INFO-搜索统计，STATUS_UPDATE-状态更新',
                                   `event_content` longtext COMMENT '事件内容（JSON格式或纯文本）',
                                   `sequence_order` int NOT NULL COMMENT '事件在响应中的顺序',
                                   `include_in_context` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否参与上下文传递',
                                   `event_status` varchar(20) DEFAULT 'SUCCESS' COMMENT '事件状态：SUCCESS-成功，FAILED-失败',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                   `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI搜索事件表';