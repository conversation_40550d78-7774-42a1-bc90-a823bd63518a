CREATE TABLE center_chat_session (
    `id` BIGINT(20) NOT NULL,
    `robot_id` BIGINT(20) NOT NULL COMMENT '对话机器人ID',
    `title` MEDIUMTEXT NOT NULL COMMENT '历史对话标题',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='模型对话表（以session为单位记录历史对话）';