
CREATE TABLE center_knowledges (
                                   `id` BIGINT(20) NOT NULL COMMENT '唯一标识',
                                   `kb_id` BIGINT(20) NOT NULL COMMENT '知识库ID',
                                   `file_id` BIGINT(20) DEFAULT NULL COMMENT '关联文件ID',
                                   `ai_faqb_faq_id` BIGINT(20) NOT NULL COMMENT '模型知识库的知识ID',
                                   `ai_fileb_file_id` BIGINT(20) DEFAULT NULL COMMENT '模型文件库的文件ID',
                                   `knowledge_name` VARCHAR(100) NOT NULL COMMENT '知识名称',
                                   `status` VARCHAR(50) NOT NULL COMMENT '知识状态',
                                   `is_deleted` INT DEFAULT 0 COMMENT '删除标识',
                                   `tenant_id` BIGINT(20) DEFAULT NULL COMMENT '租户ID',
                                   `creator_id` BIGINT(20) DEFAULT NULL COMMENT '创建人',
                                   `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `updater_id` BIGINT(20) DEFAULT NULL COMMENT '更新人',
                                   `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识表';

CREATE TABLE center_tags (
                             `id` BIGINT(20) NOT NULL COMMENT '唯一标识',
                             `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称',
                             `tag_type` VARCHAR(50) NOT NULL COMMENT '标签类型; 系统标签，自定义标签',
                             `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
                             `tenant_id` BIGINT(20) DEFAULT NULL COMMENT '租户号',
                             `creator_id` BIGINT(20) DEFAULT NULL COMMENT '创建人',
                             `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `updater_id` BIGINT(20) DEFAULT NULL COMMENT '更新人',
                             `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';


CREATE TABLE center_file_tags (
                                  `id` BIGINT(20) NOT NULL COMMENT '唯一标识',
                                  `file_id` BIGINT(20) NOT NULL COMMENT '文件ID',
                                  `tag_id` BIGINT(20) NOT NULL COMMENT '标签ID',
                                  `tenant_id` BIGINT(20) DEFAULT NULL COMMENT '租户号',
                                  `creator_id` BIGINT(20) DEFAULT NULL COMMENT '创建人',
                                  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updater_id` BIGINT(20) DEFAULT NULL COMMENT '更新人',
                                  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件标签关联表';


CREATE TABLE center_knowledge_operation_logs (
                                                 `id` BIGINT(20) NOT NULL COMMENT '唯一标识',
                                                 `knowledge_id` BIGINT(20) DEFAULT NULL COMMENT '知识ID',
                                                 `operation_type` VARCHAR(50) DEFAULT NULL COMMENT '操作类型; 如：create、modify、submit_approval、approve、reject、delete',
                                                 `operation_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                                                 `operation_result` TEXT DEFAULT NULL COMMENT '操作结果',
                                                 `reason` VARCHAR(100) DEFAULT NULL COMMENT '审批理由（审批通过时为空）',
                                                 `remark` VARCHAR(100) DEFAULT NULL COMMENT '备注',
                                                 `tenant_id` BIGINT(20) DEFAULT NULL COMMENT '租户号',
                                                 `creator_id` BIGINT(20) DEFAULT NULL COMMENT '创建人',
                                                 `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `updater_id` BIGINT(20) DEFAULT NULL COMMENT '更新人',
                                                 `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识操作日志表';
