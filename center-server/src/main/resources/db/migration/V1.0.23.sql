CREATE TABLE center_pdf_message (
             `id` BIGINT(20) NOT NULL COMMENT '主键',
             `job_id` BIGINT(20) NOT NULL COMMENT '任务id',
             `keyword` VARCHAR(100) NOT NULL COMMENT '关键词',
             `value` VARCHAR(100) NULL COMMENT '查询结果',
             `start_id` VARCHAR(100) NULL COMMENT '开始位置',
             `end_id` VARCHAR(100) NULL COMMENT '结束位置',
             `tenant_id` BIGINT(20) NOT NULL COMMENT '租户ID',
             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
             `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
             `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
             PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='pdf数据表';

CREATE TABLE center_pdf_job (
            `id` BIGINT(20) NOT NULL COMMENT '主键',
            `pdf_url` varchar(100) NOT NULL COMMENT 'pdf的url',
            `status` varchar(10) NOT NULL COMMENT '状态',
            `tenant_id` BIGINT(20) NOT NULL COMMENT '租户ID',
            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
            `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
            `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
            PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='pdf任务表';