-- 1. API Keys主表
CREATE TABLE api_keys
(
    id          BIGINT(20) NOT NULL COMMENT 'API Key主键',
    api_key_name  VARCHAR(100) NOT NULL COMMENT 'API Key名称',
    model_id  BIGINT(20) NULL COMMENT '关联模型Id',
    api_key  VARCHAR(100) NOT NULL COMMENT 'API Key值',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'开始时间',
    end_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'结束时间',
    last_used_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'最后使用时间',
    tenant_id   BIGINT(20) NULL COMMENT '租户号',
    creator_id  BIGINT(20) NULL COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT'创建时间',
    updater_id  BIGINT(20) NULL COMMENT '更新人ID',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB
  DEFAULT CHARSET=utf8mb4
  COLLATE=utf8mb4_unicode_ci
  COMMENT='API Keys 表';
