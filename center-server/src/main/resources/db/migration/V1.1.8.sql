-- 为AI搜索模块表添加缺失的tenant_id字段
-- V1.1.7 - 修复AI搜索表结构

-- 为ai_search_message表添加tenant_id字段
ALTER TABLE `ai_search_message` 
ADD COLUMN `tenant_id` bigint(20) NOT NULL COMMENT '租户ID' AFTER `session_id`;

-- 为ai_search_event表添加tenant_id字段
ALTER TABLE `ai_search_event` 
ADD COLUMN `tenant_id` bigint(20) NOT NULL COMMENT '租户ID' AFTER `session_id`;

-- 修改ai_search_message表的thumbs_up字段类型，从bit(1)改为tinyint
ALTER TABLE `ai_search_message` 
MODIFY COLUMN `thumbs_up` tinyint DEFAULT NULL COMMENT '点赞状态：1-点赞，-1-点踩，null-未操作（仅assistant角色时有值）';

-- 为ai_search_event表添加department_id字段以支持完整的BaseTenantModel
ALTER TABLE `ai_search_event` 
ADD COLUMN `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID' AFTER `tenant_id`; 