CREATE TABLE center_knowledge_bases (
                                        `id` BIGINT(20) NOT NULL COMMENT '主键',
                                        `department_id` BIGINT(20) NULL COMMENT '所属部门ID',
                                        `kb_name` VARCHAR(100) NOT NULL COMMENT '知识库名称',
                                        `ai_fileb_id` VARCHAR(255) NULL COMMENT '模型文件库ID',
                                        `ai_faqb_id` VARCHAR(255) NULL COMMENT '模型知识库ID',
                                        `tenant_id` BIGINT(32) NULL COMMENT '租户号',
                                        `creator_id` BIGINT(32) NULL COMMENT '创建人ID',
                                        `create_time` DATETIME DEFAULT NULL COMMENT '创建时间',
                                        `updater_id` BIGINT(32) NULL COMMENT '更新人ID',
                                        `update_time` DATETIME DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='知识库表';
