CREATE TABLE center_files(
    `id` BIGINT(20) NOT NULL COMMENT '主键',
    `kb_id` BIGINT(20) NOT NULL COMMENT '知识库ID',
    `ai_fileb_file_id` BIGINT(20) NULL COMMENT '模型文件库的文件id',
    `file_name` varchar(100) NOT NULL COMMENT '文件名称',
    `original_name` varchar(100) NOT NULL COMMENT '原始文件名称',
    `hdfs_path` varchar(255) NULL COMMENT 'hdfs路径',
    `status` varchar(50) NOT NULL COMMENT '文件状态',
    `file_num` INT NULL COMMENT '文件重名序号',
    `is_deleted` INT NULL COMMENT '删除标识',
    `tenant_id` BIGINT(20) NULL COMMENT '租户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='文件表';