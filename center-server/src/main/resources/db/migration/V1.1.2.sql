CREATE TABLE center_datasource (
    `id` BIGINT(20) NOT NULL COMMENT '主键',
    `datasource_name` varchar(100) NOT NULL COMMENT '数据库名称',
    `datasource_description` varchar(200) NULL COMMENT '数据库描述说明',
    `larger_model_id` BIGINT NOT NULL COMMENT '关联的大模型',
    `datasource_type` varchar(100) NOT NULL COMMENT '数据库类型（SINGLE/MULTIPLE）',
    `tenant_id` BIGINT(20) NOT NULL COMMENT '租户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='数据库基础表';

CREATE TABLE center_datasource_extension (
    `id` BIGINT(20) NOT NULL COMMENT '主键',
    `datasource_id` BIGINT(20) NOT NULL COMMENT '数据库基础表ID',
    `category` varchar(100)  NOT NULL COMMENT '数据源类型',
    `ip` varchar(100)  NOT NULL COMMENT '数据库地址',
    `port` int NOT NULL COMMENT '端口',
    `database_name` varchar(100)  DEFAULT NULL COMMENT '数据库名',
    `user_name` varchar(100)  DEFAULT NULL COMMENT '数据库用户名',
    `password` varchar(100)  DEFAULT NULL COMMENT '数据库密码',
    `jdbc_parameter` varchar(100)  DEFAULT NULL COMMENT 'jdbc参数',
    `table_name` varchar(50)  DEFAULT NULL COMMENT '表名称',
    `table_relation` varchar(500)  DEFAULT NULL COMMENT '表关系定义',
    `table_description` varchar(500)  DEFAULT NULL COMMENT '表结构说明',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='数据库和表扩展信息表';