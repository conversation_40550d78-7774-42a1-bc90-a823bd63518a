-- 创建新的事件详情表
CREATE TABLE center_chat_answer_event (
                                          id BIGINT NOT NULL COMMENT '主键ID',
                                          answer_id BIGINT NOT NULL COMMENT '回答ID',
                                          event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
                                          event_content TEXT COMMENT '事件内容',
                                          sequence_order INT NOT NULL COMMENT '事件顺序',
                                          include_in_context TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否参与上下文',
                                          event_status VARCHAR(20) COMMENT '事件状态',
                                          tenant_id BIGINT COMMENT '租户ID',
                                          creator_id BIGINT COMMENT '创建人ID',
                                          create_time DATETIME COMMENT '创建时间',
                                          updater_id BIGINT COMMENT '更新人ID',
                                          update_time DATETIME COMMENT '更新时间',
                                          PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天回答事件详情表';
