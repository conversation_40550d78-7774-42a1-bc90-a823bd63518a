CREATE TABLE center_robot_knowledge_bases
(
    `id`          BIGINT(20) NOT NULL COMMENT '唯一标识',
    `robot_id`        BIGINT(20) NOT NULL COMMENT '机器人ID',
    `kb_id`   BIGINT(20) NOT NULL COMMENT '知识库ID',
    `tenant_id`   BIGINT(20) NOT NULL COMMENT '租户ID',
    `is_deleted` INT COMMENT '删除标示',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id`  bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id`  bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='机器人知识库关联表';