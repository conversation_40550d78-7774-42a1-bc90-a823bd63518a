CREATE TABLE center_depart (
    `id` BIGINT(20) NOT NULL COMMENT '主键',
    `name` varchar(100) NOT NULL COMMENT '部门名称',
    `parent_id` BIGINT NULL COMMENT '上级部门ID',
    `sort` INT NULL COMMENT '显示顺序',
    `tenant_id` BIGINT(20) NOT NULL COMMENT '租户ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
)
 ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_unicode_ci
COMMENT='部门表';